#!/usr/bin/env python3
"""
簡化的依賴注入驗證腳本
專門檢查 staging_routes.py 和 processing_routes.py
"""

import re
from pathlib import Path


def validate_file(file_path: Path) -> dict:
    """驗證單個文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有端點
    endpoint_pattern = r'@router\.(get|post|put|delete|patch)\("([^"]+)"\)'
    endpoints = re.findall(endpoint_pattern, content)
    
    # 檢查舊模式
    old_patterns = [
        r'get_file_staging_service\(\)',
        r'get_file_processing_service\(\)',
        r'if.*get_file.*service.*is None',
        r'staging_service.*is None',
        r'processing_service.*is None'
    ]
    
    old_issues = []
    for pattern in old_patterns:
        matches = re.findall(pattern, content)
        if matches:
            old_issues.extend(matches)
    
    # 檢查依賴注入使用
    di_patterns = [
        r'Depends\(require_staging_service\)',
        r'Depends\(require_processing_service\)',
        r'Depends\(get_api_state\)'
    ]
    
    di_count = 0
    for pattern in di_patterns:
        di_count += len(re.findall(pattern, content))
    
    # 檢查請求追蹤
    request_tracking_count = len(re.findall(r'api_state\.increment_request_count\(\)', content))
    error_tracking_count = len(re.findall(r'api_state\.increment_error_count\(\)', content))
    
    return {
        'file': file_path.name,
        'total_endpoints': len(endpoints),
        'endpoints': endpoints,
        'old_issues': old_issues,
        'dependency_injection_count': di_count,
        'request_tracking_count': request_tracking_count,
        'error_tracking_count': error_tracking_count,
        'is_fully_refactored': len(old_issues) == 0 and di_count > 0
    }


def main():
    """主函數"""
    project_root = Path.cwd()
    api_dir = project_root / "src" / "presentation" / "api"
    
    files_to_check = [
        api_dir / "staging_routes.py",
        api_dir / "processing_routes.py"
    ]
    
    print("🔍 驗證依賴注入重構狀況...")
    print("=" * 60)
    
    total_endpoints = 0
    total_refactored = 0
    all_results = []
    
    for file_path in files_to_check:
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        result = validate_file(file_path)
        all_results.append(result)
        
        print(f"\n📁 {result['file']}")
        print(f"   端點總數: {result['total_endpoints']}")
        print(f"   依賴注入使用: {result['dependency_injection_count']} 次")
        print(f"   請求追蹤: {result['request_tracking_count']} 次")
        print(f"   錯誤追蹤: {result['error_tracking_count']} 次")
        
        if result['old_issues']:
            print(f"   ❌ 舊模式殘留: {len(result['old_issues'])} 個")
            for issue in result['old_issues'][:3]:  # 只顯示前3個
                print(f"      - {issue}")
        else:
            print(f"   ✅ 無舊模式殘留")
        
        if result['is_fully_refactored']:
            print(f"   ✅ 完全重構")
            total_refactored += result['total_endpoints']
        else:
            print(f"   ❌ 需要進一步重構")
        
        total_endpoints += result['total_endpoints']
        
        print(f"   端點列表:")
        for method, path in result['endpoints']:
            print(f"      - {method.upper()} {path}")
    
    print("\n" + "=" * 60)
    print("📊 總體統計:")
    print(f"   總端點數: {total_endpoints}")
    print(f"   完全重構端點: {total_refactored}")
    print(f"   重構完成率: {(total_refactored/total_endpoints*100):.1f}%" if total_endpoints > 0 else "0%")
    
    if total_refactored == total_endpoints:
        print("✅ 所有端點都已完全重構！")
        return 0
    else:
        print("❌ 仍有端點需要重構")
        return 1


if __name__ == "__main__":
    exit(main())
