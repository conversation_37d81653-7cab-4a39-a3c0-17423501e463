"""統一監控儀表板配置模組

提供完整的配置管理功能：
- 主要配置管理 (dashboard_config.py)
- 監控規則管理 (dashboard_monitoring_rules.py)
- 環境變數覆蓋機制
- 配置驗證和預設值系統
"""

from .dashboard_config import (
    DashboardConfig,
    AlertThresholds,
    UpdateIntervals,
    RetentionPolicies,
    WebSocketConfig,
    DisplayConfig,
    NotificationConfig,
    DatabaseConfig,
    SecurityConfig,
    AlertLevel,
    NotificationChannel,
    get_dashboard_config,
    init_dashboard_config,
    reload_dashboard_config
)

from .dashboard_monitoring_rules import (
    MonitoringRulesManager,
    MonitoringRule,
    AlertCondition,
    ConditionType,
    RuleCategory,
    RuleStatus,
    get_monitoring_rules_manager,
    init_monitoring_rules_manager,
    reload_monitoring_rules
)

__all__ = [
    # 主要配置類別
    "DashboardConfig",
    "AlertThresholds",
    "UpdateIntervals",
    "RetentionPolicies",
    "WebSocketConfig",
    "DisplayConfig",
    "NotificationConfig",
    "DatabaseConfig",
    "SecurityConfig",
    
    # 枚舉類型
    "AlertLevel",
    "NotificationChannel",
    "ConditionType",
    "RuleCategory",
    "RuleStatus",
    
    # 監控規則類別
    "MonitoringRulesManager",
    "MonitoringRule",
    "AlertCondition",
    
    # 工廠函數
    "get_dashboard_config",
    "init_dashboard_config",
    "reload_dashboard_config",
    "get_monitoring_rules_manager",
    "init_monitoring_rules_manager",
    "reload_monitoring_rules"
]