"""錯誤處理模組
提供統一的錯誤處理功能
"""

import uuid
import time
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from functools import wraps

from .errors import *


def create_unified_error_response(
    error_code: str,
    message: str,
    status_code: int,
    details: Optional[str] = None,
    path: Optional[str] = None,
    trace_id: Optional[str] = None
) -> Dict[str, Any]:
    """建立統一錯誤響應
    
    Args:
        error_code: 錯誤代碼
        message: 錯誤訊息
        status_code: HTTP狀態碼
        details: 詳細信息（可選）
        path: 請求路徑（可選）
        trace_id: 追蹤ID（可選）
        
    Returns:
        統一格式的錯誤響應
    """
    return {
        "success": False,
        "error": {
            "code": error_code,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat() + "Z",
            "trace_id": trace_id or str(uuid.uuid4()),
            "path": path
        },
        "status_code": status_code
    }


def with_retry(
    func: Callable,
    max_retries: int = 3,
    backoff_factor: float = 1.0
):
    """重試裝飾器
    
    Args:
        func: 要重試的函數
        max_retries: 最大重試次數
        backoff_factor: 退避係數
        
    Returns:
        函數結果
    """
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries:
                raise e
            time.sleep(backoff_factor * (2 ** attempt))


class CircuitBreaker:
    """熔斷器實現"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs):
        """調用函數，使用熔斷器模式"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
            else:
                raise Exception("熔斷器開啟，服務不可用")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _on_success(self):
        """成功時重置狀態"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """失敗時更新狀態"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class ErrorLogger:
    """錯誤日誌記錄器"""
    
    def __init__(self):
        self.sensitive_fields = ["password", "api_key", "token", "secret", "credit_card"]
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """記錄錯誤

        Args:
            error: 錯誤實例
            context: 上下文信息
        """
        from src.presentation.api.dependencies import logger

        filtered_context = self.filter_sensitive_data(context or {})

        # 使用實際的日誌系統
        logger.error(
            f"API 錯誤: {str(error)}",
            extra={
                "error_type": type(error).__name__,
                "context": filtered_context
            }
        )

        # 為了測試目的，保留 print 輸出
        print(f"錯誤記錄: {error}")
        print(f"上下文: {filtered_context}")
    
    def filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """過濾敏感數據
        
        Args:
            data: 原始數據
            
        Returns:
            過濾後的數據
        """
        filtered = data.copy()
        
        for key in filtered:
            if any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                filtered[key] = "***"
        
        return filtered