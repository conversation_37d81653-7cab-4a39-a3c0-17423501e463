"""網路瀏覽器 API - 暫存相關路由
處理檔案暫存任務的建立、執行、狀態查詢和清理功能
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Query, HTTPException, Depends
from fastapi.responses import JSONResponse

from loguru import logger

try:
    from ...services.staging.models import StagingError, InsufficientSpaceError, FileIntegrityError
    from ...services.staging import FileStagingService
except ImportError:
    # 當直接執行時的回退
    StagingError = Exception
    InsufficientSpaceError = Exception
    FileIntegrityError = Exception
    FileStagingService = None

# 導入依賴注入
from .dependencies import get_staging_service, get_api_state, require_staging_service, APIState

# 建立路由器
router = APIRouter(prefix="/api/staging", tags=["File Staging"])


@router.post("/create")
async def create_staging_task(
    product_name: str = Query(..., description="產品名稱"),
    source_files: List[str] = Query(..., description="來源檔案路徑列表"),
    preserve_structure: bool = Query(default=True, description="是否保持目錄結構"),
    use_unique_name: bool = Query(default=True, description="是否使用唯一產品名稱"),
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """建立檔案暫存任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 直接使用注入的服務，無需 None 檢查

        logger.info(f"建立暫存任務請求: 產品={product_name}, 檔案數={len(source_files)}")

        # 驗證輸入參數
        if not product_name.strip():
            raise HTTPException(status_code=400, detail="產品名稱不能為空")

        if not source_files:
            raise HTTPException(status_code=400, detail="來源檔案列表不能為空")
        
        # 建立暫存任務
        task_id = staging_service.create_staging_task(
            product_name=product_name,
            source_files=source_files,
            preserve_structure=preserve_structure,
            use_unique_name=use_unique_name
        )
        
        logger.info(f"暫存任務建立成功: {task_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": f"暫存任務建立成功",
            "product_name": product_name,
            "source_files_count": len(source_files),
            "preserve_structure": preserve_structure,
            "use_unique_name": use_unique_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"建立暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"建立暫存任務時發生錯誤: {str(e)}")


@router.post("/execute/{task_id}")
async def execute_staging_task(
    task_id: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """執行檔案暫存任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 驗證 task_id 格式
        try:
            uuid.UUID(task_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="無效的任務ID格式")

        logger.info(f"執行暫存任務: {task_id}")

        # 直接使用注入的服務，無需 None 檢查
        
        # 檢查任務是否存在
        task = staging_service.get_task_status(task_id)
        if task is None:
            raise HTTPException(status_code=404, detail=f"找不到任務: {task_id}")
        
        # 執行暫存任務
        result = await staging_service.execute_staging_task(task_id)
        
        if result.success:
            logger.info(f"暫存任務執行成功: {task_id}")
            return {
                "success": True,
                "task_id": task_id,
                "message": "暫存任務執行成功",
                "staging_directory": str(result.staging_directory),
                "staged_files": [str(f) for f in result.staged_files],
                "total_files": result.total_files,
                "total_size": result.total_size,
                "staging_duration": result.staging_duration,
                "average_speed": result.average_speed,
                "integrity_check_passed": result.integrity_check_passed
            }
        else:
            logger.error(f"暫存任務執行失敗: {task_id}, 錯誤: {result.error_message}")
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "task_id": task_id,
                    "message": "暫存任務執行失敗",
                    "error_message": result.error_message,
                    "failed_files": result.failed_files
                }
            )
        
    except HTTPException:
        raise
    except (InsufficientSpaceError, FileIntegrityError) as e:
        api_state.increment_error_count()
        logger.error(f"暫存任務執行失敗 (業務邏輯錯誤): {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))  # Unprocessable Entity
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"執行暫存任務時發生錯誤: {str(e)}")
        raise HTTPException(status_code=500, detail=f"執行暫存任務時發生錯誤: {str(e)}")


@router.get("/status/{task_id}")
async def get_staging_task_status(
    task_id: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """取得暫存任務狀態（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 取得任務進度
        progress = staging_service.get_task_progress(task_id)

        if "error" in progress:
            raise HTTPException(status_code=404, detail=progress["error"])

        return progress

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"取得暫存任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取得任務狀態時發生錯誤: {str(e)}")


@router.delete("/cleanup/{task_id}")
async def cleanup_staging_directory(
    task_id: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """清理暫存目錄（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        success = await staging_service.cleanup_staging_directory(task_id)

        return {"success": success, "task_id": task_id, "message": "暫存目錄清理完成" if success else "暫存目錄清理失敗"}

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"清理暫存目錄失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理暫存目錄時發生錯誤: {str(e)}")


@router.get("/tasks")
async def list_staging_tasks(
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """列出所有暫存任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        tasks = staging_service.list_tasks()

        return {"tasks": tasks, "total_count": len(tasks)}

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"列出暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出暫存任務時發生錯誤: {str(e)}")


@router.get("/statistics")
async def get_staging_statistics(
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """取得暫存服務統計資料（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        stats = staging_service.get_service_statistics()

        return {
            "success": True,
            "statistics": stats,
            "timestamp": "now"  # 可以用 datetime.now().isoformat()
        }

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"取得暫存統計資料失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取得統計資料時發生錯誤: {str(e)}")


@router.post("/cancel/{task_id}")
async def cancel_staging_task(
    task_id: str,
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """取消暫存任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        success = await staging_service.cancel_task(task_id)

        if success:
            return {
                "success": True,
                "task_id": task_id,
                "message": "暫存任務已取消"
            }
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "task_id": task_id,
                    "message": "無法取消任務（任務可能不存在或已完成）"
                }
            )

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"取消暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任務時發生錯誤: {str(e)}")


@router.post("/cleanup-completed")
async def cleanup_completed_tasks(
    max_age_hours: int = Query(default=24, description="清理多少小時前的已完成任務"),
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """清理已完成的舊任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        cleaned_count = staging_service.cleanup_completed_tasks(max_age_hours)

        return {
            "success": True,
            "cleaned_tasks_count": cleaned_count,
            "max_age_hours": max_age_hours,
            "message": f"已清理 {cleaned_count} 個舊任務"
        }

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"清理已完成任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理任務時發生錯誤: {str(e)}")
