# 統一監控儀表板 API 文檔

## 概述

統一監控儀表板提供完整的 REST API 和 WebSocket API，用於監控半導體郵件處理基礎設施。API 設計遵循 RESTful 原則，支援即時資料更新和快取管理。

## 基本資訊

- **基礎 URL**: `http://localhost:8000`
- **API 版本**: v1.0
- **內容類型**: `application/json`
- **認證方式**: Bearer <PERSON>ken (可選)

## 回應格式

### 成功回應
```json
{
  "status": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 錯誤回應
```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "錯誤描述",
    "details": {}
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 監控 API 端點

### 儀表板主要端點

#### 主儀表板頁面
```http
GET /dashboard
```

**狀態**: ✅ **已實現並可用**

**描述**: 返回統一監控儀表板的主頁面，包含完整的響應式 UI 和即時監控功能。

**功能特色**:
- ✅ 響應式佈局設計，支援桌面、平板、手機
- ✅ 即時資料更新（WebSocket 連接）
- ✅ 六大監控區域：郵件處理、Celery 任務、系統資源、檔案處理、業務指標、告警管理
- ✅ 互動式 UI 元件：刷新按鈕、面板展開/收縮、告警確認
- ✅ 深色模式支援和列印樣式

**回應**: HTML 頁面

#### 獲取儀表板狀態
```http
GET /dashboard/api/status
```

**狀態**: ✅ **已實現並可用**

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "metrics": {
      "email_metrics": {...},
      "celery_metrics": {...},
      "system_metrics": {...},
      "file_metrics": {...},
      "business_metrics": {...}
    },
    "active_alerts": [...],
    "websocket_connections": 5,
    "system_status": "running"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取當前指標
```http
GET /dashboard/api/metrics/current
```

**狀態**: ✅ **已實現並可用**

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "email": {
      "pending": 10,
      "processing": 5,
      "completed": 100,
      "failed": 2,
      "vendor_counts": {
        "GTK": 5,
        "JCET": 3,
        "ETD": 2
      }
    },
    "celery": {
      "total_active": 8,
      "total_pending": 15,
      "task_type_counts": {
        "code_comparison": {"active": 3, "pending": 5},
        "csv_to_summary": {"active": 2, "pending": 3}
      }
    },
    "system": {
      "cpu_percent": 45.2,
      "memory_percent": 68.5,
      "disk_percent": 32.1
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取儀表板配置
```http
GET /dashboard/api/config
```

**狀態**: ✅ **已實現並可用**

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "metrics_update_interval": 30,
    "alerts_check_interval": 10,
    "default_time_range": "1h",
    "alert_thresholds": {
      "email_queue": {
        "pending_warning": 10,
        "pending_critical": 50
      },
      "celery_tasks": {
        "pending_warning": 20,
        "pending_critical": 100
      },
      "system_resources": {
        "cpu_warning": 80,
        "cpu_critical": 95
      }
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取儀表板資料
```http
GET /api/monitoring/dashboard
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "metrics": {
      "email_metrics": {
        "pending_count": 10,
        "processing_count": 5,
        "completed_count": 100,
        "failed_count": 2,
        "vendor_queue_counts": {
          "GTK": 5,
          "JCET": 3,
          "ETD": 2
        },
        "code_comparison_active": 2
      },
      "celery_metrics": {
        "total_active": 8,
        "total_pending": 15,
        "task_type_counts": {
          "code_comparison": {"active": 3, "pending": 5},
          "csv_to_summary": {"active": 2, "pending": 3}
        }
      },
      "system_metrics": {
        "cpu_percent": 45.2,
        "memory_percent": 68.5,
        "disk_percent": 32.1
      }
    },
    "update_interval": 30
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 系統健康檢查
```http
GET /api/monitoring/health
```

**回應範例：**
```json
{
  "status": "healthy",
  "services": {
    "email_service": "healthy",
    "celery_service": "healthy",
    "database": "healthy",
    "cache_service": "healthy",
    "overall": true
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 郵件監控端點

#### 獲取郵件佇列狀態
```http
GET /api/monitoring/email/queue
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "pending_count": 10,
    "processing_count": 5,
    "completed_count": 100,
    "failed_count": 2,
    "vendor_counts": {
      "GTK": 5,
      "JCET": 3,
      "ETD": 2
    },
    "code_comparison_active": 2
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取郵件處理效能
```http
GET /api/monitoring/email/performance?time_range=1h
```

**查詢參數：**
- `time_range`: 時間範圍 (`1h`, `6h`, `24h`, `7d`)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "avg_processing_time": 30.5,
    "throughput_per_hour": 120.0,
    "vendor_success_rates": {
      "GTK": 0.95,
      "JCET": 0.88,
      "ETD": 0.92
    },
    "time_range": "1h"
  }
}
```

### Celery 任務監控端點

#### 獲取 Celery 任務狀態
```http
GET /api/monitoring/celery/tasks
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "total_active": 8,
    "total_pending": 15,
    "total_completed": 1250,
    "total_failed": 25,
    "task_type_counts": {
      "search_product": {
        "active": 2,
        "pending": 4,
        "completed": 450,
        "failed": 8
      },
      "code_comparison": {
        "active": 3,
        "pending": 5,
        "completed": 500,
        "failed": 10
      },
      "csv_summary": {
        "active": 2,
        "pending": 3,
        "completed": 300,
        "failed": 5
      },
      "health_check": {
        "active": 1,
        "pending": 3,
        "completed": 200,
        "failed": 2
      }
    },
    "worker_status": {
      "worker1": "online",
      "worker2": "online",
      "worker3": "offline"
    },
    "worker_load": {
      "worker1": 3,
      "worker2": 2,
      "worker3": 0
    },
    "avg_task_duration": {
      "search_product": 120.5,
      "code_comparison": 45.2,
      "csv_summary": 25.8,
      "health_check": 5.1
    },
    "task_success_rate": {
      "search_product": 0.982,
      "code_comparison": 0.980,
      "csv_summary": 0.984,
      "health_check": 0.990
    },
    "long_running_tasks": [
      {
        "task_id": "abc123",
        "name": "search_product",
        "worker": "worker1",
        "duration": 1850.5,
        "started_at": "2024-01-01T11:30:00.000Z"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取工作者狀態
```http
GET /api/monitoring/celery/workers
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "worker_status": {
      "worker1": "online",
      "worker2": "online",
      "worker3": "offline"
    },
    "worker_load": {
      "worker1": 3,
      "worker2": 2,
      "worker3": 0
    },
    "total_workers": 3,
    "online_workers": 2,
    "offline_workers": 1
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取 Celery 收集器健康狀態
```http
GET /api/monitoring/celery/health
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "collector_name": "celery_collector",
    "status": "healthy",
    "celery_connected": true,
    "last_check": "2024-01-01T12:00:00.000Z",
    "supported_task_types": [
      "search_product",
      "csv_summary", 
      "code_comparison",
      "health_check"
    ],
    "connection_test_duration_ms": 150.2
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取任務類型詳細統計
```http
GET /api/monitoring/celery/task-types/{task_type}
```

**路徑參數：**
- `task_type`: 任務類型 (`search_product`, `csv_summary`, `code_comparison`, `health_check`)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "task_type": "search_product",
    "current_stats": {
      "active": 2,
      "pending": 4,
      "completed": 450,
      "failed": 8
    },
    "performance_metrics": {
      "avg_duration_seconds": 120.5,
      "success_rate": 0.982,
      "throughput_per_hour": 45.2
    },
    "recent_failures": [
      {
        "task_id": "failed_task_123",
        "failed_at": "2024-01-01T11:45:00.000Z",
        "error_message": "Connection timeout",
        "retry_count": 3
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取長時間運行任務
```http
GET /api/monitoring/celery/long-running?threshold=1800
```

**查詢參數：**
- `threshold`: 時間閾值（秒），預設 1800（30分鐘）

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "long_running_tasks": [
      {
        "task_id": "abc123",
        "name": "search_product",
        "worker": "worker1",
        "duration": 1850.5,
        "started_at": "2024-01-01T11:30:00.000Z",
        "estimated_remaining": 300.0
      },
      {
        "task_id": "def456",
        "name": "code_comparison",
        "worker": "worker2",
        "duration": 2100.8,
        "started_at": "2024-01-01T11:25:00.000Z",
        "estimated_remaining": null
      }
    ],
    "total_count": 2,
    "threshold_seconds": 1800
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 系統監控端點

#### 獲取系統資源狀態
```http
GET /api/monitoring/system/resources
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "cpu_percent": 45.2,
    "memory_percent": 68.5,
    "disk_percent": 32.1,
    "active_connections": 25,
    "service_health": {
      "email_service": "healthy",
      "celery_service": "healthy",
      "database": "healthy"
    }
  }
}
```

### 告警管理端點

#### 獲取活躍告警
```http
GET /api/monitoring/alerts?level=critical
```

**查詢參數：**
- `level`: 告警級別篩選 (`info`, `warning`, `error`, `critical`)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "alert_type": "queue_overflow",
        "level": "warning",
        "title": "郵件佇列過多",
        "message": "待處理郵件數量超過 10 個",
        "source": "email_monitor",
        "triggered_at": "2024-01-01T12:00:00.000Z",
        "status": "active"
      }
    ],
    "total_count": 1,
    "critical_count": 0,
    "warning_count": 1
  }
}
```

#### 確認告警
```http
POST /api/monitoring/alerts/{alert_id}/acknowledge
```

**路徑參數：**
- `alert_id`: 告警 ID

**回應範例：**
```json
{
  "status": "success",
  "message": "告警 alert_123 已確認"
}
```

### 趨勢分析端點

#### 獲取趨勢資料
```http
GET /api/monitoring/trends/{metric_type}?time_range=24h
```

**路徑參數：**
- `metric_type`: 指標類型 (`email_queue`, `celery_tasks`, `system_resources`)

**查詢參數：**
- `time_range`: 時間範圍 (`1h`, `6h`, `24h`, `7d`, `30d`)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "trend_points": [
      {
        "timestamp": "2024-01-01T10:00:00.000Z",
        "value": 100
      },
      {
        "timestamp": "2024-01-01T11:00:00.000Z",
        "value": 120
      }
    ],
    "summary": {
      "avg_value": 110,
      "min_value": 85,
      "max_value": 150,
      "trend_direction": "increasing"
    }
  }
}
```

#### 獲取負載預測
```http
GET /api/monitoring/predictions/load?hours_ahead=24
```

**查詢參數：**
- `hours_ahead`: 預測小時數 (預設: 24)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "predictions": [
      {
        "timestamp": "2024-01-02T12:00:00.000Z",
        "predicted_load": 85.2,
        "confidence": 0.85
      }
    ],
    "model_accuracy": 0.92,
    "prediction_horizon": 24
  }
}
```

### 歷史資料端點

#### 獲取歷史指標
```http
GET /api/monitoring/history/metrics?metric_type=email_queue&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z
```

**查詢參數：**
- `metric_type`: 指標類型
- `start_time`: 開始時間 (ISO 8601 格式)
- `end_time`: 結束時間 (ISO 8601 格式)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "metrics": [
      {
        "timestamp": "2024-01-01T10:00:00.000Z",
        "metric_name": "pending_count",
        "metric_value": 10,
        "tags": {"vendor": "GTK"}
      }
    ],
    "count": 1440,
    "time_range": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-01T23:59:59.000Z"
    }
  }
}
```

### 統計資料端點

#### 獲取統計摘要
```http
GET /api/monitoring/statistics/summary
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "email_summary": {
      "total_pending": 10,
      "processing_rate": 120.0
    },
    "celery_summary": {
      "total_active": 8,
      "total_pending": 15
    },
    "system_summary": {
      "cpu_usage": 45.2,
      "memory_usage": 68.5
    },
    "alerts_summary": {
      "total_alerts": 3,
      "critical_alerts": 1
    }
  }
}
```

## 快取管理 API 端點

### 快取狀態

#### 獲取快取服務狀態
```http
GET /api/monitoring/cache/status
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "service_status": "running",
    "statistics": {
      "total_entries": 150,
      "total_size_mb": 12.5,
      "hit_rate": 85.2,
      "memory_usage_mb": 45.8
    },
    "health": {
      "status": "excellent",
      "health_score": 95,
      "recommendations": []
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取快取統計資料
```http
GET /api/monitoring/cache/statistics
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "statistics": {
      "total_entries": 150,
      "total_size_mb": 12.5,
      "hit_rate": 85.2,
      "hit_count": 1250,
      "miss_count": 220,
      "eviction_count": 15,
      "expired_count": 45,
      "avg_access_time_ms": 2.5
    },
    "configuration": {
      "max_size": 1000,
      "default_ttl": 300,
      "max_memory_mb": 256,
      "cleanup_interval": 60
    },
    "recent_entries": [
      {
        "key": "metrics:email:current",
        "size_bytes": 1024,
        "access_count": 25,
        "last_accessed": "2024-01-01T12:00:00.000Z"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取快取健康狀態
```http
GET /api/monitoring/cache/health
```

**回應範例：**
```json
{
  "status": "excellent",
  "health_score": 95,
  "statistics": {
    "hit_rate": 85.2,
    "memory_usage_mb": 45.8,
    "avg_access_time_ms": 2.5
  },
  "recommendations": [
    "快取運行狀況良好",
    "建議定期監控記憶體使用情況"
  ]
}
```

### 快取操作

#### 獲取快取鍵列表
```http
GET /api/monitoring/cache/keys?pattern=metrics:*&limit=100
```

**查詢參數：**
- `pattern`: 鍵模式過濾 (可選)
- `limit`: 返回數量限制 (預設: 100)

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "keys": [
      "metrics:email:current",
      "metrics:celery:current",
      "metrics:system:current"
    ],
    "total_count": 3,
    "truncated": false,
    "pattern": "metrics:*"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 獲取特定快取項目
```http
GET /api/monitoring/cache/entry/{key}
```

**路徑參數：**
- `key`: 快取鍵

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "key": "metrics:email:current",
    "value": {
      "pending_count": 10,
      "processing_count": 5
    },
    "exists": true,
    "entry_info": {
      "size_bytes": 1024,
      "access_count": 25,
      "created_at": "2024-01-01T11:30:00.000Z",
      "last_accessed": "2024-01-01T12:00:00.000Z",
      "expires_at": "2024-01-01T12:05:00.000Z",
      "is_expired": false
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 設定快取項目
```http
POST /api/monitoring/cache/set
Content-Type: application/json

{
  "key": "test_key",
  "value": {"data": "test_value"},
  "ttl": 300
}
```

**請求體：**
- `key`: 快取鍵 (必需)
- `value`: 快取值 (必需)
- `ttl`: 存活時間，秒 (可選)

**回應範例：**
```json
{
  "status": "success",
  "message": "快取項目已設定",
  "data": {
    "key": "test_key",
    "ttl": 300,
    "set_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 刪除快取項目
```http
DELETE /api/monitoring/cache/entry/{key}
```

**路徑參數：**
- `key`: 快取鍵

**回應範例：**
```json
{
  "status": "success",
  "message": "快取項目已刪除",
  "data": {
    "key": "test_key",
    "deleted_at": "2024-01-01T12:00:00.000Z"
  }
}
```

### 快取管理

#### 失效快取模式
```http
POST /api/monitoring/cache/invalidate
Content-Type: application/json

{
  "pattern": "metrics:*"
}
```

**請求體：**
- `pattern`: 快取鍵模式

**回應範例：**
```json
{
  "status": "success",
  "message": "已失效 15 個快取項目",
  "data": {
    "pattern": "metrics:*",
    "invalidated_count": 15,
    "invalidated_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 失效命名空間快取
```http
POST /api/monitoring/cache/invalidate/namespace/{namespace}
```

**路徑參數：**
- `namespace`: 命名空間 (`metrics`, `alerts`, `business`, `system_info`, `config`)

**回應範例：**
```json
{
  "status": "success",
  "message": "已失效命名空間 'metrics' 的 25 個快取項目",
  "data": {
    "namespace": "metrics",
    "invalidated_count": 25,
    "invalidated_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 清空所有快取
```http
DELETE /api/monitoring/cache/clear?confirm=true
```

**查詢參數：**
- `confirm`: 必須設為 `true` 以確認操作

**回應範例：**
```json
{
  "status": "success",
  "message": "已清空所有快取，移除 150 個項目",
  "data": {
    "entries_cleared": 150,
    "cleared_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 最佳化快取記憶體
```http
POST /api/monitoring/cache/optimize
```

**回應範例：**
```json
{
  "status": "success",
  "message": "快取記憶體最佳化完成",
  "data": {
    "before": {
      "total_entries": 150,
      "memory_usage_mb": 65.2
    },
    "after": {
      "total_entries": 120,
      "memory_usage_mb": 45.8
    },
    "optimized_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 預熱快取
```http
POST /api/monitoring/cache/warmup
```

**回應範例：**
```json
{
  "status": "success",
  "message": "快取預熱完成",
  "data": {
    "warmed_up_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 獲取快取配置
```http
GET /api/monitoring/cache/config
```

**回應範例：**
```json
{
  "status": "success",
  "data": {
    "config": {
      "max_size": 1000,
      "default_ttl": 300,
      "cleanup_interval": 60,
      "max_memory_mb": 256,
      "memory_pressure_threshold": 0.8,
      "ttl_by_type": {
        "email_metrics": 30,
        "celery_metrics": 30,
        "system_metrics": 60
      }
    },
    "service_info": {
      "running": true,
      "max_memory_bytes": 268435456
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## WebSocket API

### 連接端點
```
ws://localhost:8000/ws/dashboard/{client_id}
```

**狀態**: ✅ **已實現並可用**

### 主要功能
- ✅ 客戶端連接管理
- ✅ 訂閱和取消訂閱功能
- ✅ 即時資料推送
- ✅ 心跳檢測機制
- ✅ 錯誤處理和重連

### 支援的訊息類型
- `subscribe` - 訂閱監控資料更新
- `unsubscribe` - 取消訂閱
- `metrics_update` - 即時指標更新推送
- `alert` - 告警通知推送
- `system_status` - 系統狀態更新
- `heartbeat` - 心跳檢測
- `connection_info` - 連接資訊
- `error` - 錯誤訊息

### 快速開始範例

#### JavaScript 客戶端
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/dashboard/my-client-id');

ws.onopen = () => {
    // 訂閱所有監控資料
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            types: ['metrics', 'alerts', 'system_status']
        }
    }));
};

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('收到訊息:', message.type, message.payload);
};
```

#### Python 客戶端
```python
import asyncio
import websockets
import json

async def connect_dashboard():
    uri = "ws://localhost:8000/ws/dashboard/python-client"
    async with websockets.connect(uri) as websocket:
        # 訂閱監控資料
        await websocket.send(json.dumps({
            "type": "subscribe",
            "payload": {
                "types": ["metrics", "alerts"]
            }
        }))
        
        # 監聽訊息
        async for message in websocket:
            data = json.loads(message)
            print(f"收到 {data['type']}: {data['payload']}")

asyncio.run(connect_dashboard())
```

### 詳細文檔

完整的 WebSocket API 說明請參考：
- [WebSocket API 指南](./websocket_api.md) - 完整的 API 參考
- [WebSocket API 使用指南](../../docs/websocket-api-guide.md) - 詳細的使用範例和最佳實踐

## 錯誤代碼

### HTTP 狀態碼

- `200 OK`: 請求成功
- `201 Created`: 資源創建成功
- `400 Bad Request`: 請求格式錯誤
- `401 Unauthorized`: 認證失敗
- `403 Forbidden`: 權限不足
- `404 Not Found`: 資源不存在
- `422 Unprocessable Entity`: 請求內容無效
- `429 Too Many Requests`: 請求頻率過高
- `500 Internal Server Error`: 服務器內部錯誤
- `503 Service Unavailable`: 服務暫時不可用

### 業務錯誤代碼

- `CACHE_SERVICE_UNAVAILABLE`: 快取服務不可用
- `MONITORING_SERVICE_UNAVAILABLE`: 監控服務不可用
- `INVALID_TIME_RANGE`: 無效的時間範圍
- `INVALID_METRIC_TYPE`: 無效的指標類型
- `ALERT_NOT_FOUND`: 告警不存在
- `CACHE_KEY_NOT_FOUND`: 快取鍵不存在
- `INVALID_CACHE_PATTERN`: 無效的快取模式

## 速率限制

- **一般 API**: 每分鐘 100 次請求
- **快取 API**: 每分鐘 200 次請求
- **WebSocket**: 每個連接每秒 10 條訊息

## 認證

### Bearer Token 認證 (可選)
```http
Authorization: Bearer <token>
```

### API Key 認證 (可選)
```http
X-API-Key: <api_key>
```

## SDK 和客戶端庫

### JavaScript/TypeScript
```javascript
import { DashboardAPI } from '@dashboard/monitoring-client';

const client = new DashboardAPI({
  baseURL: 'http://localhost:8000',
  apiKey: 'your-api-key'
});

// 獲取儀表板資料
const dashboard = await client.monitoring.getDashboard();

// 獲取快取統計
const cacheStats = await client.cache.getStatistics();
```

### Python
```python
from dashboard_monitoring_client import DashboardClient

client = DashboardClient(
    base_url='http://localhost:8000',
    api_key='your-api-key'
)

# 獲取儀表板資料
dashboard = await client.monitoring.get_dashboard()

# 獲取快取統計
cache_stats = await client.cache.get_statistics()
```

## 版本更新

### v1.0.0 (當前版本)
- 基本監控 API
- 快取管理 API
- WebSocket 即時更新
- 告警管理

### v1.1.0 (計劃中)
- 批量操作 API
- 更多統計指標
- 進階篩選功能
- 資料匯出功能

## 支援和聯繫

- **文檔**: [完整文檔](./README.md)
- **問題回報**: [GitHub Issues](https://github.com/your-org/dashboard-monitoring/issues)
- **技術支援**: <EMAIL>