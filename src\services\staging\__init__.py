"""檔案暫存服務套件
提供檔案暫存功能的完整實作，包含資料模型、工具函數、檔案操作和主要服務類別
"""

from .models import (
    StagingStatus, StagingError, InsufficientSpaceError, FileIntegrityError,
    StagingPermissionError, StagingTimeoutError, StagingConcurrencyError,
    TaskCancellationError, StagingFileInfo, StagingTask, StagingResult
)
from .utils import file_lock, ProgressBatcher, TaskMonitor
from .operations import FileOperations
from .service import FileStagingService

# 全域服務實例
_file_staging_service = None


def get_file_staging_service() -> FileStagingService:
    """取得檔案暫存服務實例（單例模式）"""
    global _file_staging_service
    if _file_staging_service is None:
        _file_staging_service = FileStagingService()
    return _file_staging_service


# 匯出所有公開的類別和函數
__all__ = [
    # 資料模型
    'StagingStatus', 'StagingError', 'InsufficientSpaceError', 'FileIntegrityError',
    'StagingPermissionError', 'StagingTimeoutError', 'StagingConcurrencyError',
    'TaskCancellationError', 'StagingFileInfo', 'StagingTask', 'StagingResult',
    
    # 工具函數和類別
    'file_lock', 'ProgressBatcher', 'TaskMonitor',
    
    # 檔案操作
    'FileOperations',
    
    # 主要服務
    'FileStagingService',
    
    # 單例函數
    'get_file_staging_service'
]
