"""檔案處理服務套件
提供檔案處理功能的完整實作，包含資料模型、執行器和主要服務類別
"""

from .models import (
    ProcessingStatus, ProcessingTool, ProcessingError, ProcessingTimeoutError,
    ProcessingRetryExhaustedError, ProcessingCancellationError, ToolExecutionError,
    RetryConfig, ProcessingTask, ProcessingResult
)
from .executor import TaskTimeout, ToolExecutor
from .service import FileProcessingService

# 全域服務實例
_file_processing_service = None


def get_file_processing_service() -> FileProcessingService:
    """取得檔案處理服務實例（單例模式）"""
    global _file_processing_service
    if _file_processing_service is None:
        _file_processing_service = FileProcessingService()
    return _file_processing_service


# 匯出所有公開的類別和函數
__all__ = [
    # 資料模型
    'ProcessingStatus', 'ProcessingTool', 'ProcessingError', 'ProcessingTimeoutError',
    'ProcessingRetryExhaustedError', 'ProcessingCancellationError', 'ToolExecutionError',
    'RetryConfig', 'ProcessingTask', 'ProcessingResult',
    
    # 執行器
    'TaskTimeout', 'ToolExecutor',
    
    # 主要服務
    'FileProcessingService',
    
    # 單例函數
    'get_file_processing_service'
]
