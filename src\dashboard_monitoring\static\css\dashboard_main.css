/* 統一監控儀表板 - 主要樣式 */

/* 全域重置和基礎樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 顏色變數 */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --critical-color: #c0392b;
    --info-color: #17a2b8;
    
    /* 背景顏色 */
    --bg-primary: #f8f9fa;
    --bg-secondary: #ffffff;
    --bg-dark: #343a40;
    
    /* 文字顏色 */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #ffffff;
    
    /* 邊框和陰影 */
    --border-color: #dee2e6;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* 間距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* 字體大小 */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-md: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-xxl: 1.5rem;
    
    /* 邊框圓角 */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    
    /* 動畫時間 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-md);
}

/* 頂部導航欄 */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.dashboard-title {
    font-size: var(--font-xxl);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.title-icon {
    font-size: 1.8rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background: var(--warning-color);
}

.status-indicator.error {
    background: var(--error-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    font-size: var(--font-sm);
}

.last-update, .connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.connection-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--success-color);
}

.connection-indicator.disconnected {
    background: var(--error-color);
}

/* 告警橫幅 */
.alert-banner {
    background: var(--error-color);
    color: var(--text-light);
    padding: var(--spacing-sm) 0;
    animation: slideDown var(--transition-normal) ease-out;
}

.alert-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-close {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: var(--font-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.alert-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 主要內容區域 */
.dashboard-main {
    padding: var(--spacing-xl) 0;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 概覽統計區域 */
.overview-section {
    margin-bottom: var(--spacing-xxl);
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.overview-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    border-left: 4px solid var(--secondary-color);
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.overview-card.email-overview {
    border-left-color: var(--info-color);
}

.overview-card.celery-overview {
    border-left-color: var(--secondary-color);
}

.overview-card.system-overview {
    border-left-color: var(--success-color);
}

.overview-card.business-overview {
    border-left-color: var(--warning-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.card-header h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    font-weight: 500;
    background: var(--success-color);
    color: var(--text-light);
}

.card-status.warning {
    background: var(--warning-color);
}

.card-status.error {
    background: var(--error-color);
}

.metric-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.metric-item {
    text-align: center;
}

.metric-label {
    display: block;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    display: block;
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.metric-value.error {
    color: var(--error-color);
}

.metric-value.warning {
    color: var(--warning-color);
}

.metric-value.success {
    color: var(--success-color);
}

/* 監控區域 */
.monitoring-section {
    margin-bottom: var(--spacing-xxl);
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.monitoring-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.monitoring-panel:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.panel-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.refresh-btn, .expand-btn, .clear-all-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-sm);
    transition: all var(--transition-fast);
}

.refresh-btn:hover, .expand-btn:hover, .clear-all-btn:hover {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.panel-content {
    padding: var(--spacing-lg);
}

/* 載入中遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-text {
    color: var(--text-light);
    font-size: var(--font-lg);
}

/* 動畫 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .dashboard-container {
        padding: 0 var(--spacing-md);
    }
    
    .overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .monitoring-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .header-left, .header-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .dashboard-title {
        font-size: var(--font-xl);
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .monitoring-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .panel-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0 var(--spacing-sm);
    }
    
    .overview-card, .monitoring-panel {
        margin: 0 -var(--spacing-xs);
    }
    
    .metric-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm);
        background: var(--bg-primary);
        border-radius: var(--border-radius-sm);
    }
    
    .metric-label {
        margin-bottom: 0;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
        --border-color: #404040;
    }
    
    .overview-card, .monitoring-panel {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
    }
    
    .panel-header {
        background: linear-gradient(135deg, #3a3a3a, #2d2d2d);
    }
}

/* 列印樣式 */
@media print {
    .dashboard-header, .panel-controls, .loading-overlay {
        display: none !important;
    }
    
    .overview-cards, .monitoring-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .overview-card, .monitoring-panel {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}