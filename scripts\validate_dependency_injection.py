#!/usr/bin/env python3
"""
嚴格的依賴注入重構驗證腳本

此腳本檢查所有 API 端點是否正確使用依賴注入模式，
並生成詳細的驗證報告。
"""

import re
import os
from pathlib import Path
from typing import List, Dict, Tuple
from dataclasses import dataclass
import json


@dataclass
class EndpointInfo:
    """端點信息"""
    file_path: str
    line_number: int
    method: str
    path: str
    function_name: str
    uses_dependency_injection: bool
    has_manual_checks: bool
    has_request_tracking: bool
    issues: List[str]


class DependencyInjectionValidator:
    """依賴注入驗證器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.api_routes_dir = self.project_root / "src" / "presentation" / "api"
        
        # 定義模式
        self.endpoint_pattern = r'@router\.(get|post|put|delete|patch)\("([^"]+)"\)'
        self.function_pattern = r'async def (\w+)\('
        self.dependency_patterns = {
            'require_staging_service': r'Depends\(require_staging_service\)',
            'require_processing_service': r'Depends\(require_processing_service\)',
            'get_api_state': r'Depends\(get_api_state\)',
        }
        self.manual_check_patterns = [
            r'if.*get_file_staging_service.*is None',
            r'if.*get_file_processing_service.*is None',
            r'if.*staging_service.*is None',
            r'if.*processing_service.*is None',
        ]
        self.old_service_call_patterns = [
            r'get_file_staging_service\(\)',
            r'get_file_processing_service\(\)',
        ]
        self.request_tracking_patterns = [
            r'api_state\.increment_request_count\(\)',
            r'api_state\.increment_error_count\(\)',
        ]
    
    def find_route_files(self) -> List[Path]:
        """查找所有路由文件"""
        route_files = []
        # 只檢查我們重構的文件
        target_files = ["staging_routes.py", "processing_routes.py"]
        for file_name in target_files:
            file_path = self.api_routes_dir / file_name
            if file_path.exists():
                route_files.append(file_path)
        return route_files
    
    def extract_endpoints(self, file_path: Path) -> List[EndpointInfo]:
        """提取文件中的所有端點"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        endpoints = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 查找端點裝飾器
            endpoint_match = re.search(self.endpoint_pattern, line)
            if endpoint_match:
                method = endpoint_match.group(1)
                path = endpoint_match.group(2)
                
                # 查找函數定義
                j = i + 1
                function_name = None
                function_start_line = None
                
                while j < len(lines) and j < i + 10:  # 在接下來的10行內查找
                    func_match = re.search(self.function_pattern, lines[j])
                    if func_match:
                        function_name = func_match.group(1)
                        function_start_line = j
                        break
                    j += 1
                
                if function_name:
                    # 分析函數內容
                    function_content = self._extract_function_content(lines, function_start_line)
                    endpoint_info = self._analyze_endpoint(
                        file_path, i + 1, method, path, function_name, function_content
                    )
                    endpoints.append(endpoint_info)
                
                i = j if j < len(lines) else i + 1
            else:
                i += 1
        
        return endpoints
    
    def _extract_function_content(self, lines: List[str], start_line: int) -> str:
        """提取函數內容"""
        content_lines = []
        base_indent = None
        in_function = False

        for i in range(start_line, len(lines)):
            line = lines[i]

            # 跳過函數定義行，找到函數體開始
            if not in_function:
                if line.strip().startswith('"""') or line.strip().startswith("'''"):
                    # 跳過文檔字符串
                    continue
                if line.strip() and not line.strip().startswith('"""') and not line.strip().startswith("'''"):
                    in_function = True
                    base_indent = len(line) - len(line.lstrip())

            if in_function:
                # 如果遇到下一個函數或類定義，停止
                if (line.strip() and
                    (line.strip().startswith('@') or
                     line.strip().startswith('def ') or
                     line.strip().startswith('class ')) and
                    len(line) - len(line.lstrip()) <= base_indent and
                    i > start_line + 5):  # 給一些緩衝行數
                    break

                content_lines.append(line)

        return '\n'.join(content_lines)
    
    def _analyze_endpoint(self, file_path: Path, line_number: int, method: str, 
                         path: str, function_name: str, content: str) -> EndpointInfo:
        """分析端點是否使用依賴注入"""
        issues = []
        
        # 檢查是否使用依賴注入
        uses_dependency_injection = any(
            re.search(pattern, content) for pattern in self.dependency_patterns.values()
        )
        
        # 檢查是否有手動檢查
        has_manual_checks = any(
            re.search(pattern, content) for pattern in self.manual_check_patterns
        )
        
        # 檢查是否有舊式服務調用
        has_old_service_calls = any(
            re.search(pattern, content) for pattern in self.old_service_call_patterns
        )
        
        # 檢查是否有請求追蹤（至少要有 increment_request_count）
        has_request_tracking = bool(re.search(r'api_state\.increment_request_count\(\)', content))
        
        # 分析問題
        if not uses_dependency_injection:
            issues.append("未使用依賴注入模式")
        
        if has_manual_checks:
            issues.append("仍有手動服務可用性檢查")
        
        if has_old_service_calls:
            issues.append("仍使用舊式服務調用")
        
        if not has_request_tracking:
            issues.append("缺少請求追蹤")
        
        return EndpointInfo(
            file_path=str(file_path.relative_to(self.project_root)),
            line_number=line_number,
            method=method.upper(),
            path=path,
            function_name=function_name,
            uses_dependency_injection=uses_dependency_injection,
            has_manual_checks=has_manual_checks,
            has_request_tracking=has_request_tracking,
            issues=issues
        )
    
    def validate_all(self) -> Dict:
        """驗證所有路由文件"""
        route_files = self.find_route_files()
        all_endpoints = []
        
        for file_path in route_files:
            endpoints = self.extract_endpoints(file_path)
            all_endpoints.extend(endpoints)
        
        # 統計
        total_endpoints = len(all_endpoints)
        compliant_endpoints = [ep for ep in all_endpoints if not ep.issues]
        non_compliant_endpoints = [ep for ep in all_endpoints if ep.issues]
        
        compliance_rate = (len(compliant_endpoints) / total_endpoints * 100) if total_endpoints > 0 else 0
        
        return {
            'total_endpoints': total_endpoints,
            'compliant_endpoints': len(compliant_endpoints),
            'non_compliant_endpoints': len(non_compliant_endpoints),
            'compliance_rate': compliance_rate,
            'endpoints': all_endpoints,
            'files_analyzed': [str(f.relative_to(self.project_root)) for f in route_files]
        }
    
    def generate_report(self, results: Dict) -> str:
        """生成驗證報告"""
        report = []
        report.append("# 依賴注入重構驗證報告")
        report.append("")
        report.append("## 📊 總體統計")
        report.append("")
        report.append(f"- **總端點數**: {results['total_endpoints']} 個")
        report.append(f"- **符合標準端點**: {results['compliant_endpoints']} 個")
        report.append(f"- **不符合標準端點**: {results['non_compliant_endpoints']} 個")
        report.append(f"- **合規率**: {results['compliance_rate']:.2f}%")
        report.append("")
        
        if results['compliance_rate'] == 100:
            report.append("✅ **所有端點都符合依賴注入標準！**")
        else:
            report.append("❌ **仍有端點需要重構**")
        
        report.append("")
        report.append("## 📋 詳細分析")
        report.append("")
        
        # 按文件分組
        by_file = {}
        for endpoint in results['endpoints']:
            file_path = endpoint.file_path
            if file_path not in by_file:
                by_file[file_path] = []
            by_file[file_path].append(endpoint)
        
        for file_path, endpoints in by_file.items():
            compliant = [ep for ep in endpoints if not ep.issues]
            non_compliant = [ep for ep in endpoints if ep.issues]
            
            report.append(f"### {file_path}")
            report.append("")
            report.append(f"- 總端點: {len(endpoints)} 個")
            report.append(f"- 符合標準: {len(compliant)} 個")
            report.append(f"- 需要重構: {len(non_compliant)} 個")
            report.append("")
            
            if non_compliant:
                report.append("#### ❌ 需要重構的端點:")
                report.append("")
                for ep in non_compliant:
                    report.append(f"**{ep.method} {ep.path}** (line {ep.line_number})")
                    for issue in ep.issues:
                        report.append(f"  - ❌ {issue}")
                    report.append("")
            
            if compliant:
                report.append("#### ✅ 符合標準的端點:")
                report.append("")
                for ep in compliant:
                    report.append(f"- **{ep.method} {ep.path}** (line {ep.line_number})")
                report.append("")
        
        return '\n'.join(report)


def main():
    """主函數"""
    project_root = os.getcwd()
    validator = DependencyInjectionValidator(project_root)
    
    print("🔍 開始驗證依賴注入重構...")
    results = validator.validate_all()
    
    print(f"📊 驗證完成:")
    print(f"  - 總端點數: {results['total_endpoints']}")
    print(f"  - 合規率: {results['compliance_rate']:.2f}%")
    
    # 生成報告
    report = validator.generate_report(results)
    
    # 保存報告
    report_path = Path(project_root) / "reports" / "dependency_injection_validation.md"
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 驗證報告已保存到: {report_path}")
    
    # 保存 JSON 數據
    json_path = Path(project_root) / "reports" / "dependency_injection_validation.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        # 轉換 EndpointInfo 對象為字典
        json_data = {
            **results,
            'endpoints': [
                {
                    'file_path': ep.file_path,
                    'line_number': ep.line_number,
                    'method': ep.method,
                    'path': ep.path,
                    'function_name': ep.function_name,
                    'uses_dependency_injection': ep.uses_dependency_injection,
                    'has_manual_checks': ep.has_manual_checks,
                    'has_request_tracking': ep.has_request_tracking,
                    'issues': ep.issues
                }
                for ep in results['endpoints']
            ]
        }
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"📊 JSON 數據已保存到: {json_path}")
    
    if results['compliance_rate'] == 100:
        print("✅ 所有端點都符合依賴注入標準！")
        return 0
    else:
        print("❌ 仍有端點需要重構，請查看報告了解詳情")
        return 1


if __name__ == "__main__":
    exit(main())
