"""服務整合器
將 Flask 和 FastAPI 服務整合到單一 FastAPI 應用程式中
使用 WSGIMiddleware 實現無縫整合
"""

import asyncio
import threading
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse, HTMLResponse
from loguru import logger

from .config_manager import get_config_manager, ServiceConfig, ServiceType


class ServiceProcess:
    """服務進程管理器"""
    
    def __init__(self, service_config: ServiceConfig):
        self.config = service_config
        self.process: Optional[subprocess.Popen] = None
        self.is_running = False
    
    async def start(self) -> bool:
        """啟動服務進程"""
        if self.is_running:
            logger.warning(f"服務 {self.config.name} 已經運行")
            return True
        
        try:
            # 根據服務類型啟動不同的進程
            if self.config.service_type == ServiceType.FLASK:
                success = await self._start_flask_service()
            elif self.config.service_type == ServiceType.FASTAPI:
                success = await self._start_fastapi_service()
            else:
                logger.error(f"不支援的服務類型: {self.config.service_type}")
                return False
            
            if success:
                self.is_running = True
                logger.info(f"服務 {self.config.name} 啟動成功，端口: {self.config.port}")
            
            return success
            
        except Exception as e:
            logger.error(f"啟動服務 {self.config.name} 失敗: {e}")
            return False
    
    async def _start_flask_service(self) -> bool:
        """啟動 Flask 服務"""
        # 這裡不啟動獨立進程，而是通過 WSGIMiddleware 整合
        # Flask 服務將在主 FastAPI 應用中作為 middleware 運行
        return True
    
    async def _start_fastapi_service(self) -> bool:
        """啟動 FastAPI 服務"""
        # FastAPI 服務將通過 router 整合到主應用中
        return True
    
    async def stop(self):
        """停止服務"""
        if self.process:
            self.process.terminate()
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
        
        self.is_running = False
        logger.info(f"服務 {self.config.name} 已停止")
    
    def health_check(self) -> bool:
        """健康檢查"""
        if not self.is_running:
            return False
        
        if self.process:
            return self.process.poll() is None
        
        return True


class ServiceIntegrator:
    """服務整合器
    
    負責將所有服務整合到單一 FastAPI 應用中
    """
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.app: Optional[FastAPI] = None
        self.service_processes: Dict[str, ServiceProcess] = {}
        self.service_apps: Dict[str, Any] = {}  # 儲存服務應用實例
    
    def create_integrated_app(self) -> FastAPI:
        """建立整合應用程式"""
        # 建立主 FastAPI 應用
        self.app = FastAPI(
            title="企業級整合服務平台",
            description="整合所有郵件處理、文件分析和任務排程服務",
            version="1.0.0"
        )
        
        # 設定 CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config_manager.get_cors_origins(),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )

        # 掛載全域靜態檔案服務
        self._mount_global_static_files()

        # 整合所有啟用的服務
        self._integrate_services()
        
        # 添加系統管理端點
        self._add_system_endpoints()
        
        return self.app

    def _mount_global_static_files(self):
        """掛載全域靜態檔案服務"""
        from pathlib import Path
        from fastapi.staticfiles import StaticFiles

        static_path = Path(__file__).parent.parent / "presentation" / "web" / "static"
        if static_path.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
            logger.info(f"[OK] 全域靜態檔案服務已啟用: {static_path}")
        else:
            logger.warning(f"[WARNING] 靜態檔案目錄不存在: {static_path}")

    def _integrate_services(self):
        """整合所有服務"""
        enabled_services = self.config_manager.get_enabled_services()
        
        for service_name, service_config in enabled_services.items():
            try:
                if service_config.service_type == ServiceType.FLASK:
                    self._integrate_flask_service(service_name, service_config)
                elif service_config.service_type == ServiceType.FASTAPI:
                    self._integrate_fastapi_service(service_name, service_config)
                
                # 建立服務進程管理器
                self.service_processes[service_name] = ServiceProcess(service_config)
                
            except Exception as e:
                logger.error(f"整合服務 {service_name} 失敗: {e}")
    
    def _integrate_flask_service(self, service_name: str, config: ServiceConfig):
        """整合 Flask 服務"""
        if service_name == "inbox":
            # 動態導入 Flask 應用
            try:
                from email_inbox_app import create_flask_app
                flask_app = create_flask_app()
                
                # 通過 WSGIMiddleware 整合
                self.app.mount(
                    config.path_prefix,
                    WSGIMiddleware(flask_app)
                )
                
                self.service_apps[service_name] = flask_app
                logger.info(f"Flask 服務 {service_name} 整合成功")
                
            except ImportError as e:
                logger.error(f"無法導入 Flask 應用: {e}")
            except Exception as e:
                logger.error(f"整合 Flask 服務失敗: {e}")
    
    def _integrate_fastapi_service(self, service_name: str, config: ServiceConfig):
        """整合 FastAPI 服務"""
        try:
            router = None
            
            if service_name == "ft_eqc":
                from src.presentation.api.ft_eqc_api import app as ft_eqc_app
                # 將 FastAPI 應用轉換為 router
                router = self._convert_app_to_router(ft_eqc_app, config.path_prefix)

                # 為 FT-EQC 服務單獨掛載靜態文件
                from pathlib import Path
                from fastapi.staticfiles import StaticFiles
                static_path = Path(__file__).parent.parent / "presentation" / "web" / "static"
                if static_path.exists():
                    self.app.mount(f"{config.path_prefix}/static", StaticFiles(directory=str(static_path)), name=f"{service_name}_static")
                    logger.info(f"[OK] FT-EQC 靜態檔案服務已啟用: {config.path_prefix}/static -> {static_path}")
                else:
                    logger.warning(f"[WARNING] FT-EQC 靜態檔案目錄不存在: {static_path}")
                
            elif service_name == "scheduler":
                from src.presentation.api.enhanced_scheduler_api import router as scheduler_router
                router = scheduler_router
                
            elif service_name == "network":
                from src.presentation.api.network_browser_api_new import app as network_app
                router = self._convert_app_to_router(network_app, config.path_prefix)
                
                # 為網路服務單獨掛載靜態文件
                from pathlib import Path
                from fastapi.staticfiles import StaticFiles
                static_path = Path(__file__).parent.parent / "presentation" / "web" / "static"
                if static_path.exists():
                    self.app.mount(f"{config.path_prefix}/static", StaticFiles(directory=str(static_path)), name=f"{service_name}_static")
            
            if router:
                self.app.include_router(
                    router,
                    prefix=config.path_prefix,
                    tags=[config.name]
                )
                
                self.service_apps[service_name] = router
                logger.info(f"FastAPI 服務 {service_name} 整合成功")
            
        except ImportError as e:
            logger.error(f"無法導入 {service_name} 服務: {e}")
        except Exception as e:
            logger.error(f"整合 {service_name} 服務失敗: {e}")
    
    def _convert_app_to_router(self, app: FastAPI, prefix: str):
        """將 FastAPI 應用轉換為 router"""
        from fastapi import APIRouter
        from fastapi.routing import APIRoute
        
        router = APIRouter()
        
        # 複製所有路由
        for route in app.routes:
            if isinstance(route, APIRoute):
                # 創建新的路由，保持原有的處理函數和配置
                new_route = APIRoute(
                    path=route.path,
                    endpoint=route.endpoint,
                    methods=route.methods,
                    name=route.name,
                    dependencies=route.dependencies,
                    response_model=route.response_model,
                    status_code=route.status_code,
                    tags=route.tags,
                    summary=route.summary,
                    description=route.description,
                    response_description=route.response_description,
                    responses=route.responses,
                    deprecated=route.deprecated,
                    operation_id=route.operation_id,
                    response_model_include=route.response_model_include,
                    response_model_exclude=route.response_model_exclude,
                    response_model_by_alias=route.response_model_by_alias,
                    response_model_exclude_unset=route.response_model_exclude_unset,
                    response_model_exclude_defaults=route.response_model_exclude_defaults,
                    response_model_exclude_none=route.response_model_exclude_none,
                    include_in_schema=route.include_in_schema,
                    response_class=route.response_class,
                    callbacks=route.callbacks,
                    openapi_extra=route.openapi_extra,
                    generate_unique_id_function=route.generate_unique_id_function
                )
                router.routes.append(new_route)
                logger.debug(f"添加路由: {route.path} -> {prefix}{route.path}")
        
        logger.info(f"成功轉換 {len(router.routes)} 個路由到 router")
        return router
    
    def _add_system_endpoints(self):
        """添加系統管理端點"""
        
        @self.app.get("/health")
        async def health_check():
            """健康檢查端點"""
            service_status = {}
            
            for name, process in self.service_processes.items():
                service_status[name] = {
                    "status": "running" if process.health_check() else "stopped",
                    "config": {
                        "name": process.config.name,
                        "port": process.config.port,
                        "path_prefix": process.config.path_prefix
                    }
                }
            
            return {
                "status": "healthy",
                "main_port": self.config_manager.get_main_port(),
                "services": service_status,
                "timestamp": asyncio.get_event_loop().time()
            }
        
        @self.app.get("/services")
        async def list_services():
            """列出所有服務"""
            services = []
            
            for name, config in self.config_manager.get_enabled_services().items():
                services.append({
                    "name": name,
                    "display_name": config.name,
                    "type": config.service_type.value,
                    "path_prefix": config.path_prefix,
                    "port": config.port,
                    "enabled": config.enabled,
                    "metadata": config.metadata
                })
            
            return {"services": services}
        
        @self.app.get("/")
        async def root():
            """根路徑"""
            return {
                "message": "歡迎使用企業級整合服務平台",
                "version": "1.0.0",
                "services": {
                    "inbox": "/inbox",
                    "ft_eqc": "/ft-eqc",
                    "scheduler": "/scheduler",
                    "network": "/network",
                    "database_manager": "/inbox/database-manager"
                },
                "docs": "/docs",
                "health": "/health"
            }
        
        @self.app.get("/ft-eqc", response_class=HTMLResponse)
        async def ft_eqc_main():
            """FT-EQC 主頁面"""
            try:
                # 創建一個簡單的 FT-EQC 主頁面
                html_content = """
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT-EQC 分組處理系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; }
        .feature-card { transition: transform 0.3s; }
        .feature-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="display-4"><i class="fas fa-chart-line"></i> FT-EQC 分組處理系統</h1>
                    <p class="lead">專業的 FT-EQC 數據分析和分組處理平台</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">檔案上傳</h5>
                        <p class="card-text">支援多種格式的檔案上傳和處理</p>
                        <a href="/ft-eqc/docs" class="btn btn-primary">查看 API</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                        <h5 class="card-title">分組處理</h5>
                        <p class="card-text">自動化的數據分組和統計分析</p>
                        <a href="/ft-eqc/health" class="btn btn-success">服務狀態</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-3x text-info mb-3"></i>
                        <h5 class="card-title">結果下載</h5>
                        <p class="card-text">處理完成後的結果檔案下載</p>
                        <a href="/" class="btn btn-info">返回首頁</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 系統狀態</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-status">載入中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 載入系統狀態
        fetch('/ft-eqc/health')
            .then(response => response.json())
            .then(data => {
                document.getElementById('system-status').innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> 
                        服務狀態：正常運行 | 版本：${data.version || '2.0.0'}
                    </div>
                `;
            })
            .catch(error => {
                document.getElementById('system-status').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        無法獲取系統狀態
                    </div>
                `;
            });
    </script>
</body>
</html>
                """
                return HTMLResponse(content=html_content)
            except Exception as e:
                logger.error(f"載入 FT-EQC 主頁失敗: {e}")
                return HTMLResponse(content=f"<h1>載入 FT-EQC 主頁失敗: {e}</h1>", status_code=500)

        # database-manager 路由已被移除，使用 Flask 應用的原始路由
        # Flask 應用已包含完整的 database-manager 功能，包括模板渲染和靜態文件處理
        
        # 添加 API 路由代理 - 將 /api/* 重定向到 /inbox/api/*
        from fastapi import Request
        from fastapi.responses import RedirectResponse
        
        @self.app.api_route("/api/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
        async def api_proxy(request: Request, path: str):
            """API 路由代理 - 重定向到 inbox API"""
            # 獲取查詢參數
            query_string = str(request.url.query)
            redirect_url = f"/inbox/api/{path}"
            if query_string:
                redirect_url += f"?{query_string}"
            
            logger.info(f"API 代理重定向: {request.url.path} -> {redirect_url}")
            return RedirectResponse(url=redirect_url, status_code=307)
    
    async def start_all_services(self):
        """啟動所有服務"""
        logger.info("正在啟動所有服務...")
        
        for name, process in self.service_processes.items():
            success = await process.start()
            if not success:
                logger.error(f"啟動服務 {name} 失敗")
    
    async def stop_all_services(self):
        """停止所有服務"""
        logger.info("正在停止所有服務...")
        
        for name, process in self.service_processes.items():
            await process.stop()
    
    def get_service_app(self, service_name: str) -> Optional[Any]:
        """獲取服務應用實例"""
        return self.service_apps.get(service_name)


# 全域服務整合器實例
_service_integrator: Optional[ServiceIntegrator] = None


def get_service_integrator() -> ServiceIntegrator:
    """獲取全域服務整合器實例"""
    global _service_integrator
    
    if _service_integrator is None:
        _service_integrator = ServiceIntegrator()
    
    return _service_integrator


def create_integrated_app() -> FastAPI:
    """建立整合應用程式"""
    integrator = get_service_integrator()
    return integrator.create_integrated_app()
