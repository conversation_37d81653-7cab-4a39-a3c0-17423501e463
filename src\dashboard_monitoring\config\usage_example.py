#!/usr/bin/env python3
"""統一監控儀表板配置系統使用範例

展示如何使用配置系統的各項功能：
- 基本配置載入
- 環境變數覆蓋
- 監控規則管理
- 動態配置更新
"""

import os
from pathlib import Path

# 匯入配置系統
from . import (
    get_dashboard_config,
    get_monitoring_rules_manager,
    init_dashboard_config,
    DashboardConfig,
    MonitoringRule,
    AlertCondition,
    ConditionType,
    RuleCategory,
    AlertLevel
)


def basic_config_usage():
    """基本配置使用範例"""
    print("📋 基本配置使用範例")
    
    # 獲取預設配置
    config = get_dashboard_config()
    
    print(f"當前環境: {config.env}")
    print(f"郵件待處理警告閾值: {config.alert_thresholds.email_pending_warning}")
    print(f"指標收集間隔: {config.update_intervals.metrics_collection}秒")
    print(f"WebSocket最大連接數: {config.websocket_config.max_connections}")
    
    # 獲取特定告警閾值
    cpu_warning = config.get_alert_threshold("cpu", "warning")
    print(f"CPU警告閾值: {cpu_warning}%")
    
    # 檢查環境
    if config.is_development():
        print("運行在開發環境")
    elif config.is_production():
        print("運行在生產環境")


def environment_override_example():
    """環境變數覆蓋範例"""
    print("\n🌍 環境變數覆蓋範例")
    
    # 設置環境變數
    os.environ["DASHBOARD_EMAIL_PENDING_WARNING"] = "25"
    os.environ["DASHBOARD_CPU_WARNING"] = "70.0"
    os.environ["DASHBOARD_NOTIFICATION_CHANNELS"] = "system,email,line"
    
    # 重新初始化配置以應用環境變數
    config = DashboardConfig()
    
    print(f"覆蓋後的郵件警告閾值: {config.alert_thresholds.email_pending_warning}")
    print(f"覆蓋後的CPU警告閾值: {config.alert_thresholds.cpu_warning}%")
    print(f"覆蓋後的通知管道: {[ch.value for ch in config.notification_config.enabled_channels]}")
    
    # 清理環境變數
    for key in ["DASHBOARD_EMAIL_PENDING_WARNING", "DASHBOARD_CPU_WARNING", "DASHBOARD_NOTIFICATION_CHANNELS"]:
        if key in os.environ:
            del os.environ[key]


def monitoring_rules_usage():
    """監控規則使用範例"""
    print("\n📏 監控規則使用範例")
    
    # 獲取規則管理器
    rules_manager = get_monitoring_rules_manager()
    
    # 查看規則統計
    stats = rules_manager.get_rule_statistics()
    print(f"總規則數: {stats['total_rules']}")
    print(f"啟用規則數: {stats['enabled_rules']}")
    print(f"類別分布: {stats['category_counts']}")
    
    # 獲取特定類別的規則
    email_rules = rules_manager.get_rules_by_category(RuleCategory.EMAIL)
    print(f"郵件相關規則數: {len(email_rules)}")
    
    # 新增自定義規則
    custom_rule = MonitoringRule(
        rule_id="custom_disk_space_warning",
        rule_name="自定義磁碟空間警告",
        category=RuleCategory.SYSTEM,
        alert_level=AlertLevel.WARNING,
        conditions=[
            AlertCondition(
                condition_type=ConditionType.GREATER_THAN,
                threshold_value=80.0,
                comparison_field="system.disk_percent",
                description="磁碟使用率超過80%"
            )
        ],
        alert_title="磁碟空間警告",
        alert_message="磁碟使用率過高，請檢查並清理不必要的檔案",
        notification_channels=["system", "email"],
        cooldown_period=600  # 10分鐘冷卻期
    )
    
    rules_manager.add_rule(custom_rule)
    print(f"新增自定義規則: {custom_rule.rule_name}")
    
    # 模擬規則評估
    test_metrics = {
        "system": {
            "disk_percent": 85.0,
            "cpu_percent": 45.0,
            "memory_percent": 60.0
        },
        "email": {
            "pending_count": 5
        }
    }
    
    triggered_rules = rules_manager.evaluate_all_rules(test_metrics)
    print(f"觸發的規則數: {len(triggered_rules)}")
    
    for rule in triggered_rules:
        print(f"  - {rule.rule_name} ({rule.alert_level.value})")


def config_file_usage():
    """配置檔案使用範例"""
    print("\n📄 配置檔案使用範例")
    
    # 創建配置檔案路徑
    config_file = Path("config/dashboard_monitoring_example.json")
    
    # 初始化配置並保存
    config = DashboardConfig()
    
    # 修改一些配置值
    config.alert_thresholds.email_pending_warning = 20
    config.update_intervals.metrics_collection = 45
    config.websocket_config.max_connections = 150
    
    # 保存配置
    config.save_config(config_file)
    print(f"配置已保存到: {config_file}")
    
    # 從檔案載入配置
    loaded_config = DashboardConfig(config_file=config_file)
    print(f"從檔案載入的郵件警告閾值: {loaded_config.alert_thresholds.email_pending_warning}")
    
    # 清理測試檔案
    if config_file.exists():
        config_file.unlink()
        print("測試配置檔案已清理")


def advanced_usage():
    """進階使用範例"""
    print("\n🚀 進階使用範例")
    
    config = get_dashboard_config()
    rules_manager = get_monitoring_rules_manager()
    
    # 動態調整告警閾值
    original_threshold = config.alert_thresholds.cpu_warning
    config.alert_thresholds.cpu_warning = 75.0
    print(f"CPU警告閾值從 {original_threshold}% 調整為 {config.alert_thresholds.cpu_warning}%")
    
    # 停用特定規則
    rule_id = "cpu_usage_warning"
    if rules_manager.disable_rule(rule_id):
        print(f"已停用規則: {rule_id}")
    
    # 重新啟用規則
    if rules_manager.enable_rule(rule_id):
        print(f"已重新啟用規則: {rule_id}")
    
    # 獲取資料庫URL
    db_url = config.get_database_url()
    print(f"資料庫URL: {db_url}")
    
    # 轉換配置為字典（用於API回應）
    config_dict = config.to_dict()
    print(f"配置字典包含 {len(config_dict)} 個主要區段")


def main():
    """主函數 - 執行所有範例"""
    print("🎯 統一監控儀表板配置系統使用範例\n")
    
    try:
        basic_config_usage()
        environment_override_example()
        monitoring_rules_usage()
        config_file_usage()
        advanced_usage()
        
        print("\n✅ 所有範例執行完成！")
        
    except Exception as e:
        print(f"\n❌ 範例執行失敗: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()