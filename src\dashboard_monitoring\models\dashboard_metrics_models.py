"""
Dashboard Metrics Models

This module defines all data models for monitoring metrics in the unified dashboard.
Supports email processing, Celery tasks, system resources, file processing, and business metrics.

Requirements covered: 1, 2, 5, 6, 8, 9
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json
from pydantic import BaseModel, Field, validator


class MetricType(Enum):
    """Enumeration of metric types for categorization"""
    EMAIL_QUEUE = "email_queue"
    CELERY_TASK = "celery_task"
    SYSTEM_RESOURCE = "system_resource"
    FILE_PROCESSING = "file_processing"
    DATABASE_PERFORMANCE = "database_performance"
    BUSINESS_METRICS = "business_metrics"


class MetricUnit(Enum):
    """Enumeration of metric units"""
    COUNT = "count"
    PERCENT = "percent"
    SECONDS = "seconds"
    BYTES = "bytes"
    MEGABYTES = "mb"
    GIGABYTES = "gb"
    RATE = "rate"
    RATIO = "ratio"


@dataclass
class MonitoringMetric:
    """Base monitoring metric with validation and serialization"""
    metric_type: MetricType
    metric_name: str
    metric_value: float
    metric_unit: MetricUnit
    tags: Dict[str, str] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    source_system: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "metric_type": self.metric_type.value,
            "metric_name": self.metric_name,
            "metric_value": self.metric_value,
            "metric_unit": self.metric_unit.value,
            "tags": self.tags,
            "timestamp": self.timestamp.isoformat(),
            "source_system": self.source_system
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringMetric':
        """Create from dictionary"""
        return cls(
            metric_type=MetricType(data["metric_type"]),
            metric_name=data["metric_name"],
            metric_value=data["metric_value"],
            metric_unit=MetricUnit(data["metric_unit"]),
            tags=data.get("tags", {}),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            source_system=data.get("source_system")
        )


@dataclass
class EmailMetrics:
    """Email processing metrics - Requirement 1"""
    # Queue status
    pending_count: int = 0
    processing_count: int = 0
    completed_count: int = 0
    failed_count: int = 0
    
    # Processing performance
    avg_processing_time_seconds: float = 0.0
    throughput_per_hour: float = 0.0
    
    # Vendor grouping - supports GTK, JCET, ETD, LINGSEN, XAHT
    vendor_queue_counts: Dict[str, int] = field(default_factory=dict)
    vendor_success_rates: Dict[str, float] = field(default_factory=dict)
    
    # code_comparison.py specific metrics
    code_comparison_active: int = 0
    code_comparison_pending: int = 0
    code_comparison_avg_duration: float = 0.0
    
    # Additional email service metrics
    last_email_check: Optional[datetime] = None
    connection_status: str = "unknown"  # "connected", "disconnected", "error"
    connection_failures: int = 0
    
    timestamp: datetime = field(default_factory=datetime.now)

    def get_total_queue_size(self) -> int:
        """Get total queue size across all vendors"""
        return self.pending_count + self.processing_count

    def get_success_rate(self) -> float:
        """Calculate overall success rate"""
        total_processed = self.completed_count + self.failed_count
        if total_processed == 0:
            return 0.0
        return self.completed_count / total_processed

    def is_queue_warning(self, warning_threshold: int = 10) -> bool:
        """Check if queue size exceeds warning threshold - Requirement 1.5"""
        return self.pending_count > warning_threshold

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "pending_count": self.pending_count,
            "processing_count": self.processing_count,
            "completed_count": self.completed_count,
            "failed_count": self.failed_count,
            "avg_processing_time_seconds": self.avg_processing_time_seconds,
            "throughput_per_hour": self.throughput_per_hour,
            "vendor_queue_counts": self.vendor_queue_counts,
            "vendor_success_rates": self.vendor_success_rates,
            "code_comparison_active": self.code_comparison_active,
            "code_comparison_pending": self.code_comparison_pending,
            "code_comparison_avg_duration": self.code_comparison_avg_duration,
            "last_email_check": self.last_email_check.isoformat() if self.last_email_check else None,
            "connection_status": self.connection_status,
            "connection_failures": self.connection_failures,
            "timestamp": self.timestamp.isoformat(),
            "total_queue_size": self.get_total_queue_size(),
            "success_rate": self.get_success_rate()
        }


@dataclass
class CeleryMetrics:
    """Celery task metrics - Requirement 2"""
    # Overall status
    total_active: int = 0
    total_pending: int = 0
    total_completed: int = 0
    total_failed: int = 0
    
    # Task type breakdown - code_comparison, csv_to_summary, compression, decompression
    task_type_counts: Dict[str, Dict[str, int]] = field(default_factory=dict)
    
    # Worker status
    worker_status: Dict[str, str] = field(default_factory=dict)  # {"worker1": "online", "worker2": "offline"}
    worker_load: Dict[str, int] = field(default_factory=dict)    # {"worker1": 3, "worker2": 0}
    
    # Performance metrics
    avg_task_duration: Dict[str, float] = field(default_factory=dict)  # by task type
    task_success_rate: Dict[str, float] = field(default_factory=dict)  # by task type
    
    # Long-running task monitoring
    long_running_tasks: List[Dict[str, Any]] = field(default_factory=list)
    
    timestamp: datetime = field(default_factory=datetime.now)

    def get_task_type_total(self, task_type: str) -> int:
        """Get total tasks for a specific type"""
        if task_type not in self.task_type_counts:
            return 0
        counts = self.task_type_counts[task_type]
        return counts.get("active", 0) + counts.get("pending", 0)

    def is_queue_critical(self, critical_threshold: int = 20) -> bool:
        """Check if any queue exceeds critical threshold - Requirement 2.7"""
        return self.total_pending > critical_threshold

    def get_long_running_warning(self, time_threshold: float = 1800) -> List[str]:
        """Get tasks exceeding time threshold - Requirement 2.8"""
        warnings = []
        for task in self.long_running_tasks:
            if task.get("duration", 0) > time_threshold:
                warnings.append(f"Task {task.get('name', 'unknown')} running for {task.get('duration', 0):.0f}s")
        return warnings

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "total_active": self.total_active,
            "total_pending": self.total_pending,
            "total_completed": self.total_completed,
            "total_failed": self.total_failed,
            "task_type_counts": self.task_type_counts,
            "worker_status": self.worker_status,
            "worker_load": self.worker_load,
            "avg_task_duration": self.avg_task_duration,
            "task_success_rate": self.task_success_rate,
            "long_running_tasks": self.long_running_tasks,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class SystemMetrics:
    """System resource metrics - Requirement 6"""
    # Resource utilization
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    disk_percent: float = 0.0
    
    # Detailed resource information
    memory_available_mb: float = 0.0
    disk_free_gb: float = 0.0
    
    # Network and connections
    active_connections: int = 0
    websocket_connections: int = 0
    
    # Service health status
    service_health: Dict[str, str] = field(default_factory=dict)
    # {
    #   "email_service": "healthy",
    #   "celery_service": "healthy", 
    #   "database": "healthy",
    #   "scheduler": "warning"
    # }
    
    # Database metrics
    database_connections: int = 0
    database_query_avg_time: float = 0.0
    database_size_mb: float = 0.0
    
    # Load average (Linux/Unix systems)
    load_average: List[float] = field(default_factory=list)
    
    # Network I/O
    network_io: Dict[str, float] = field(default_factory=dict)
    
    timestamp: datetime = field(default_factory=datetime.now)

    def is_resource_warning(self, cpu_threshold: float = 80, memory_threshold: float = 85, 
                          disk_threshold: float = 85) -> Dict[str, bool]:
        """Check resource warning thresholds - Requirement 6.4"""
        return {
            "cpu_warning": self.cpu_percent > cpu_threshold,
            "memory_warning": self.memory_percent > memory_threshold,
            "disk_warning": self.disk_percent > disk_threshold
        }

    def is_resource_critical(self, cpu_threshold: float = 95, memory_threshold: float = 95, 
                           disk_threshold: float = 95) -> Dict[str, bool]:
        """Check resource critical thresholds - Requirement 6.5"""
        return {
            "cpu_critical": self.cpu_percent > cpu_threshold,
            "memory_critical": self.memory_percent > memory_threshold,
            "disk_critical": self.disk_percent > disk_threshold
        }

    def get_unhealthy_services(self) -> List[str]:
        """Get list of unhealthy services"""
        return [service for service, status in self.service_health.items() 
                if status not in ["healthy", "ok"]]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "disk_percent": self.disk_percent,
            "memory_available_mb": self.memory_available_mb,
            "disk_free_gb": self.disk_free_gb,
            "active_connections": self.active_connections,
            "websocket_connections": self.websocket_connections,
            "service_health": self.service_health,
            "database_connections": self.database_connections,
            "database_query_avg_time": self.database_query_avg_time,
            "database_size_mb": self.database_size_mb,
            "load_average": self.load_average,
            "network_io": self.network_io,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class FileMetrics:
    """File processing metrics - Requirement 8"""
    # Attachment processing
    attachments_downloaded: int = 0
    attachments_pending: int = 0
    attachments_failed: int = 0
    
    # File type statistics
    file_type_counts: Dict[str, int] = field(default_factory=dict)  # {"csv": 10, "excel": 5, "zip": 3}
    
    # Compression/decompression
    compression_active: int = 0
    compression_pending: int = 0
    decompression_active: int = 0
    decompression_pending: int = 0
    
    # Storage space
    temp_folder_size_mb: float = 0.0
    upload_folder_size_mb: float = 0.0
    processed_folder_size_mb: float = 0.0
    
    # Processing performance
    avg_download_time: float = 0.0
    avg_compression_time: float = 0.0
    avg_decompression_time: float = 0.0
    
    # Vendor parser statistics
    parser_success_rates: Dict[str, float] = field(default_factory=dict)  # {"GTK": 0.95, "JCET": 0.88}
    parser_failure_reasons: Dict[str, List[str]] = field(default_factory=dict)
    
    timestamp: datetime = field(default_factory=datetime.now)

    def get_attachment_success_rate(self) -> float:
        """Calculate attachment processing success rate - Requirement 8.1"""
        total = self.attachments_downloaded + self.attachments_failed
        if total == 0:
            return 0.0
        return self.attachments_downloaded / total

    def is_storage_warning(self, warning_threshold_mb: float = 1000) -> bool:
        """Check if storage exceeds warning threshold - Requirement 8.5"""
        total_size = self.temp_folder_size_mb + self.upload_folder_size_mb + self.processed_folder_size_mb
        return total_size > warning_threshold_mb

    def get_failed_parsers(self) -> List[str]:
        """Get list of parsers with low success rates"""
        failed_parsers = []
        for parser, rate in self.parser_success_rates.items():
            if rate < 0.8:  # Less than 80% success rate
                failed_parsers.append(parser)
        return failed_parsers

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "attachments_downloaded": self.attachments_downloaded,
            "attachments_pending": self.attachments_pending,
            "attachments_failed": self.attachments_failed,
            "file_type_counts": self.file_type_counts,
            "compression_active": self.compression_active,
            "compression_pending": self.compression_pending,
            "decompression_active": self.decompression_active,
            "decompression_pending": self.decompression_pending,
            "temp_folder_size_mb": self.temp_folder_size_mb,
            "upload_folder_size_mb": self.upload_folder_size_mb,
            "processed_folder_size_mb": self.processed_folder_size_mb,
            "avg_download_time": self.avg_download_time,
            "avg_compression_time": self.avg_compression_time,
            "avg_decompression_time": self.avg_decompression_time,
            "parser_success_rates": self.parser_success_rates,
            "parser_failure_reasons": self.parser_failure_reasons,
            "timestamp": self.timestamp.isoformat(),
            "attachment_success_rate": self.get_attachment_success_rate()
        }


@dataclass
class BusinessMetrics:
    """Business metrics for semiconductor processing - Requirement 9"""
    # MO/LOT processing statistics
    mo_processed_today: int = 0
    lot_processed_today: int = 0
    
    # Data quality
    data_quality_score: float = 0.0  # 0-100
    validation_errors_count: int = 0
    duplicate_mo_count: int = 0
    duplicate_lot_count: int = 0
    
    # Vendor processing statistics
    vendor_processing_stats: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    # {
    #   "GTK": {"mo_count": 15, "lot_count": 45, "success_rate": 95, "yield_rate": 0.85},
    #   "JCET": {"mo_count": 8, "lot_count": 24, "success_rate": 88, "yield_rate": 0.92}
    # }
    
    # Excel report generation
    reports_generated_today: int = 0
    reports_pending: int = 0
    avg_report_generation_time: float = 0.0
    
    # Data validation issues
    missing_fields: Dict[str, int] = field(default_factory=dict)
    format_errors: Dict[str, int] = field(default_factory=dict)
    
    # Anomaly detection
    anomalous_mo_list: List[str] = field(default_factory=list)
    anomalous_lot_list: List[str] = field(default_factory=list)
    
    timestamp: datetime = field(default_factory=datetime.now)

    def get_overall_yield_rate(self) -> float:
        """Calculate overall yield rate across all vendors"""
        total_yield = 0.0
        total_count = 0
        
        for vendor_stats in self.vendor_processing_stats.values():
            if "yield_rate" in vendor_stats and "lot_count" in vendor_stats:
                total_yield += vendor_stats["yield_rate"] * vendor_stats["lot_count"]
                total_count += vendor_stats["lot_count"]
        
        return total_yield / total_count if total_count > 0 else 0.0

    def has_data_conflicts(self) -> bool:
        """Check for data conflicts - Requirement 9.5"""
        return self.duplicate_mo_count > 0 or self.duplicate_lot_count > 0

    def get_data_quality_issues(self) -> Dict[str, int]:
        """Get summary of data quality issues"""
        return {
            "validation_errors": self.validation_errors_count,
            "duplicate_mo": self.duplicate_mo_count,
            "duplicate_lot": self.duplicate_lot_count,
            "missing_fields": sum(self.missing_fields.values()),
            "format_errors": sum(self.format_errors.values())
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "mo_processed_today": self.mo_processed_today,
            "lot_processed_today": self.lot_processed_today,
            "data_quality_score": self.data_quality_score,
            "validation_errors_count": self.validation_errors_count,
            "duplicate_mo_count": self.duplicate_mo_count,
            "duplicate_lot_count": self.duplicate_lot_count,
            "vendor_processing_stats": self.vendor_processing_stats,
            "reports_generated_today": self.reports_generated_today,
            "reports_pending": self.reports_pending,
            "avg_report_generation_time": self.avg_report_generation_time,
            "missing_fields": self.missing_fields,
            "format_errors": self.format_errors,
            "anomalous_mo_list": self.anomalous_mo_list,
            "anomalous_lot_list": self.anomalous_lot_list,
            "timestamp": self.timestamp.isoformat(),
            "overall_yield_rate": self.get_overall_yield_rate(),
            "has_data_conflicts": self.has_data_conflicts(),
            "data_quality_issues": self.get_data_quality_issues()
        }


@dataclass
class DashboardMetrics:
    """Main dashboard metrics container - Requirements 1, 2, 5, 6, 8, 9"""
    email_metrics: EmailMetrics = field(default_factory=EmailMetrics)
    celery_metrics: CeleryMetrics = field(default_factory=CeleryMetrics)
    system_metrics: SystemMetrics = field(default_factory=SystemMetrics)
    file_metrics: FileMetrics = field(default_factory=FileMetrics)
    business_metrics: BusinessMetrics = field(default_factory=BusinessMetrics)
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "email_metrics": self.email_metrics.to_dict(),
            "celery_metrics": self.celery_metrics.to_dict(),
            "system_metrics": self.system_metrics.to_dict(),
            "file_metrics": self.file_metrics.to_dict(),
            "business_metrics": self.business_metrics.to_dict(),
            "timestamp": self.timestamp.isoformat()
        }

    def get_critical_alerts(self) -> List[str]:
        """Get list of critical alerts across all metrics"""
        alerts = []
        
        # Email queue warnings
        if self.email_metrics.is_queue_warning():
            alerts.append(f"Email queue has {self.email_metrics.pending_count} pending items")
        
        # Celery queue warnings
        if self.celery_metrics.is_queue_critical():
            alerts.append(f"Celery queue has {self.celery_metrics.total_pending} pending tasks")
        
        # System resource warnings
        resource_warnings = self.system_metrics.is_resource_critical()
        for resource, is_critical in resource_warnings.items():
            if is_critical:
                alerts.append(f"System {resource.replace('_', ' ')}")
        
        # Storage warnings
        if self.file_metrics.is_storage_warning():
            alerts.append("Storage space exceeds warning threshold")
        
        # Data quality warnings
        if self.business_metrics.has_data_conflicts():
            alerts.append("Data conflicts detected in MO/LOT processing")
        
        return alerts


# Pydantic models for API validation
class EmailMetricsModel(BaseModel):
    """Pydantic model for email metrics validation"""
    pending_count: int = Field(ge=0, description="Number of pending emails")
    processing_count: int = Field(ge=0, description="Number of emails being processed")
    completed_count: int = Field(ge=0, description="Number of completed emails")
    failed_count: int = Field(ge=0, description="Number of failed emails")
    avg_processing_time_seconds: float = Field(ge=0, description="Average processing time in seconds")
    throughput_per_hour: float = Field(ge=0, description="Emails processed per hour")
    vendor_queue_counts: Dict[str, int] = Field(default_factory=dict)
    vendor_success_rates: Dict[str, float] = Field(default_factory=dict)
    code_comparison_active: int = Field(ge=0, description="Active code comparison tasks")
    code_comparison_pending: int = Field(ge=0, description="Pending code comparison tasks")
    code_comparison_avg_duration: float = Field(ge=0, description="Average code comparison duration")
    connection_status: str = Field(default="unknown", description="Email service connection status")
    connection_failures: int = Field(ge=0, description="Number of connection failures")

    @validator('vendor_success_rates')
    def validate_success_rates(cls, v):
        """Validate success rates are between 0 and 1"""
        for vendor, rate in v.items():
            if not 0 <= rate <= 1:
                raise ValueError(f"Success rate for {vendor} must be between 0 and 1")
        return v


class CeleryMetricsModel(BaseModel):
    """Pydantic model for Celery metrics validation"""
    total_active: int = Field(ge=0, description="Total active tasks")
    total_pending: int = Field(ge=0, description="Total pending tasks")
    total_completed: int = Field(ge=0, description="Total completed tasks")
    total_failed: int = Field(ge=0, description="Total failed tasks")
    task_type_counts: Dict[str, Dict[str, int]] = Field(default_factory=dict)
    worker_status: Dict[str, str] = Field(default_factory=dict)
    worker_load: Dict[str, int] = Field(default_factory=dict)
    avg_task_duration: Dict[str, float] = Field(default_factory=dict)
    task_success_rate: Dict[str, float] = Field(default_factory=dict)


class SystemMetricsModel(BaseModel):
    """Pydantic model for system metrics validation"""
    cpu_percent: float = Field(ge=0, le=100, description="CPU usage percentage")
    memory_percent: float = Field(ge=0, le=100, description="Memory usage percentage")
    disk_percent: float = Field(ge=0, le=100, description="Disk usage percentage")
    memory_available_mb: float = Field(ge=0, description="Available memory in MB")
    disk_free_gb: float = Field(ge=0, description="Free disk space in GB")
    active_connections: int = Field(ge=0, description="Number of active connections")
    websocket_connections: int = Field(ge=0, description="Number of WebSocket connections")
    service_health: Dict[str, str] = Field(default_factory=dict)
    database_connections: int = Field(ge=0, description="Number of database connections")
    database_query_avg_time: float = Field(ge=0, description="Average database query time")
    database_size_mb: float = Field(ge=0, description="Database size in MB")


class DashboardMetricsModel(BaseModel):
    """Pydantic model for complete dashboard metrics validation"""
    email_metrics: EmailMetricsModel
    celery_metrics: CeleryMetricsModel
    system_metrics: SystemMetricsModel
    timestamp: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }