"""
統一監控儀表板快取工具函數
提供快取操作的便利函數和裝飾器
"""

import asyncio
import functools
import hashlib
import logging
import time
from typing import Any, Callable, Dict, Optional, Union, List
from datetime import datetime, timedelta

from ..core.dashboard_cache_manager import get_cache_manager
from ..config.dashboard_cache_config import CacheNamespace


logger = logging.getLogger(__name__)


def cache_result(
    ttl: Optional[int] = None,
    key_prefix: Optional[str] = None,
    namespace: Optional[str] = None,
    serialize: bool = True,
    ignore_errors: bool = True
):
    """
    快取函數結果的裝飾器
    
    Args:
        ttl: 快取存活時間（秒）
        key_prefix: 快取鍵前綴
        namespace: 快取命名空間
        serialize: 是否序列化結果
        ignore_errors: 是否忽略快取錯誤
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache_manager = get_cache_manager()
            
            # 生成快取鍵
            cache_key = _generate_cache_key(func, args, kwargs, key_prefix, namespace)
            
            try:
                # 嘗試從快取獲取
                cached_result = cache_manager.cache_service.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"快取命中: {cache_key}")
                    return cached_result
                
                # 執行原函數
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 快取結果
                cache_manager.cache_service.set(cache_key, result, ttl)
                logger.debug(f"快取已設定: {cache_key}")
                
                return result
                
            except Exception as e:
                if ignore_errors:
                    logger.warning(f"快取操作失敗，執行原函數: {e}")
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                else:
                    raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            cache_manager = get_cache_manager()
            
            # 生成快取鍵
            cache_key = _generate_cache_key(func, args, kwargs, key_prefix, namespace)
            
            try:
                # 嘗試從快取獲取
                cached_result = cache_manager.cache_service.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"快取命中: {cache_key}")
                    return cached_result
                
                # 執行原函數
                result = func(*args, **kwargs)
                
                # 快取結果
                cache_manager.cache_service.set(cache_key, result, ttl)
                logger.debug(f"快取已設定: {cache_key}")
                
                return result
                
            except Exception as e:
                if ignore_errors:
                    logger.warning(f"快取操作失敗，執行原函數: {e}")
                    return func(*args, **kwargs)
                else:
                    raise
        
        # 根據函數類型返回對應的包裝器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def cache_metrics(ttl: int = 30, namespace: str = CacheNamespace.METRICS):
    """快取監控指標的裝飾器"""
    return cache_result(ttl=ttl, namespace=namespace)


def cache_business_data(ttl: int = 300, namespace: str = CacheNamespace.BUSINESS):
    """快取業務資料的裝飾器"""
    return cache_result(ttl=ttl, namespace=namespace)


def cache_system_info(ttl: int = 600, namespace: str = CacheNamespace.SYSTEM_INFO):
    """快取系統資訊的裝飾器"""
    return cache_result(ttl=ttl, namespace=namespace)


async def get_or_set_cache(
    key: str,
    callback: Callable,
    ttl: Optional[int] = None,
    force_refresh: bool = False
) -> Any:
    """
    獲取快取或設定快取的便利函數
    
    Args:
        key: 快取鍵
        callback: 獲取資料的回調函數
        ttl: 快取存活時間
        force_refresh: 是否強制重新整理
    """
    cache_manager = get_cache_manager()
    return await cache_manager.cache_with_callback(key, callback, ttl, force_refresh)


async def invalidate_cache_by_pattern(pattern: str) -> int:
    """根據模式失效快取"""
    cache_manager = get_cache_manager()
    return await cache_manager.invalidate_pattern(pattern)


async def invalidate_metrics_cache() -> int:
    """失效所有監控指標快取"""
    cache_manager = get_cache_manager()
    return await cache_manager.invalidate_namespace(CacheNamespace.METRICS)


async def invalidate_business_cache() -> int:
    """失效所有業務資料快取"""
    cache_manager = get_cache_manager()
    return await cache_manager.invalidate_namespace(CacheNamespace.BUSINESS)


async def warm_up_monitoring_cache():
    """預熱監控相關快取"""
    cache_manager = get_cache_manager()
    
    try:
        # 預載入系統資訊
        system_info = {
            'cache_warmed_at': datetime.now().isoformat(),
            'monitoring_services': ['email', 'celery', 'system', 'file']
        }
        await cache_manager.cache_system_info(system_info)
        
        logger.info("監控快取預熱完成")
        
    except Exception as e:
        logger.error(f"監控快取預熱失敗: {e}")


def get_cache_key_for_metrics(metric_type: str, timestamp: Optional[str] = None) -> str:
    """獲取監控指標的快取鍵"""
    cache_manager = get_cache_manager()
    return cache_manager.key_builder.build_metrics_key(metric_type, timestamp)


def get_cache_key_for_business(business_type: str, date: Optional[str] = None) -> str:
    """獲取業務資料的快取鍵"""
    cache_manager = get_cache_manager()
    return cache_manager.key_builder.build_business_key(business_type, date)


def get_cache_key_for_trend(metric_type: str, time_range: str) -> str:
    """獲取趨勢資料的快取鍵"""
    cache_manager = get_cache_manager()
    return cache_manager.key_builder.build_trend_key(metric_type, time_range)


class CacheContext:
    """快取上下文管理器"""
    
    def __init__(self, namespace: str, auto_invalidate: bool = False):
        """
        初始化快取上下文
        
        Args:
            namespace: 快取命名空間
            auto_invalidate: 退出時是否自動失效快取
        """
        self.namespace = namespace
        self.auto_invalidate = auto_invalidate
        self.cache_manager = get_cache_manager()
        self.cached_keys: List[str] = []
    
    async def __aenter__(self):
        """進入上下文"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.auto_invalidate:
            await self.invalidate_all()
    
    async def cache(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """在上下文中快取資料"""
        full_key = f"{self.namespace}:{key}"
        success = self.cache_manager.cache_service.set(full_key, value, ttl)
        if success:
            self.cached_keys.append(full_key)
        return success
    
    async def get(self, key: str, default: Any = None) -> Any:
        """從上下文快取獲取資料"""
        full_key = f"{self.namespace}:{key}"
        return self.cache_manager.cache_service.get(full_key, default)
    
    async def invalidate_all(self) -> int:
        """失效上下文中的所有快取"""
        count = 0
        for key in self.cached_keys:
            if self.cache_manager.cache_service.delete(key):
                count += 1
        
        self.cached_keys.clear()
        return count


class CacheBatch:
    """批量快取操作"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.operations: List[Dict[str, Any]] = []
    
    def add_set(self, key: str, value: Any, ttl: Optional[int] = None):
        """添加設定操作"""
        self.operations.append({
            'type': 'set',
            'key': key,
            'value': value,
            'ttl': ttl
        })
    
    def add_delete(self, key: str):
        """添加刪除操作"""
        self.operations.append({
            'type': 'delete',
            'key': key
        })
    
    async def execute(self) -> Dict[str, int]:
        """執行批量操作"""
        results = {'success': 0, 'failed': 0}
        
        for op in self.operations:
            try:
                if op['type'] == 'set':
                    success = self.cache_manager.cache_service.set(
                        op['key'], op['value'], op.get('ttl')
                    )
                elif op['type'] == 'delete':
                    success = self.cache_manager.cache_service.delete(op['key'])
                else:
                    success = False
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                logger.error(f"批量快取操作失敗 {op}: {e}")
                results['failed'] += 1
        
        self.operations.clear()
        return results


def _generate_cache_key(
    func: Callable,
    args: tuple,
    kwargs: dict,
    key_prefix: Optional[str] = None,
    namespace: Optional[str] = None
) -> str:
    """生成快取鍵"""
    # 基本鍵
    func_name = f"{func.__module__}.{func.__name__}"
    
    # 參數雜湊
    args_str = str(args) + str(sorted(kwargs.items()))
    args_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
    
    # 組合鍵
    parts = []
    
    if namespace:
        parts.append(namespace)
    
    if key_prefix:
        parts.append(key_prefix)
    
    parts.extend([func_name, args_hash])
    
    return ":".join(parts)


def get_cache_statistics() -> Dict[str, Any]:
    """獲取快取統計資料"""
    cache_manager = get_cache_manager()
    return cache_manager.get_cache_statistics()


def get_cache_health() -> Dict[str, Any]:
    """獲取快取健康狀態"""
    cache_manager = get_cache_manager()
    return cache_manager.get_cache_health()


async def optimize_cache_memory():
    """最佳化快取記憶體使用"""
    cache_manager = get_cache_manager()
    
    try:
        # 清理過期項目
        cache_manager.cache_service._cleanup_expired()
        
        # 檢查記憶體壓力
        cache_manager.cache_service._check_memory_usage()
        
        logger.info("快取記憶體最佳化完成")
        
    except Exception as e:
        logger.error(f"快取記憶體最佳化失敗: {e}")


class CacheMetrics:
    """快取指標收集器"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
    
    def collect_metrics(self) -> Dict[str, Any]:
        """收集快取指標"""
        stats = self.cache_manager.get_cache_statistics()
        health = self.cache_manager.get_cache_health()
        
        return {
            'cache_statistics': stats,
            'cache_health': health,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """獲取效能指標"""
        stats = self.cache_manager.cache_service.get_statistics()
        
        return {
            'hit_rate': stats.hit_rate,
            'avg_access_time_ms': stats.avg_access_time_ms,
            'memory_usage_mb': stats.memory_usage_mb,
            'total_entries': float(stats.total_entries),
            'eviction_rate': stats.eviction_count / max(1, stats.hit_count + stats.miss_count)
        }