"""FastAPI 依賴性注入
集中管理所有服務實例和全域狀態
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import HTTPEx<PERSON>, Depends

from loguru import logger

# 導入所有服務
try:
    from ...services.staging import get_file_staging_service, FileStagingService
    from ...services.processing import get_file_processing_service, FileProcessingService
    from ...services.product_search_service import ProductSearchService
    from ...services.llm_search_service import LLMSearchService
except ImportError:
    # 回退處理
    get_file_staging_service = None
    get_file_processing_service = None
    FileStagingService = None
    FileProcessingService = None
    ProductSearchService = None
    LLMSearchService = None


class APIState:
    """API 全域狀態管理"""
    
    def __init__(self):
        # 網路連接狀態
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        
        # 任務快取
        self.task_cache: Dict[str, Any] = {}
        
        # 系統統計
        self.system_stats = {
            "startup_time": datetime.now(),
            "request_count": 0,
            "error_count": 0
        }
        
        logger.info("API 狀態管理器已初始化")
    
    def add_connection(self, connection_key: str, connection_info: Dict[str, Any]):
        """添加網路連接"""
        self.active_connections[connection_key] = {
            **connection_info,
            "connected_at": datetime.now(),
            "last_used": datetime.now()
        }
        logger.info(f"添加網路連接: {connection_key}")
    
    def get_connection(self, connection_key: str) -> Optional[Dict[str, Any]]:
        """獲取網路連接"""
        connection = self.active_connections.get(connection_key)
        if connection:
            connection["last_used"] = datetime.now()
        return connection
    
    def remove_connection(self, connection_key: str) -> bool:
        """移除網路連接"""
        if connection_key in self.active_connections:
            del self.active_connections[connection_key]
            logger.info(f"移除網路連接: {connection_key}")
            return True
        return False
    
    def increment_request_count(self):
        """增加請求計數"""
        self.system_stats["request_count"] += 1
    
    def increment_error_count(self):
        """增加錯誤計數"""
        self.system_stats["error_count"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取系統統計"""
        uptime = datetime.now() - self.system_stats["startup_time"]
        return {
            **self.system_stats,
            "uptime_seconds": uptime.total_seconds(),
            "active_connections_count": len(self.active_connections),
            "cached_tasks_count": len(self.task_cache)
        }


class ServiceContainer:
    """服務容器，管理所有服務實例"""
    
    def __init__(self):
        self._staging_service = None
        self._processing_service = None
        self._product_search_service = None
        self._llm_search_service = None
        self._initialization_errors = {}
        
        logger.info("服務容器已初始化")
    
    def get_staging_service(self) -> Optional[FileStagingService]:
        """獲取檔案暫存服務（單例）"""
        if self._staging_service is None and get_file_staging_service is not None:
            try:
                self._staging_service = get_file_staging_service()
                logger.info("檔案暫存服務已初始化")
                # 清除之前的錯誤記錄
                self._initialization_errors.pop('staging', None)
            except Exception as e:
                error_msg = f"初始化檔案暫存服務失敗: {e}"
                logger.error(error_msg)
                self._initialization_errors['staging'] = error_msg
        return self._staging_service
    
    def get_processing_service(self) -> Optional[FileProcessingService]:
        """獲取檔案處理服務（單例）"""
        if self._processing_service is None and get_file_processing_service is not None:
            try:
                self._processing_service = get_file_processing_service()
                logger.info("檔案處理服務已初始化")
                # 清除之前的錯誤記錄
                self._initialization_errors.pop('processing', None)
            except Exception as e:
                error_msg = f"初始化檔案處理服務失敗: {e}"
                logger.error(error_msg)
                self._initialization_errors['processing'] = error_msg
        return self._processing_service
    
    def get_product_search_service(self) -> Optional[ProductSearchService]:
        """獲取產品搜尋服務（單例）"""
        if self._product_search_service is None and ProductSearchService is not None:
            try:
                self._product_search_service = ProductSearchService(
                    max_workers=4, 
                    search_timeout=300
                )
                logger.info("產品搜尋服務已初始化")
            except Exception as e:
                error_msg = f"初始化產品搜尋服務失敗: {e}"
                logger.error(error_msg)
                self._initialization_errors['product_search'] = error_msg
        return self._product_search_service
    
    def get_initialization_errors(self) -> Dict[str, str]:
        """獲取初始化錯誤訊息"""
        return self._initialization_errors.copy()
    
    def reset_service(self, service_name: str) -> bool:
        """重置指定服務（用於錯誤恢復）"""
        service_map = {
            'staging': '_staging_service',
            'processing': '_processing_service', 
            'product_search': '_product_search_service',
            'llm_search': '_llm_search_service'
        }
        
        if service_name in service_map:
            setattr(self, service_map[service_name], None)
            self._initialization_errors.pop(service_name, None)
            logger.info(f"已重置服務: {service_name}")
            return True
        return False
    
    def get_service_status(self) -> Dict[str, Any]:
        """獲取所有服務的狀態"""
        return {
            'staging_service': {
                'available': self._staging_service is not None,
                'error': self._initialization_errors.get('staging')
            },
            'processing_service': {
                'available': self._processing_service is not None,
                'error': self._initialization_errors.get('processing')
            },
            'product_search_service': {
                'available': self._product_search_service is not None,
                'error': self._initialization_errors.get('product_search')
            },
            'llm_search_service': {
                'available': self._llm_search_service is not None,
                'error': self._initialization_errors.get('llm_search')
            }
        }
    
    def get_llm_search_service(self) -> Optional[LLMSearchService]:
        """獲取 LLM 搜尋服務（單例）"""
        if self._llm_search_service is None and LLMSearchService is not None:
            try:
                product_service = self.get_product_search_service()
                if product_service:
                    self._llm_search_service = LLMSearchService(product_service)
                    logger.info("LLM 搜尋服務已初始化")
                    # 清除之前的錯誤記錄
                    self._initialization_errors.pop('llm_search', None)
            except Exception as e:
                error_msg = f"初始化 LLM 搜尋服務失敗: {e}"
                logger.error(error_msg)
                self._initialization_errors['llm_search'] = error_msg
        return self._llm_search_service


# 全域實例
_api_state = APIState()
_service_container = ServiceContainer()


# FastAPI 依賴函數
def get_api_state() -> APIState:
    """獲取 API 狀態管理器"""
    return _api_state


def get_service_container() -> ServiceContainer:
    """獲取服務容器"""
    return _service_container


def get_staging_service() -> Optional[FileStagingService]:
    """獲取檔案暫存服務"""
    return _service_container.get_staging_service()


def get_processing_service() -> Optional[FileProcessingService]:
    """獲取檔案處理服務"""
    return _service_container.get_processing_service()


def get_product_search_service() -> Optional[ProductSearchService]:
    """獲取產品搜尋服務"""
    return _service_container.get_product_search_service()


def get_llm_search_service() -> Optional[LLMSearchService]:
    """獲取 LLM 搜尋服務"""
    return _service_container.get_llm_search_service()


# 中介軟體依賴
async def track_request_stats(api_state: APIState = get_api_state()):
    """追蹤請求統計"""
    api_state.increment_request_count()
    return api_state


# 驗證依賴
def validate_service_availability():
    """驗證服務可用性"""
    def _validate():
        container = get_service_container()
        
        # 檢查關鍵服務
        staging_service = container.get_staging_service()
        processing_service = container.get_processing_service()
        
        if staging_service is None:
            logger.warning("檔案暫存服務不可用")
        
        if processing_service is None:
            logger.warning("檔案處理服務不可用")
        
        return {
            "staging_available": staging_service is not None,
            "processing_available": processing_service is not None,
            "search_available": container.get_product_search_service() is not None,
            "llm_available": container.get_llm_search_service() is not None
        }
    
    return _validate


# 高級依賴注入函數（已移至文件末尾的統一錯誤處理區域）


# 錯誤處理依賴
async def handle_service_errors(api_state: APIState = get_api_state()):
    """處理服務錯誤"""
    try:
        yield
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"服務錯誤: {e}")
        raise


# 組合依賴注入
class ServiceDependencies:
    """組合多個服務的依賴注入"""
    
    def __init__(
        self,
        staging_service: Optional[FileStagingService] = None,
        processing_service: Optional[FileProcessingService] = None,
        product_search_service: Optional[ProductSearchService] = None,
        llm_search_service: Optional[LLMSearchService] = None,
        api_state: Optional[APIState] = None
    ):
        self.staging = staging_service
        self.processing = processing_service
        self.product_search = product_search_service
        self.llm_search = llm_search_service
        self.api_state = api_state
    
    def has_staging(self) -> bool:
        return self.staging is not None
    
    def has_processing(self) -> bool:
        return self.processing is not None
    
    def has_search(self) -> bool:
        return self.product_search is not None
    
    def get_available_services(self) -> List[str]:
        """獲取可用服務列表"""
        services = []
        if self.has_staging():
            services.append('staging')
        if self.has_processing():
            services.append('processing')
        if self.has_search():
            services.append('search')
        if self.llm_search is not None:
            services.append('llm_search')
        return services


def get_all_services(
    staging_service: Optional[FileStagingService] = Depends(get_staging_service),
    processing_service: Optional[FileProcessingService] = Depends(get_processing_service),
    product_search_service: Optional[ProductSearchService] = Depends(get_product_search_service),
    llm_search_service: Optional[LLMSearchService] = Depends(get_llm_search_service),
    api_state: APIState = Depends(get_api_state)
) -> ServiceDependencies:
    """獲取所有服務的組合依賴注入"""
    return ServiceDependencies(
        staging_service=staging_service,
        processing_service=processing_service,
        product_search_service=product_search_service,
        llm_search_service=llm_search_service,
        api_state=api_state
    )


# ================================
# 統一錯誤處理機制（已拆分到獨立模組）
# ================================

# 導入統一錯誤處理模組
from .error_handling import (
    StagingServiceUnavailableError,
    ProcessingServiceUnavailableError,
    ErrorLogger
)

# ================================
# 更新的依賴注入函數（使用統一錯誤處理）
# ================================

def require_staging_service() -> FileStagingService:
    """必需檔案暫存服務的依賴注入（使用統一錯誤處理）

    Returns:
        FileStagingService: 檔案暫存服務實例

    Raises:
        StagingServiceUnavailableError: 當服務不可用時
    """
    service = get_staging_service()
    if service is None:
        container = get_service_container()
        errors = container.get_initialization_errors()
        error_details = errors.get('staging', '服務初始化失敗')

        # 使用統一錯誤處理
        error = StagingServiceUnavailableError(details=error_details)
        logger.error(f"暫存服務不可用: {error_details}")
        raise error.to_http_exception()

    return service


def require_processing_service() -> FileProcessingService:
    """必需檔案處理服務的依賴注入（使用統一錯誤處理）

    Returns:
        FileProcessingService: 檔案處理服務實例

    Raises:
        ProcessingServiceUnavailableError: 當服務不可用時
    """
    service = get_processing_service()
    if service is None:
        container = get_service_container()
        errors = container.get_initialization_errors()
        error_details = errors.get('processing', '服務初始化失敗')

        # 使用統一錯誤處理
        error = ProcessingServiceUnavailableError(details=error_details)
        logger.error(f"處理服務不可用: {error_details}")
        raise error.to_http_exception()

    return service
