"""依賴注入測試的共用配置和 Fixtures

提供測試所需的共用設置、Mock 對象和測試工具
使用新的測試基礎設施
"""

import pytest
from unittest.mock import Mock, MagicMock
from fastapi import FastAPI
from fastapi.testclient import TestClient

# 導入新的測試基礎設施
from .shared.test_infrastructure import (
    TestClientFactory, MockServiceFactory, DependencyOverrideManager, TestDataFactory
)


@pytest.fixture
def mock_staging_service():
    """提供 Mock 暫存服務實例 - 使用新的工廠"""
    return MockServiceFactory.create_staging_service(
        service_id="test_staging_service",
        is_healthy=True
    )


@pytest.fixture
def mock_processing_service():
    """提供 Mock 處理服務實例 - 使用新的工廠"""
    return MockServiceFactory.create_processing_service(
        service_id="test_processing_service",
        is_healthy=True
    )


@pytest.fixture
def mock_service_container():
    """提供 Mock 服務容器 - 使用新的工廠"""
    return MockServiceFactory.create_service_container()


@pytest.fixture
def test_app():
    """提供測試用的 FastAPI 應用"""
    app = FastAPI(title="Test App for Dependency Injection")
    return app


@pytest.fixture
def test_client(test_app):
    """提供測試客戶端 - 使用新的工廠"""
    return TestClientFactory.create_client(test_app)


@pytest.fixture
def mock_api_state():
    """提供 Mock API 狀態管理器 - 使用新的工廠"""
    return MockServiceFactory.create_api_state(
        initial_request_count=100,
        initial_error_count=5
    )


# 測試用的常數和配置
TEST_TASK_ID = "test-task-12345"
TEST_PRODUCT_NAME = "TestProduct"
TEST_SOURCE_FILES = [
    "/test/path/file1.txt",
    "/test/path/file2.csv"
]

# 測試用的錯誤訊息
ERROR_MESSAGES = {
    "staging_unavailable": "檔案暫存服務不可用",
    "processing_unavailable": "檔案處理服務不可用",
    "service_init_failed": "服務初始化失敗",
    "database_connection_failed": "連接資料庫失敗",
    "llm_service_timeout": "LLM 服務連接超時"
}


@pytest.fixture
def error_messages():
    """提供測試用的錯誤訊息"""
    return ERROR_MESSAGES


# 測試輔助函數
def assert_http_exception(exc_info, expected_status_code, expected_message_contains):
    """驗證 HTTPException 的輔助函數"""
    assert exc_info.value.status_code == expected_status_code
    assert expected_message_contains in str(exc_info.value.detail)


def create_mock_dependency_override(app, dependency_func, mock_return_value):
    """創建依賴覆蓋的輔助函數 - 使用新的管理器"""
    manager = DependencyOverrideManager(app)
    app.dependency_overrides[dependency_func] = lambda: mock_return_value
    return app


@pytest.fixture
def dependency_override_manager(test_app):
    """提供依賴覆蓋管理器"""
    return DependencyOverrideManager(test_app)


@pytest.fixture
def test_data_factory():
    """提供測試數據工廠"""
    return TestDataFactory()
