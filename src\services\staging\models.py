"""檔案暫存服務 - 資料模型
包含所有與暫存相關的資料類別、列舉和例外類別
"""

import asyncio
from pathlib import Path
from typing import List, Optional
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum


class StagingStatus(str, Enum):
    """暫存狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CLEANING = "cleaning"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class StagingError(Exception):
    """暫存相關錯誤基類"""
    pass


class InsufficientSpaceError(StagingError):
    """磁碟空間不足錯誤"""
    pass


class FileIntegrityError(StagingError):
    """檔案完整性驗證錯誤"""
    pass


class StagingPermissionError(StagingError):
    """暫存權限錯誤"""
    pass


class StagingTimeoutError(StagingError):
    """暫存超時錯誤"""
    pass


class StagingConcurrencyError(StagingError):
    """暫存並發錯誤"""
    pass


class TaskCancellationError(StagingError):
    """任務取消錯誤"""
    pass


@dataclass
class StagingFileInfo:
    """暫存檔案資訊"""
    source_path: Path
    target_path: Path
    size: int
    checksum: Optional[str] = None
    copied: bool = False
    verified: bool = False
    error_message: Optional[str] = None
    retry_count: int = 0
    last_retry_at: Optional[datetime] = None


@dataclass
class StagingTask:
    """暫存任務"""
    task_id: str
    product_name: str
    source_files: List[Path]
    staging_directory: Path
    status: StagingStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    file_infos: List[StagingFileInfo] = field(default_factory=list)
    total_size: int = 0
    copied_size: int = 0
    progress: float = 0.0
    error_message: Optional[str] = None
    preserve_structure: bool = True
    verify_integrity: bool = True
    retry_count: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0  # seconds
    timeout: Optional[float] = None  # 任務超時時間（秒）
    cancelled: bool = False
    cancel_event: Optional[asyncio.Event] = field(default_factory=lambda: asyncio.Event())
    
    # 監控資料
    peak_memory_usage: int = 0
    cpu_time: float = 0.0
    io_operations: int = 0
    network_bytes: int = 0


@dataclass
class StagingResult:
    """暫存結果"""
    success: bool
    task_id: str
    staging_directory: Path
    staged_files: List[Path]
    total_files: int
    total_size: int
    staging_duration: float
    error_message: Optional[str] = None
    failed_files: List[str] = field(default_factory=list)
    integrity_check_passed: bool = True
    retries_used: int = 0
    
    # 效能統計
    average_speed: float = 0.0  # MB/s
    peak_memory: int = 0
    cpu_usage: float = 0.0
