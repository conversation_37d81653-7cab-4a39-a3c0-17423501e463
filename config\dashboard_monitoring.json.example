{"env": "production", "alert_thresholds": {"email_pending_warning": 15, "email_pending_critical": 75, "email_processing_time_warning": 600, "email_processing_time_critical": 1800, "email_failure_rate_warning": 0.15, "email_failure_rate_critical": 0.3, "celery_pending_warning": 30, "celery_pending_critical": 150, "celery_failure_rate_warning": 0.15, "celery_failure_rate_critical": 0.3, "celery_task_timeout_warning": 2400, "celery_task_timeout_critical": 4800, "cpu_warning": 85.0, "cpu_critical": 98.0, "memory_warning": 90.0, "memory_critical": 98.0, "disk_warning": 90.0, "disk_critical": 98.0, "temp_size_warning": 2000, "temp_size_critical": 8000, "file_processing_time_warning": 900, "file_processing_time_critical": 2700, "db_query_time_warning": 8.0, "db_query_time_critical": 15.0, "db_connection_warning": 85, "db_connection_critical": 98}, "update_intervals": {"metrics_collection": 20, "alerts_evaluation": 5, "trends_analysis": 180, "websocket_heartbeat": 25, "database_cleanup": 7200}, "retention_policies": {"metrics_history": 60, "alerts_history": 180, "task_execution_history": 14, "system_health_checks": 30, "file_processing_stats": 60}, "websocket_config": {"max_connections": 200, "heartbeat_interval": 25, "connection_timeout": 600, "message_queue_size": 2000, "enable_compression": true}, "display_config": {"default_time_range": "6h", "max_chart_data_points": 200, "refresh_interval": 3, "enable_dark_theme": true, "show_debug_info": false}, "notification_config": {"enabled_channels": ["system", "email", "line"], "email_smtp_server": "smtp.company.com", "email_smtp_port": 587, "email_username": "<EMAIL>", "email_password": "your_email_password", "email_recipients": ["<EMAIL>", "<EMAIL>"], "line_token": "your_line_token", "webhook_urls": ["https://hooks.company.com/monitoring"], "alert_cooldown": 180}, "database_config": {"db_path": "outlook.db", "connection_pool_size": 20, "connection_timeout": 60, "query_timeout": 60, "enable_wal_mode": true, "backup_enabled": true, "backup_interval": 43200}, "security_config": {"enable_authentication": true, "api_key": "your_secure_api_key", "allowed_ips": ["127.0.0.1", "***********/24"], "enable_rate_limiting": true, "rate_limit_requests": 200, "enable_cors": true, "cors_origins": ["https://monitoring.company.com"]}}