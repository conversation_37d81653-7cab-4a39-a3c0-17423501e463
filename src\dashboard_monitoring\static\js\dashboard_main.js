/**
 * 統一監控儀表板 - 主要功能
 * 負責整合所有儀表板功能和用戶互動
 */

class DashboardMain {
    constructor() {
        this.websocket = null;
        this.charts = null;
        this.refreshInterval = null;
        this.isInitialized = false;
        this.currentData = {
            email: {},
            celery: {},
            system: {},
            file: {},
            business: {},
            alerts: []
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('初始化統一監控儀表板...');
            
            // 顯示載入畫面
            showLoadingOverlay('初始化儀表板...');
            
            // 初始化 WebSocket 連接
            this.websocket = initDashboardWebSocket();
            
            // 初始化圖表系統
            this.charts = initDashboardCharts();
            
            // 設置事件監聽器
            this.setupEventListeners();
            
            // 設置 WebSocket 訊息處理器
            this.setupWebSocketHandlers();
            
            // 初始載入資料
            await this.loadInitialData();
            
            // 設置定期刷新
            this.setupRefreshInterval();
            
            this.isInitialized = true;
            console.log('✅ 統一監控儀表板初始化完成');
            
        } catch (error) {
            console.error('❌ 儀表板初始化失敗:', error);
            this.showError('儀表板初始化失敗，請重新整理頁面');
        }
    }
    
    setupEventListeners() {
        // 監聽 WebSocket 指標更新事件
        window.addEventListener('metricsUpdate', (event) => {
            this.handleMetricsUpdate(event.detail);
        });
        
        // 監聽新告警事件
        window.addEventListener('newAlert', (event) => {
            this.handleNewAlert(event.detail);
        });
        
        // 監聽頁面可見性變化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                // 頁面重新可見時刷新資料
                this.refreshAllData();
            }
        });
        
        // 設置刷新按鈕事件
        this.setupRefreshButtons();
        
        // 設置面板展開/收縮功能
        this.setupPanelControls();
    }
    
    setupWebSocketHandlers() {
        if (!this.websocket) return;
        
        // 設置指標更新處理器
        this.websocket.addMessageHandler('metrics_update', (data) => {
            this.updateAllMetrics(data);
        });
        
        // 設置告警處理器
        this.websocket.addMessageHandler('alert', (alert) => {
            this.addAlert(alert);
        });
        
        // 設置系統狀態處理器
        this.websocket.addMessageHandler('system_status', (status) => {
            this.updateSystemStatus(status);
        });
    }
    
    async loadInitialData() {
        try {
            // 載入當前指標
            const response = await fetch('/dashboard/api/metrics/current');
            if (response.ok) {
                const result = await response.json();
                if (result.status === 'success') {
                    this.updateAllMetrics(result.data);
                }
            }
            
            // 載入活躍告警
            const alertResponse = await fetch('/dashboard/api/alerts/active');
            if (alertResponse.ok) {
                const alertResult = await alertResponse.json();
                if (alertResult.status === 'success') {
                    this.currentData.alerts = alertResult.data;
                    this.charts.updateAlertStats(alertResult.data);
                }
            }
            
        } catch (error) {
            console.error('載入初始資料失敗:', error);
        }
    }
    
    setupRefreshInterval() {
        // 每30秒自動刷新一次（如果 WebSocket 未連接）
        this.refreshInterval = setInterval(() => {
            if (!this.websocket?.isConnected) {
                this.refreshAllData();
            }
        }, 30000);
    }
    
    setupRefreshButtons() {
        // 全域刷新按鈕
        const refreshButtons = document.querySelectorAll('.refresh-btn');
        refreshButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.refreshAllData();
            });
        });
    }
    
    setupPanelControls() {
        // 面板展開/收縮按鈕
        const expandButtons = document.querySelectorAll('.expand-btn');
        expandButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const panel = btn.closest('.monitoring-panel');
                if (panel) {
                    panel.classList.toggle('expanded');
                    btn.textContent = panel.classList.contains('expanded') ? '⛶' : '⛶';
                }
            });
        });
    }
    
    handleMetricsUpdate(metrics) {
        console.log('收到指標更新:', metrics);
        this.updateAllMetrics(metrics);
    }
    
    handleNewAlert(alert) {
        console.log('收到新告警:', alert);
        this.addAlert(alert);
    }
    
    updateAllMetrics(data) {
        try {
            // 更新概覽卡片
            this.updateOverviewCards(data);
            
            // 更新詳細監控面板
            this.updateDetailedPanels(data);
            
            // 儲存當前資料
            this.currentData = { ...this.currentData, ...data };
            
            // 隱藏載入畫面
            hideLoadingOverlay();
            
        } catch (error) {
            console.error('更新指標失敗:', error);
        }
    }
    
    updateOverviewCards(data) {
        // 更新郵件處理概覽
        if (data.email) {
            this.updateElement('emailPending', data.email.pending || 0);
            this.updateElement('emailProcessing', data.email.processing || 0);
            this.updateElement('emailCompleted', data.email.completed || 0);
            this.updateElement('emailFailed', data.email.failed || 0);
            this.updateCardStatus('emailStatus', this.getEmailStatus(data.email));
        }
        
        // 更新 Celery 任務概覽
        if (data.celery) {
            this.updateElement('celeryActive', data.celery.total_active || 0);
            this.updateElement('celeryPending', data.celery.total_pending || 0);
            this.updateElement('celeryWorkers', Object.keys(data.celery.worker_status || {}).length);
            this.updateElement('celerySuccessRate', this.calculateSuccessRate(data.celery));
            this.updateCardStatus('celeryStatus', this.getCeleryStatus(data.celery));
        }
        
        // 更新系統資源概覽
        if (data.system) {
            this.updateElement('systemCpu', `${Math.round(data.system.cpu_percent || 0)}%`);
            this.updateElement('systemMemory', `${Math.round(data.system.memory_percent || 0)}%`);
            this.updateElement('systemDisk', `${Math.round(data.system.disk_percent || 0)}%`);
            this.updateElement('systemConnections', data.system.active_connections || 0);
            this.updateCardStatus('systemResourceStatus', this.getSystemStatus(data.system));
        }
        
        // 更新業務指標概覽
        if (data.business) {
            this.updateElement('businessMO', data.business.mo_processed_today || 0);
            this.updateElement('businessLOT', data.business.lot_processed_today || 0);
            this.updateElement('businessQuality', Math.round(data.business.data_quality_score || 0));
            this.updateElement('businessReports', data.business.reports_generated_today || 0);
            this.updateCardStatus('businessStatus', this.getBusinessStatus(data.business));
        }
    }
    
    updateDetailedPanels(data) {
        // 更新廠商統計
        if (data.email) {
            this.charts.updateVendorStats(data.email);
            
            // 更新 Code Comparison 統計
            this.updateElement('codeComparisonActive', data.email.code_comparison_active || 0);
            this.updateElement('codeComparisonPending', data.email.code_comparison_pending || 0);
            this.updateElement('codeComparisonAvgTime', `${Math.round(data.email.code_comparison_avg_duration || 0)}s`);
        }
        
        // 更新 Celery 任務詳細統計
        if (data.celery) {
            this.charts.updateTaskTypeStats(data.celery);
            this.charts.updateWorkerStats(data.celery);
        }
        
        // 更新系統資源詳細統計
        if (data.system) {
            this.charts.updateProgressBar('cpu', data.system.cpu_percent || 0);
            this.charts.updateProgressBar('memory', data.system.memory_percent || 0);
            this.charts.updateProgressBar('disk', data.system.disk_percent || 0);
            this.charts.updateServiceHealth(data.system);
        }
        
        // 更新檔案處理統計
        if (data.file) {
            this.charts.updateFileStats(data.file);
        }
    }
    
    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
            
            // 添加更新動畫
            element.classList.add('updated');
            setTimeout(() => {
                element.classList.remove('updated');
            }, 500);
        }
    }
    
    updateCardStatus(elementId, status) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = status.text;
            element.className = `card-status ${status.level}`;
        }
    }
    
    addAlert(alert) {
        // 添加到告警列表
        this.currentData.alerts.unshift(alert);
        
        // 限制告警列表長度
        if (this.currentData.alerts.length > 100) {
            this.currentData.alerts = this.currentData.alerts.slice(0, 100);
        }
        
        // 更新告警統計
        this.charts.updateAlertStats(this.currentData.alerts);
    }
    
    async refreshAllData() {
        try {
            showLoadingOverlay('刷新資料中...');
            
            // 重新載入所有資料
            await this.loadInitialData();
            
            console.log('資料刷新完成');
            
        } catch (error) {
            console.error('刷新資料失敗:', error);
            this.showError('資料刷新失敗');
        } finally {
            hideLoadingOverlay();
        }
    }
    
    // 狀態計算方法
    getEmailStatus(emailData) {
        const failed = emailData.failed || 0;
        const total = (emailData.pending || 0) + (emailData.processing || 0) + (emailData.completed || 0) + failed;
        
        if (total === 0) return { text: '無資料', level: 'info' };
        
        const failureRate = failed / total;
        if (failureRate > 0.1) return { text: '異常', level: 'error' };
        if (failureRate > 0.05) return { text: '警告', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getCeleryStatus(celeryData) {
        const pending = celeryData.total_pending || 0;
        const workers = Object.keys(celeryData.worker_status || {}).length;
        
        if (workers === 0) return { text: '無工作者', level: 'error' };
        if (pending > 100) return { text: '佇列過載', level: 'error' };
        if (pending > 20) return { text: '佇列繁忙', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getSystemStatus(systemData) {
        const cpu = systemData.cpu_percent || 0;
        const memory = systemData.memory_percent || 0;
        const disk = systemData.disk_percent || 0;
        
        if (cpu > 95 || memory > 95 || disk > 95) return { text: '嚴重', level: 'error' };
        if (cpu > 80 || memory > 85 || disk > 85) return { text: '警告', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getBusinessStatus(businessData) {
        const quality = businessData.data_quality_score || 0;
        
        if (quality < 70) return { text: '品質差', level: 'error' };
        if (quality < 85) return { text: '品質一般', level: 'warning' };
        return { text: '品質良好', level: 'success' };
    }
    
    calculateSuccessRate(celeryData) {
        const taskTypeCounts = celeryData.task_type_counts || {};
        let totalCompleted = 0;
        let totalFailed = 0;
        
        Object.values(taskTypeCounts).forEach(counts => {
            totalCompleted += counts.completed || 0;
            totalFailed += counts.failed || 0;
        });
        
        const total = totalCompleted + totalFailed;
        if (total === 0) return '0%';
        
        return `${Math.round((totalCompleted / total) * 100)}%`;
    }
    
    showError(message) {
        // 顯示錯誤訊息
        const alertBanner = document.getElementById('alertBanner');
        const alertContent = document.getElementById('alertContent');
        
        if (alertBanner && alertContent) {
            alertContent.innerHTML = `<strong>錯誤</strong><span>${message}</span>`;
            alertBanner.className = 'alert-banner error';
            alertBanner.style.display = 'block';
            
            // 5秒後自動隱藏
            setTimeout(() => {
                alertBanner.style.display = 'none';
            }, 5000);
        }
    }
    
    destroy() {
        // 清理資源
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.websocket) {
            this.websocket.close();
        }
        
        if (this.charts) {
            this.charts.destroy();
        }
    }
}

// 全域功能函數
function refreshEmailData() {
    console.log('刷新郵件資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshCeleryData() {
    console.log('刷新 Celery 資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshSystemData() {
    console.log('刷新系統資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshFileData() {
    console.log('刷新檔案資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshAlertData() {
    console.log('刷新告警資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function expandPanel(panelType) {
    console.log(`展開 ${panelType} 面板`);
    const panel = document.querySelector(`.${panelType}-panel`);
    if (panel) {
        panel.classList.toggle('expanded');
    }
}

async function acknowledgeAlert(alertId) {
    try {
        const response = await fetch(`/dashboard/api/alerts/${alertId}/acknowledge`, {
            method: 'POST'
        });
        
        if (response.ok) {
            console.log(`告警 ${alertId} 已確認`);
            // 刷新告警資料
            refreshAlertData();
        } else {
            console.error('確認告警失敗');
        }
    } catch (error) {
        console.error('確認告警錯誤:', error);
    }
}

function dismissAlert(alertId) {
    console.log(`忽略告警 ${alertId}`);
    // 從 DOM 中移除告警項目
    const alertItem = document.querySelector(`[data-alert-id="${alertId}"]`);
    if (alertItem) {
        alertItem.style.display = 'none';
    }
}

function clearAllAlerts() {
    console.log('清除所有告警');
    const alertList = document.getElementById('alertList');
    if (alertList) {
        alertList.innerHTML = '<div class="no-alerts">所有告警已清除</div>';
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM 載入完成，初始化儀表板...');
    
    // 創建全域儀表板實例
    window.dashboardMain = new DashboardMain();
    
    // 設置全域錯誤處理
    window.addEventListener('error', (event) => {
        console.error('全域錯誤:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未處理的 Promise 拒絕:', event.reason);
    });
});

// 頁面卸載時清理資源
window.addEventListener('beforeunload', () => {
    if (window.dashboardMain) {
        window.dashboardMain.destroy();
    }
});

// 導出到全域作用域
window.DashboardMain = DashboardMain;
window.refreshEmailData = refreshEmailData;
window.refreshCeleryData = refreshCeleryData;
window.refreshSystemData = refreshSystemData;
window.refreshFileData = refreshFileData;
window.refreshAlertData = refreshAlertData;
window.expandPanel = expandPanel;
window.acknowledgeAlert = acknowledgeAlert;
window.dismissAlert = dismissAlert;
window.clearAllAlerts = clearAllAlerts;