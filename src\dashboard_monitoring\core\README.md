# 核心服務層文檔

## 📋 概述

核心服務層包含統一監控儀表板的所有核心業務邏輯，提供監控協調、資料收集、告警處理、趨勢分析和 WebSocket 管理等核心功能。

## 📁 模組結構

### `dashboard_monitoring_coordinator.py` - 監控協調器
系統的核心協調器，負責統一管理所有監控活動。

**主要功能：**
- 監控系統啟動和停止
- 定期收集任務循環
- 指標收集和廣播
- WebSocket 連接管理整合

### `dashboard_data_collection_service.py` - 資料收集服務
統一的資料收集服務，協調各種監控資料收集器。

**主要功能：**
- 郵件處理指標收集
- Celery 任務指標收集
- 系統資源指標收集
- 檔案處理指標收集

### `dashboard_alert_service.py` - 告警服務
智能告警系統，處理告警規則評估和通知發送。

**主要功能：**
- 告警規則評估
- 多管道通知系統
- 告警合併和去重
- 告警歷史記錄

### `dashboard_trend_analyzer.py` - 趨勢分析服務
歷史資料分析和預測服務。

**主要功能：**
- 歷史資料趨勢分析
- 負載預測算法
- 異常檢測機制
- 統計資料計算

### `dashboard_websocket_manager.py` - WebSocket 核心服務 ✅
WebSocket 管理的核心業務邏輯，與監控協調器整合。

**主要功能：**
- **服務生命週期管理** - 啟動、停止、健康檢查
- **廣播策略管理** - 統一的資料廣播邏輯
- **與監控協調器整合** - 接收監控資料並即時推送
- **統計資訊提供** - WebSocket 服務統計和監控

**核心類別：**

#### `DashboardWebSocketService`
WebSocket 核心服務類，提供業務邏輯抽象。

```python
class DashboardWebSocketService:
    """統一監控儀表板 WebSocket 核心服務"""
    
    async def start_service(self) -> None:
        """啟動 WebSocket 服務"""
    
    async def stop_service(self) -> None:
        """停止 WebSocket 服務"""
    
    async def broadcast_dashboard_state(self, dashboard_state: DashboardState) -> None:
        """廣播完整的儀表板狀態"""
    
    async def broadcast_metrics_update(self, metrics_data: Dict[str, Any]) -> None:
        """廣播指標更新"""
    
    async def broadcast_alert(self, alert_data: Dict[str, Any]) -> None:
        """廣播告警"""
    
    async def broadcast_system_status(self, status_data: Dict[str, Any]) -> None:
        """廣播系統狀態"""
```

#### `DashboardWebSocketStats`
WebSocket 統計資訊資料類。

```python
@dataclass
class DashboardWebSocketStats:
    """WebSocket 統計資訊"""
    active_connections: int = 0
    total_connections: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    uptime_seconds: float = 0.0
    last_broadcast: Optional[datetime] = None
```

**使用範例：**

```python
from src.dashboard_monitoring.core.dashboard_websocket_manager import (
    get_websocket_service,
    initialize_websocket_service
)

# 初始化並啟動服務
websocket_service = await initialize_websocket_service()

# 廣播指標更新
await websocket_service.broadcast_metrics_update({
    "cpu_percent": 75.5,
    "memory_percent": 68.2,
    "timestamp": datetime.now().isoformat()
})

# 廣播告警
await websocket_service.broadcast_alert({
    "title": "CPU 使用率過高",
    "level": "warning",
    "message": "CPU 使用率已達到 85%",
    "timestamp": datetime.now().isoformat()
})

# 獲取服務統計
stats = websocket_service.get_service_stats()
print(f"活躍連接數: {stats.active_connections}")
print(f"總訊息數: {stats.messages_sent}")

# 檢查服務健康狀態
is_healthy = websocket_service.is_healthy()
print(f"服務健康狀態: {'正常' if is_healthy else '異常'}")
```

**與監控協調器整合：**

```python
class DashboardMonitoringCoordinator:
    def __init__(self, config: DashboardConfig):
        self.websocket_service = get_websocket_service(config)
    
    async def start_monitoring(self) -> None:
        # 啟動 WebSocket 服務
        await self.websocket_service.start_service()
        
        # 啟動其他監控任務
        asyncio.create_task(self._metrics_collection_loop())
    
    async def _metrics_collection_loop(self) -> None:
        while self.is_running:
            # 收集指標
            metrics = await self.collect_all_metrics()
            
            # 廣播到 WebSocket 客戶端
            await self.websocket_service.broadcast_dashboard_state(metrics)
            
            await asyncio.sleep(self.config.update_intervals.metrics_collection)
```

## 🔧 配置整合

所有核心服務都使用統一的配置系統：

```python
from src.dashboard_monitoring.config.dashboard_config import DashboardConfig

config = DashboardConfig()

# WebSocket 配置
websocket_config = config.websocket_config
print(f"最大連接數: {websocket_config.max_connections}")
print(f"心跳間隔: {websocket_config.heartbeat_interval}")

# 更新間隔配置
update_intervals = config.update_intervals
print(f"指標收集間隔: {update_intervals.metrics_collection}")
print(f"WebSocket 心跳間隔: {update_intervals.websocket_heartbeat}")
```

## 📊 效能監控

核心服務提供詳細的效能監控：

```python
# WebSocket 服務統計
websocket_service = get_websocket_service()
stats = websocket_service.get_service_stats()

print(f"WebSocket 統計:")
print(f"  活躍連接: {stats.active_connections}")
print(f"  總連接數: {stats.total_connections}")
print(f"  發送訊息: {stats.messages_sent}")
print(f"  接收訊息: {stats.messages_received}")
print(f"  運行時間: {stats.uptime_seconds:.2f} 秒")

# 連接資訊
connection_info = websocket_service.get_connection_info()
print(f"連接資訊:")
print(f"  服務運行: {connection_info['service_running']}")
print(f"  當前連接: {connection_info['connections']}")
print(f"  最大連接: {connection_info['max_connections']}")
```

## 🛡️ 錯誤處理和隔離

核心服務實現完整的錯誤隔離：

```python
# WebSocket 服務錯誤不會影響其他服務
try:
    await websocket_service.broadcast_metrics_update(metrics_data)
except Exception as e:
    logger.error(f"WebSocket 廣播失敗: {e}")
    # 其他服務繼續正常運行

# 健康檢查
if not websocket_service.is_healthy():
    logger.warning("WebSocket 服務異常，嘗試重啟")
    await websocket_service.stop_service()
    await websocket_service.start_service()
```

## 🧪 測試支援

核心服務提供完整的測試支援：

```python
# 測試用的模擬服務
from unittest.mock import Mock, AsyncMock

async def test_websocket_service():
    config = Mock()
    config.websocket_config.max_connections = 10
    
    service = DashboardWebSocketService(config)
    
    # 測試服務啟動
    await service.start_service()
    assert service.is_running
    
    # 測試廣播功能
    await service.broadcast_metrics_update({"test": "data"})
    
    # 測試服務停止
    await service.stop_service()
    assert not service.is_running
```

## 🔄 服務整合模式

核心服務使用統一的整合模式：

1. **單例模式** - 確保服務實例唯一性
2. **依賴注入** - 統一的配置和依賴管理
3. **觀察者模式** - 事件驅動的資料更新
4. **策略模式** - 可配置的處理策略

## 📈 擴展指南

添加新的核心服務：

1. **創建服務類** - 繼承基礎服務介面
2. **實現生命週期** - start_service(), stop_service(), is_healthy()
3. **添加配置支援** - 在 DashboardConfig 中添加相關配置
4. **整合到協調器** - 在監控協調器中註冊新服務
5. **添加測試** - 完整的單元測試和整合測試

## 🚀 部署考量

核心服務的部署注意事項：

1. **服務啟動順序** - WebSocket 服務應在監控協調器之後啟動
2. **資源限制** - 設定適當的記憶體和 CPU 限制
3. **監控告警** - 監控服務健康狀態和效能指標
4. **優雅關閉** - 確保所有服務能夠優雅關閉和清理資源