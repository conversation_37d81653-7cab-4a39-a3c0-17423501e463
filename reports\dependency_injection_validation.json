{"total_endpoints": 16, "compliant_endpoints": 0, "non_compliant_endpoints": 16, "compliance_rate": 0.0, "endpoints": [{"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 29, "method": "POST", "path": "/create", "function_name": "create_staging_task", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 82, "method": "POST", "path": "/execute/{task_id}", "function_name": "execute_staging_task", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 150, "method": "GET", "path": "/status/{task_id}", "function_name": "get_staging_task_status", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 177, "method": "DELETE", "path": "/cleanup/{task_id}", "function_name": "cleanup_staging_directory", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 198, "method": "GET", "path": "/tasks", "function_name": "list_staging_tasks", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 218, "method": "GET", "path": "/statistics", "function_name": "get_staging_statistics", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 242, "method": "POST", "path": "/cancel/{task_id}", "function_name": "cancel_staging_task", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\staging_routes.py", "line_number": 277, "method": "POST", "path": "/cleanup-completed", "function_name": "cleanup_completed_tasks", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 46, "method": "POST", "path": "/csv-summary-with-staging", "function_name": "process_csv_summary_with_staging", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 100, "method": "POST", "path": "/code-comparison-with-staging", "function_name": "process_code_comparison_with_staging", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 154, "method": "POST", "path": "/csv-summary", "function_name": "process_csv_summary_submit", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 195, "method": "POST", "path": "/code-comparison", "function_name": "process_code_comparison_submit", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 236, "method": "GET", "path": "/task/{task_id}", "function_name": "get_processing_task_status", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 263, "method": "GET", "path": "/tasks", "function_name": "list_processing_tasks", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 297, "method": "POST", "path": "/execute/{task_id}", "function_name": "execute_processing_task", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}, {"file_path": "src\\presentation\\api\\processing_routes.py", "line_number": 356, "method": "POST", "path": "/cancel/{task_id}", "function_name": "cancel_processing_task", "uses_dependency_injection": true, "has_manual_checks": false, "has_request_tracking": false, "issues": ["缺少請求追蹤"]}], "files_analyzed": ["src\\presentation\\api\\staging_routes.py", "src\\presentation\\api\\processing_routes.py"]}