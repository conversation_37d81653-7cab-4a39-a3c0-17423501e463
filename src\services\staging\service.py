"""檔案暫存服務 - 主要服務類別
包含簡化的 FileStagingService，負責協調和狀態管理
"""

import os
import shutil
import uuid
import asyncio
import getpass
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from loguru import logger

from .models import (
    StagingTask, StagingFileInfo, StagingResult, StagingStatus,
    StagingError, InsufficientSpaceError, StagingPermissionError,
    StagingConcurrencyError, TaskCancellationError
)
from .utils import ProgressBatcher, TaskMonitor
from .operations import FileOperations


class FileStagingService:
    """檔案暫存服務
    
    負責將選定的檔案複製到本地暫存目錄，並提供完整性驗證和錯誤處理
    支援並發安全、重試機制、效能優化和監控診斷
    """
    
    def __init__(
        self,
        base_staging_path: str = "d:\\temp",
        max_workers: int = 4,
        chunk_size: int = 2 * 1024 * 1024,  # 2MB chunks for better performance
        verify_integrity: bool = True,
        cleanup_on_failure: bool = True,
        enable_progress_batching: bool = True,
        progress_update_interval: int = 10,
        default_timeout: float = 1800.0,  # 30分鐘預設超時
        max_concurrent_tasks: int = 5,
        enable_file_locking: bool = True
    ):
        self.base_staging_path = Path(base_staging_path)
        self.max_workers = max_workers
        self.chunk_size = chunk_size
        self.verify_integrity = verify_integrity
        self.cleanup_on_failure = cleanup_on_failure
        self.enable_progress_batching = enable_progress_batching
        self.progress_update_interval = progress_update_interval
        self.default_timeout = default_timeout
        self.max_concurrent_tasks = max_concurrent_tasks
        self.enable_file_locking = enable_file_locking
        
        # 任務管理
        self.tasks: Dict[str, StagingTask] = {}
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers, 
            thread_name_prefix="FileStaging"
        )
        self._lock = threading.RLock()
        
        # 效能優化組件
        self.progress_batcher = ProgressBatcher(progress_update_interval)
        self.task_monitor = TaskMonitor()
        
        # 檔案操作組件
        self.file_operations = FileOperations(
            chunk_size=chunk_size,
            max_workers=max_workers,
            enable_file_locking=enable_file_locking,
            enable_progress_batching=enable_progress_batching,
            progress_batcher=self.progress_batcher,
            lock=self._lock
        )
        
        # 活躍任務計數
        self.active_task_count = 0
        
        # 確保基礎暫存目錄存在
        self._ensure_base_directory()
        
        logger.info(f"檔案暫存服務已初始化 - 基礎路徑: {self.base_staging_path}")
        logger.info(f"配置 - 最大工作者: {max_workers}, 區塊大小: {chunk_size // (1024*1024)}MB, "
                   f"並發任務上限: {max_concurrent_tasks}")
    
    def _ensure_base_directory(self):
        """確保基礎暫存目錄存在"""
        try:
            self.base_staging_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"基礎暫存目錄已準備: {self.base_staging_path}")
        except Exception as e:
            logger.error(f"無法建立基礎暫存目錄: {e}")
            raise StagingError(f"無法建立基礎暫存目錄: {e}")
    
    def _generate_unique_product_name(self, product_name: str) -> str:
        """生成唯一的產品名稱
        格式：[產品名稱]_[用戶名]_[時間戳]
        例如：PROJECT_X_user1_20250801_194500
        """
        try:
            # 獲取當前用戶名
            username = getpass.getuser()
            
            # 生成時間戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 清理產品名稱，將連字號轉換為底線，移除不安全的字符
            safe_product_name = "".join(c if c.isalnum() or c == '_' else '_' if c == '-' else '' for c in product_name).strip()
            if not safe_product_name:
                safe_product_name = "PRODUCT"
            
            # 清理用戶名，允許點號
            safe_username = "".join(c for c in username if c.isalnum() or c in ['_', '-', '.']).strip()
            if not safe_username:
                safe_username = "user"
            
            # 組合唯一名稱
            unique_name = f"{safe_product_name}_{safe_username}_{timestamp}"
            
            logger.debug(f"生成唯一產品名稱: {product_name} -> {unique_name}")
            return unique_name
            
        except Exception as e:
            logger.warning(f"生成唯一產品名稱時發生錯誤: {e}，使用預設格式")
            # 回退到簡單的時間戳格式
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f"{product_name}_{timestamp}"

    def create_staging_task(
        self,
        product_name: str,
        source_files: List[Union[str, Path]],
        preserve_structure: bool = True,
        verify_integrity: bool = None,
        timeout: Optional[float] = None,
        use_unique_name: bool = True
    ) -> str:
        """建立暫存任務"""
        task_id = str(uuid.uuid4())

        # 轉換為 Path 物件
        source_paths = [Path(f) for f in source_files]

        # 生成唯一的產品名稱（如果啟用）
        if use_unique_name:
            unique_product_name = self._generate_unique_product_name(product_name)
            logger.info(f"使用唯一產品名稱: {product_name} -> {unique_product_name}")
        else:
            unique_product_name = product_name

        # 建立產品專用暫存目錄
        staging_directory = self.base_staging_path / unique_product_name

        # 計算總大小
        total_size = 0
        file_infos = []

        for source_path in source_paths:
            try:
                if source_path.exists():
                    if source_path.is_file():
                        size = source_path.stat().st_size
                        total_size += size

                        # 決定目標路徑
                        if preserve_structure:
                            relative_path = source_path.name
                            target_path = staging_directory / relative_path
                        else:
                            target_path = staging_directory / source_path.name

                        file_info = StagingFileInfo(
                            source_path=source_path,
                            target_path=target_path,
                            size=size
                        )
                        file_infos.append(file_info)

                    elif source_path.is_dir():
                        # 處理目錄
                        for file_path in source_path.rglob('*'):
                            if file_path.is_file():
                                size = file_path.stat().st_size
                                total_size += size

                                if preserve_structure:
                                    relative_path = file_path.relative_to(source_path.parent)
                                    target_path = staging_directory / relative_path
                                else:
                                    target_path = staging_directory / file_path.name

                                file_info = StagingFileInfo(
                                    source_path=file_path,
                                    target_path=target_path,
                                    size=size
                                )
                                file_infos.append(file_info)
                else:
                    logger.warning(f"來源檔案不存在: {source_path}")

            except Exception as e:
                logger.error(f"處理來源檔案時發生錯誤 {source_path}: {e}")

        # 建立任務
        task = StagingTask(
            task_id=task_id,
            product_name=product_name,
            source_files=source_paths,
            staging_directory=staging_directory,
            status=StagingStatus.PENDING,
            created_at=datetime.now(),
            file_infos=file_infos,
            total_size=total_size,
            preserve_structure=preserve_structure,
            verify_integrity=verify_integrity if verify_integrity is not None else self.verify_integrity,
            timeout=timeout or self.default_timeout
        )

        with self._lock:
            self.tasks[task_id] = task

        logger.info(f"建立暫存任務: {task_id}, 產品: {product_name}, 檔案數: {len(file_infos)}, "
                   f"總大小: {total_size / (1024*1024):.2f} MB, 超時: {task.timeout}秒")
        return task_id

    async def execute_staging_task(self, task_id: str) -> StagingResult:
        """執行暫存任務"""
        with self._lock:
            task = self.tasks.get(task_id)

            if not task:
                raise StagingError(f"暫存任務不存在: {task_id}")

            if task.status != StagingStatus.PENDING:
                raise StagingError(f"任務狀態不正確: {task.status}")

            # 檢查並發限制
            if self.active_task_count >= self.max_concurrent_tasks:
                raise StagingConcurrencyError(
                    f"超過最大並發任務數: {self.max_concurrent_tasks} "
                    f"(目前: {self.active_task_count})"
                )

            # 增加活躍任務計數
            self.active_task_count += 1
            task.status = StagingStatus.IN_PROGRESS
            task.started_at = datetime.now()

        start_time = datetime.now()

        try:
            logger.info(f"開始執行暫存任務: {task_id} (活躍任務: {self.active_task_count})")

            # 設定任務超時
            if task.timeout:
                asyncio.create_task(self._handle_task_timeout(task))

            # 步驟1: 檢查磁碟空間
            await self._check_disk_space(task)

            # 步驟2: 準備暫存目錄
            await self._prepare_staging_directory(task)

            # 步驟3: 複製檔案（帶重試機制）
            await self._copy_files_with_retry(task)

            # 步驟4: 驗證檔案完整性（如果啟用）
            if task.verify_integrity and not task.cancelled:
                await self.file_operations.verify_file_integrity(task)

            # 檢查是否被取消
            if task.cancelled:
                raise TaskCancellationError("任務已被取消")

            # 完成任務
            task.status = StagingStatus.COMPLETED
            task.completed_at = datetime.now()
            task.progress = 100.0

            staging_duration = (datetime.now() - start_time).total_seconds()

            staged_files = [info.target_path for info in task.file_infos if info.copied]

            # 計算效能統計
            average_speed = (task.total_size / (1024 * 1024)) / staging_duration if staging_duration > 0 else 0

            result = StagingResult(
                success=True,
                task_id=task_id,
                staging_directory=task.staging_directory,
                staged_files=staged_files,
                total_files=len(staged_files),
                total_size=task.total_size,
                staging_duration=staging_duration,
                integrity_check_passed=all(info.verified for info in task.file_infos) if task.verify_integrity else True,
                retries_used=task.retry_count,
                average_speed=average_speed,
                peak_memory=task.peak_memory_usage,
                cpu_usage=task.cpu_time
            )

            logger.info(f"暫存任務完成: {task_id}, 耗時: {staging_duration:.2f}秒, "
                       f"速度: {average_speed:.2f} MB/s, 重試次數: {task.retry_count}")
            return result

        except Exception as e:
            # 任務失敗處理
            task.status = StagingStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()

            # 清理失敗的暫存檔案
            if self.cleanup_on_failure:
                await self.file_operations.cleanup_failed_staging(task)

            staging_duration = (datetime.now() - start_time).total_seconds()

            result = StagingResult(
                success=False,
                task_id=task_id,
                staging_directory=task.staging_directory,
                staged_files=[],
                total_files=0,
                total_size=0,
                staging_duration=staging_duration,
                error_message=str(e),
                retries_used=task.retry_count
            )

            logger.error(f"暫存任務失敗: {task_id}, 錯誤: {e}, 重試次數: {task.retry_count}")
            return result

        finally:
            # 減少活躍任務計數
            with self._lock:
                self.active_task_count = max(0, self.active_task_count - 1)

            # 清理監控數據
            self.task_monitor.cleanup_metrics(task_id)

            logger.debug(f"任務 {task_id} 執行完畢，剩餘活躍任務: {self.active_task_count}")

    async def _handle_task_timeout(self, task: StagingTask):
        """處理任務超時"""
        await asyncio.sleep(task.timeout)

        if task.status == StagingStatus.IN_PROGRESS:
            logger.warning(f"任務 {task.task_id} 執行超時 ({task.timeout}秒)，正在取消")
            await self.cancel_task(task.task_id)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        with self._lock:
            task = self.tasks.get(task_id)

        if not task:
            logger.warning(f"嘗試取消不存在的任務: {task_id}")
            return False

        if task.status not in [StagingStatus.PENDING, StagingStatus.IN_PROGRESS]:
            logger.warning(f"無法取消任務 {task_id}，當前狀態: {task.status}")
            return False

        task.cancelled = True
        task.status = StagingStatus.CANCELLED
        task.completed_at = datetime.now()

        if task.cancel_event:
            task.cancel_event.set()

        logger.info(f"任務已取消: {task_id}")
        return True

    async def _check_disk_space(self, task: StagingTask):
        """檢查磁碟空間"""
        try:
            # 獲取磁碟使用情況
            disk_usage = shutil.disk_usage(self.base_staging_path)
            available_space = disk_usage.free

            # 預留 15% 的緩衝空間（增加安全邊際）
            required_space = int(task.total_size * 1.15)

            if available_space < required_space:
                raise InsufficientSpaceError(
                    f"磁碟空間不足。需要: {required_space / (1024*1024):.2f} MB, "
                    f"可用: {available_space / (1024*1024):.2f} MB"
                )

            logger.info(f"磁碟空間檢查通過。需要: {required_space / (1024*1024):.2f} MB, "
                       f"可用: {available_space / (1024*1024):.2f} MB")

        except Exception as e:
            if isinstance(e, InsufficientSpaceError):
                raise
            raise StagingError(f"磁碟空間檢查失敗: {e}")

    async def _prepare_staging_directory(self, task: StagingTask):
        """準備暫存目錄"""
        try:
            # 如果目錄已存在，先清理
            if task.staging_directory.exists():
                logger.info(f"清理現有暫存目錄: {task.staging_directory}")
                shutil.rmtree(task.staging_directory)

            # 建立暫存目錄
            task.staging_directory.mkdir(parents=True, exist_ok=True)

            # 建立所有必要的子目錄
            for file_info in task.file_infos:
                file_info.target_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"暫存目錄已準備: {task.staging_directory}")

        except Exception as e:
            raise StagingPermissionError(f"無法準備暫存目錄: {e}")

    async def _copy_files_with_retry(self, task: StagingTask):
        """複製檔案（帶重試機制）"""
        max_retries = task.max_retries

        for retry_attempt in range(max_retries + 1):
            if task.cancelled:
                raise TaskCancellationError("任務已被取消")

            try:
                await self.file_operations.copy_files(task)

                # 檢查是否有失敗的檔案需要重試
                failed_files = [info for info in task.file_infos if not info.copied and not info.error_message]

                if not failed_files:
                    break  # 所有檔案都成功複製

                if retry_attempt < max_retries:
                    task.status = StagingStatus.RETRYING
                    task.retry_count = retry_attempt + 1

                    logger.warning(f"第 {retry_attempt + 1} 次重試任務 {task.task_id}，"
                                 f"失敗檔案數: {len(failed_files)}")

                    # 重試延遲
                    await asyncio.sleep(task.retry_delay * (2 ** retry_attempt))  # 指數退避

                    # 重置失敗檔案的狀態
                    for file_info in failed_files:
                        file_info.error_message = None
                        file_info.retry_count = retry_attempt + 1
                        file_info.last_retry_at = datetime.now()
                else:
                    # 超過最大重試次數
                    failed_file_names = [str(info.source_path) for info in failed_files]
                    raise StagingError(f"檔案複製失敗，已重試 {max_retries} 次: {failed_file_names}")

            except TaskCancellationError:
                raise
            except Exception as e:
                if retry_attempt < max_retries:
                    task.status = StagingStatus.RETRYING
                    task.retry_count = retry_attempt + 1

                    logger.warning(f"任務 {task.task_id} 第 {retry_attempt + 1} 次重試，錯誤: {e}")
                    await asyncio.sleep(task.retry_delay * (2 ** retry_attempt))
                else:
                    raise

        task.status = StagingStatus.IN_PROGRESS  # 恢復正常狀態

    async def cleanup_staging_directory(self, task_id: str) -> bool:
        """清理暫存目錄"""
        with self._lock:
            task = self.tasks.get(task_id)

        if not task:
            logger.warning(f"清理暫存目錄時找不到任務: {task_id}")
            return False

        return await self.file_operations.cleanup_staging_directory(task.staging_directory)

    def get_task_status(self, task_id: str) -> Optional[StagingTask]:
        """取得任務狀態"""
        with self._lock:
            return self.tasks.get(task_id)

    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """取得任務進度"""
        with self._lock:
            task = self.tasks.get(task_id)

        if not task:
            return {"error": "任務不存在"}

        return {
            "task_id": task.task_id,
            "product_name": task.product_name,
            "status": task.status.value,
            "progress": task.progress,
            "total_files": len(task.file_infos),
            "copied_files": sum(1 for info in task.file_infos if info.copied),
            "verified_files": sum(1 for info in task.file_infos if info.verified),
            "failed_files": sum(1 for info in task.file_infos if info.error_message),
            "total_size": task.total_size,
            "copied_size": task.copied_size,
            "staging_directory": str(task.staging_directory),
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": task.error_message,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "cancelled": task.cancelled,
            "timeout": task.timeout,
            # 效能監控資料
            "io_operations": task.io_operations,
            "peak_memory_usage": task.peak_memory_usage,
            "cpu_time": task.cpu_time
        }

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任務"""
        with self._lock:
            return [
                {
                    "task_id": task.task_id,
                    "product_name": task.product_name,
                    "status": task.status.value,
                    "progress": task.progress,
                    "total_files": len(task.file_infos),
                    "total_size": task.total_size,
                    "created_at": task.created_at.isoformat(),
                    "retry_count": task.retry_count,
                    "cancelled": task.cancelled
                }
                for task in self.tasks.values()
            ]

    def get_service_statistics(self) -> Dict[str, Any]:
        """獲取服務統計資料"""
        with self._lock:
            total_tasks = len(self.tasks)
            pending_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.PENDING)
            active_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.IN_PROGRESS)
            completed_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.COMPLETED)
            failed_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.FAILED)
            cancelled_tasks = sum(1 for task in self.tasks.values() if task.status == StagingStatus.CANCELLED)

            total_data_processed = sum(task.copied_size for task in self.tasks.values())

            return {
                "total_tasks": total_tasks,
                "pending_tasks": pending_tasks,
                "active_tasks": active_tasks,
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "cancelled_tasks": cancelled_tasks,
                "success_rate": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                "total_data_processed_mb": total_data_processed / (1024 * 1024),
                "active_task_count": self.active_task_count,
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "executor_threads": self.max_workers
            }

    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的舊任務"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        with self._lock:
            tasks_to_remove = [
                task_id for task_id, task in self.tasks.items()
                if task.status in [StagingStatus.COMPLETED, StagingStatus.FAILED, StagingStatus.CANCELLED]
                and task.completed_at
                and task.completed_at < cutoff_time
            ]

            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                logger.info(f"清理舊暫存任務: {task_id}")

        return len(tasks_to_remove)

    def __del__(self):
        """清理資源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
