"""
API 端點模組

包含所有監控相關的API端點：
- 依賴注入系統 (Dependencies)
- 儀表板 API (DashboardAPI)
- 監控 API (MonitoringAPI)
- WebSocket 端點 (WebSocketEndpoints)
"""

# Import WebSocket functionality (always available)
from .dashboard_websocket import websocket_router

# Import other dependencies with fallbacks
try:
    from .dashboard_dependencies import (
        get_monitoring_coordinator,
        get_data_collection_service,
        get_alert_service
    )
    dependencies_available = True
except ImportError:
    # Dependencies not yet implemented
    get_monitoring_coordinator = None
    get_data_collection_service = None
    get_alert_service = None
    dependencies_available = False

try:
    from .dashboard_monitoring_api import router as monitoring_router
except ImportError:
    # Monitoring API not yet implemented
    monitoring_router = None

__all__ = [
    "websocket_router",
    "get_monitoring_coordinator",
    "get_data_collection_service", 
    "get_alert_service",
    "monitoring_router",
    "dependencies_available"
]