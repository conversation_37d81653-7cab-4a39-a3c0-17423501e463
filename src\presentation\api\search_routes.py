"""網路瀏覽器 API - 搜尋相關路由
處理產品搜尋、智慧搜尋和任務狀態查詢功能
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Query, HTTPException, Depends
from fastapi.responses import JSONResponse

from loguru import logger

from .network_models import (
    ProductSearchRequest, ProductSearchResponse, ProductSearchFileInfo,
    SearchTaskResponse, TimeRangeType
)

# 導入依賴注入
from .dependencies import (
    get_product_search_service, get_llm_search_service, get_api_state,
    require_product_search_service, require_llm_search_service,
    APIState, ProductSearchService, LLMSearchService
)

try:
    from ...data_models.search_models import SmartSearchRequest, SmartSearchResponse
    from ...services.product_search_service import ProductSearchService
    from ...services.llm_search_service import LLMSearchService
    from ...domain.entities.file_search import SearchFilters
    
    # 導入 Celery 相關工具和任務
    from celery.result import AsyncResult
    try:
        from ...tasks import search_product_task, health_check_task
    except ImportError:
        search_product_task = None
        health_check_task = None
        
except ImportError:
    # 當直接執行時的回退
    ProductSearchService = None
    LLMSearchService = None
    SearchFilters = None
    SmartSearchRequest = None
    SmartSearchResponse = None
    search_product_task = None
    health_check_task = None

# 建立路由器
router = APIRouter(prefix="/api", tags=["Search"])


@router.post("/search/product")
async def search_product_submit(
    request: ProductSearchRequest,
    api_state: APIState = Depends(get_api_state)
):
    """產品搜尋端點 - 提交一個背景搜尋任務（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        logger.info(f"🔍 API 收到搜尋請求，準備提交至背景佇列: {request.product_name}")
        print(f"🔍 API 收到搜尋請求: {request.product_name}")

        if search_product_task is None:
            raise HTTPException(status_code=503, detail="搜尋任務服務未可用")

        # 建立搜尋過濾器
        search_filters = None
        if SearchFilters is not None:
            search_filters = SearchFilters(
                time_range=request.time_range,
                file_types=request.file_types,
                size_range=request.size_range,
                include_patterns=request.include_patterns,
                exclude_patterns=request.exclude_patterns
            )

        # 提交背景任務
        task = search_product_task.delay(
            product_name=request.product_name,
            search_paths=request.search_paths,
            max_results=request.max_results,
            search_filters=search_filters.dict() if search_filters else None
        )

        logger.info(f"✅ 搜尋任務已提交至 Celery 佇列: {task.id}")
        print(f"✅ 搜尋任務已提交: {task.id}")

        return ProductSearchResponse(
            task_id=task.id,
            status="submitted",
            message=f"搜尋任務已提交，任務ID: {task.id}",
            product_name=request.product_name,
            search_paths=request.search_paths,
            max_results=request.max_results,
            files=[],
            total_found=0,
            search_duration=0.0,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"❌ 提交產品搜尋任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交搜尋任務時發生錯誤: {str(e)}")


@router.get("/task/status/{task_id}")
async def get_task_status(
    task_id: str,
    api_state: APIState = Depends(get_api_state)
):
    """根據 Task ID 獲取 Celery 背景任務的狀態和結果（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        logger.debug(f"🔍 查詢 Celery 任務狀態: {task_id}")

        # 使用 Celery 的 AsyncResult 來查詢任務狀態
        result = AsyncResult(task_id)

        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "ready": result.ready(),
            "successful": result.successful() if result.ready() else None,
            "failed": result.failed() if result.ready() else None
        }

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"❌ 查詢任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢任務狀態時發生錯誤: {str(e)}")


@router.get("/search/task/{task_id}", response_model=SearchTaskResponse)
async def get_search_task_status(
    task_id: str,
    product_search_service: ProductSearchService = Depends(require_product_search_service),
    api_state: APIState = Depends(get_api_state)
):
    """獲取搜尋任務狀態（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 直接使用注入的服務，無需 None 檢查
        task_status = product_search_service.get_task_status(task_id)

        if task_status is None:
            raise HTTPException(status_code=404, detail=f"找不到任務: {task_id}")

        return SearchTaskResponse(
            task_id=task_id,
            status=task_status.get("status", "unknown"),
            progress=task_status.get("progress", 0.0),
            message=task_status.get("message", ""),
            results_count=task_status.get("results_count", 0),
            error_message=task_status.get("error_message")
        )

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"[ERROR] 獲取搜尋任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態時發生錯誤: {str(e)}")


@router.get("/smart-search")
async def smart_search(
    query: str = Query(..., description="自然語言搜尋查詢"),
    path: str = Query(default="\\\\************\\test_log", description="網路資料夾路徑"),
    max_results: int = Query(default=100, description="最大結果數量", ge=1, le=1000),
    llm_search_service: LLMSearchService = Depends(require_llm_search_service),
    api_state: APIState = Depends(get_api_state)
):
    """LLM 智慧搜尋端點 (GET) - 簡化版本（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 直接使用注入的服務，無需 None 檢查
        search_results = await llm_search_service.smart_search(
            query=query,
            search_path=path,
            max_results=max_results
        )

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "query": query,
                "results": search_results.get("results", []),
                "total_results": len(search_results.get("results", [])),
                "processing_time": search_results.get("processing_time", 0.0),
                "timestamp": datetime.now().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"智慧搜尋失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "query": query,
                "results": [],
                "total_results": 0,
                "error_message": f"智慧搜尋時發生錯誤: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@router.post("/smart-search", response_model=SmartSearchResponse)
async def smart_search_post(
    request: SmartSearchRequest,
    llm_search_service: LLMSearchService = Depends(require_llm_search_service),
    api_state: APIState = Depends(get_api_state)
):
    """LLM 智慧搜尋端點 (POST) - 支援更複雜的搜尋請求（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        # 直接使用注入的服務，無需 None 檢查
        search_results = await llm_search_service.smart_search_advanced(request)

        if SmartSearchResponse is not None:
            return SmartSearchResponse(
                success=True,
                query=request.query,
                results=search_results.get("results", []),
                total_results=len(search_results.get("results", [])),
                processing_time=search_results.get("processing_time", 0.0),
                search_strategy=search_results.get("search_strategy", ""),
                confidence_score=search_results.get("confidence_score", 0.0)
            )
        else:
            return {
                "success": True,
                "query": request.query,
                "results": search_results.get("results", []),
                "total_results": len(search_results.get("results", []))
            }

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"智慧搜尋失敗: {str(e)}")
        if SmartSearchResponse is not None:
            return SmartSearchResponse(
                success=False,
                query=request.query,
                error_message=f"智慧搜尋時發生錯誤: {str(e)}"
            )
        else:
            raise HTTPException(status_code=500, detail=f"智慧搜尋時發生錯誤: {str(e)}")


@router.get("/celery/health")
async def celery_health_check(
    api_state: APIState = Depends(get_api_state)
):
    """Celery 健康檢查端點（重構版 - 使用依賴注入）"""
    try:
        # 追蹤請求
        api_state.increment_request_count()

        logger.info("🏥 執行 Celery 健康檢查")

        if health_check_task is None:
            return {
                "status": "error",
                "celery_worker": "unavailable",
                "error": "Celery 任務未可用",
                "timestamp": datetime.now().isoformat()
            }

        # 提交健康檢查任務
        task = health_check_task.delay()

        # 等待結果（最多5秒）
        try:
            result = task.get(timeout=5)
            logger.info(f"✅ Celery 健康檢查成功: {result}")

            return {
                "status": "healthy",
                "celery_worker": "available",
                "task_id": task.id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as timeout_error:
            logger.warning(f"⏰ Celery 健康檢查超時: {timeout_error}")
            return {
                "status": "timeout",
                "celery_worker": "slow_response",
                "task_id": task.id,
                "error": str(timeout_error),
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"❌ Celery 健康檢查失敗: {str(e)}")
        return {
            "status": "error",
            "celery_worker": "unavailable",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
