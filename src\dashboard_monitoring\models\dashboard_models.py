"""
Dashboard Main Models

This module defines the main dashboard models that integrate metrics and alerts
for the unified monitoring dashboard. Provides comprehensive data structures
for dashboard state management and API responses.

Requirements covered: 1, 2, 5, 6, 8, 9
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json

from .dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics, 
    FileMetrics, BusinessMetrics, MetricType, MetricUnit
)
from .dashboard_alert_models import (
    DashboardAlert, AlertSummary, AlertLevel, AlertStatus, AlertType
)


class DashboardStatus(Enum):
    """Overall dashboard status"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class ServiceStatus(Enum):
    """Individual service status"""
    ONLINE = "online"
    OFFLINE = "offline"
    DEGRADED = "degraded"
    MAINTENANCE = "maintenance"
    UNKNOWN = "unknown"


@dataclass
class ServiceHealth:
    """Health status of individual services"""
    service_name: str
    status: ServiceStatus = ServiceStatus.UNKNOWN
    last_check: datetime = field(default_factory=datetime.now)
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None
    uptime_percentage: float = 0.0
    
    # Service-specific metrics
    details: Dict[str, Any] = field(default_factory=dict)
    
    def is_healthy(self) -> bool:
        """Check if service is healthy"""
        return self.status == ServiceStatus.ONLINE
    
    def get_status_color(self) -> str:
        """Get color code for status display"""
        color_map = {
            ServiceStatus.ONLINE: "green",
            ServiceStatus.OFFLINE: "red",
            ServiceStatus.DEGRADED: "yellow",
            ServiceStatus.MAINTENANCE: "blue",
            ServiceStatus.UNKNOWN: "gray"
        }
        return color_map.get(self.status, "gray")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "service_name": self.service_name,
            "status": self.status.value,
            "last_check": self.last_check.isoformat(),
            "response_time_ms": self.response_time_ms,
            "error_message": self.error_message,
            "uptime_percentage": self.uptime_percentage,
            "details": self.details,
            "is_healthy": self.is_healthy(),
            "status_color": self.get_status_color()
        }


@dataclass
class DashboardConfiguration:
    """Dashboard configuration settings"""
    # Update intervals (seconds)
    metrics_update_interval: int = 30
    alerts_check_interval: int = 10
    health_check_interval: int = 60
    
    # Data retention (days)
    metrics_retention_days: int = 30
    alerts_retention_days: int = 90
    
    # Display settings
    default_time_range: str = "1h"  # "1h", "6h", "24h", "7d"
    max_chart_points: int = 100
    auto_refresh: bool = True
    
    # Alert thresholds
    alert_thresholds: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # Feature flags
    features: Dict[str, bool] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize default values"""
        if not self.alert_thresholds:
            self.alert_thresholds = {
                "email_queue": {
                    "pending_warning": 10,
                    "pending_critical": 50,
                    "processing_time_warning": 300,
                    "processing_time_critical": 900
                },
                "celery_tasks": {
                    "pending_warning": 20,
                    "pending_critical": 100,
                    "failure_rate_warning": 0.1,
                    "failure_rate_critical": 0.25
                },
                "system_resources": {
                    "cpu_warning": 80,
                    "cpu_critical": 95,
                    "memory_warning": 85,
                    "memory_critical": 95,
                    "disk_warning": 85,
                    "disk_critical": 95
                }
            }
        
        if not self.features:
            self.features = {
                "real_time_updates": True,
                "alert_notifications": True,
                "trend_analysis": True,
                "export_data": True,
                "dark_mode": False
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "metrics_update_interval": self.metrics_update_interval,
            "alerts_check_interval": self.alerts_check_interval,
            "health_check_interval": self.health_check_interval,
            "metrics_retention_days": self.metrics_retention_days,
            "alerts_retention_days": self.alerts_retention_days,
            "default_time_range": self.default_time_range,
            "max_chart_points": self.max_chart_points,
            "auto_refresh": self.auto_refresh,
            "alert_thresholds": self.alert_thresholds,
            "features": self.features
        }


@dataclass
class DashboardState:
    """Current state of the dashboard"""
    # Core metrics
    metrics: DashboardMetrics = field(default_factory=DashboardMetrics)
    
    # Active alerts
    active_alerts: List[DashboardAlert] = field(default_factory=list)
    alert_summary: AlertSummary = field(default_factory=AlertSummary)
    
    # Service health
    service_health: Dict[str, ServiceHealth] = field(default_factory=dict)
    
    # Overall status
    overall_status: DashboardStatus = DashboardStatus.UNKNOWN
    
    # Timestamps
    last_update: datetime = field(default_factory=datetime.now)
    last_alert_check: datetime = field(default_factory=datetime.now)
    last_health_check: datetime = field(default_factory=datetime.now)
    
    # Connection info
    connected_clients: int = 0
    websocket_connections: List[str] = field(default_factory=list)
    
    def update_overall_status(self) -> None:
        """Update overall dashboard status based on metrics and alerts"""
        # Check for critical alerts
        critical_alerts = [a for a in self.active_alerts if a.level == AlertLevel.CRITICAL]
        if critical_alerts:
            self.overall_status = DashboardStatus.CRITICAL
            return
        
        # Check for error alerts
        error_alerts = [a for a in self.active_alerts if a.level == AlertLevel.ERROR]
        if error_alerts:
            self.overall_status = DashboardStatus.ERROR
            return
        
        # Check service health
        unhealthy_services = [s for s in self.service_health.values() if not s.is_healthy()]
        if unhealthy_services:
            self.overall_status = DashboardStatus.WARNING
            return
        
        # Check system resources
        if (self.metrics.system_metrics.cpu_percent > 90 or 
            self.metrics.system_metrics.memory_percent > 90 or
            self.metrics.system_metrics.disk_percent > 90):
            self.overall_status = DashboardStatus.WARNING
            return
        
        # Check queue sizes
        if (self.metrics.email_metrics.pending_count > 20 or
            self.metrics.celery_metrics.total_pending > 50):
            self.overall_status = DashboardStatus.WARNING
            return
        
        # All good
        self.overall_status = DashboardStatus.HEALTHY
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get summary of current status"""
        return {
            "overall_status": self.overall_status.value,
            "total_alerts": len(self.active_alerts),
            "critical_alerts": len([a for a in self.active_alerts if a.level == AlertLevel.CRITICAL]),
            "healthy_services": len([s for s in self.service_health.values() if s.is_healthy()]),
            "total_services": len(self.service_health),
            "last_update": self.last_update.isoformat(),
            "connected_clients": self.connected_clients
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "metrics": self.metrics.to_dict(),
            "active_alerts": [alert.to_dict() for alert in self.active_alerts],
            "alert_summary": self.alert_summary.to_dict(),
            "service_health": {name: health.to_dict() for name, health in self.service_health.items()},
            "overall_status": self.overall_status.value,
            "last_update": self.last_update.isoformat(),
            "last_alert_check": self.last_alert_check.isoformat(),
            "last_health_check": self.last_health_check.isoformat(),
            "connected_clients": self.connected_clients,
            "websocket_connections": len(self.websocket_connections),
            "status_summary": self.get_status_summary()
        }


@dataclass
class TaskDetail:
    """Detailed task information - Requirement 5"""
    task_id: str
    task_type: str
    task_name: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    error_message: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    result_data: Dict[str, Any] = field(default_factory=dict)
    queue_name: Optional[str] = None
    worker_name: Optional[str] = None
    retry_count: int = 0
    
    # Performance indicators
    is_slow: bool = False
    expected_duration: Optional[float] = None
    
    def get_duration(self) -> Optional[float]:
        """Get task duration in seconds"""
        if self.duration_seconds is not None:
            return self.duration_seconds
        
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        
        if self.started_at and not self.completed_at:
            return (datetime.now() - self.started_at).total_seconds()
        
        return None
    
    def is_running_slow(self, threshold_multiplier: float = 2.0) -> bool:
        """Check if task is running slower than expected - Requirement 5.5"""
        if not self.expected_duration:
            return False
        
        current_duration = self.get_duration()
        if not current_duration:
            return False
        
        return current_duration > (self.expected_duration * threshold_multiplier)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "task_name": self.task_name,
            "status": self.status,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "duration_seconds": self.get_duration(),
            "error_message": self.error_message,
            "parameters": self.parameters,
            "result_data": self.result_data,
            "queue_name": self.queue_name,
            "worker_name": self.worker_name,
            "retry_count": self.retry_count,
            "is_slow": self.is_running_slow(),
            "expected_duration": self.expected_duration
        }


@dataclass
class TrendData:
    """Trend analysis data - Requirement 5.4"""
    metric_name: str
    time_range: str  # "1h", "6h", "24h", "7d", "30d"
    data_points: List[Dict[str, Any]] = field(default_factory=list)
    
    # Statistical analysis
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    avg_value: Optional[float] = None
    trend_direction: str = "stable"  # "increasing", "decreasing", "stable"
    trend_percentage: float = 0.0
    
    # Anomaly detection
    anomalies: List[Dict[str, Any]] = field(default_factory=list)
    
    timestamp: datetime = field(default_factory=datetime.now)
    
    def calculate_statistics(self) -> None:
        """Calculate statistical measures from data points"""
        if not self.data_points:
            return
        
        values = [point.get("value", 0) for point in self.data_points if point.get("value") is not None]
        if not values:
            return
        
        self.min_value = min(values)
        self.max_value = max(values)
        self.avg_value = sum(values) / len(values)
        
        # Calculate trend
        if len(values) >= 2:
            first_half = values[:len(values)//2]
            second_half = values[len(values)//2:]
            
            first_avg = sum(first_half) / len(first_half)
            second_avg = sum(second_half) / len(second_half)
            
            if first_avg > 0:
                self.trend_percentage = ((second_avg - first_avg) / first_avg) * 100
                
                if self.trend_percentage > 5:
                    self.trend_direction = "increasing"
                elif self.trend_percentage < -5:
                    self.trend_direction = "decreasing"
                else:
                    self.trend_direction = "stable"
    
    def detect_anomalies(self, threshold_std: float = 2.0) -> None:
        """Detect anomalies in the data"""
        if len(self.data_points) < 10:  # Need sufficient data
            return
        
        values = [point.get("value", 0) for point in self.data_points if point.get("value") is not None]
        if not values:
            return
        
        # Calculate mean and standard deviation
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        std_dev = variance ** 0.5
        
        # Find anomalies
        self.anomalies = []
        for i, point in enumerate(self.data_points):
            value = point.get("value")
            if value is not None and abs(value - mean) > (threshold_std * std_dev):
                self.anomalies.append({
                    "index": i,
                    "timestamp": point.get("timestamp"),
                    "value": value,
                    "deviation": abs(value - mean) / std_dev if std_dev > 0 else 0
                })
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "metric_name": self.metric_name,
            "time_range": self.time_range,
            "data_points": self.data_points,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "avg_value": self.avg_value,
            "trend_direction": self.trend_direction,
            "trend_percentage": self.trend_percentage,
            "anomalies": self.anomalies,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class DashboardFilter:
    """Filter configuration for dashboard data"""
    # Time range
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    time_range: str = "1h"  # "1h", "6h", "24h", "7d", "30d"
    
    # Task filters
    task_types: List[str] = field(default_factory=list)
    task_statuses: List[str] = field(default_factory=list)
    
    # Alert filters
    alert_levels: List[AlertLevel] = field(default_factory=list)
    alert_sources: List[str] = field(default_factory=list)
    
    # Service filters
    services: List[str] = field(default_factory=list)
    
    # Vendor filters (for semiconductor processing)
    vendors: List[str] = field(default_factory=list)  # GTK, JCET, ETD, etc.
    
    def get_time_range_dates(self) -> tuple[datetime, datetime]:
        """Get start and end dates based on time range"""
        end_time = self.end_time or datetime.now()
        
        if self.start_time:
            start_time = self.start_time
        else:
            # Calculate start time based on time range
            if self.time_range == "1h":
                start_time = end_time - timedelta(hours=1)
            elif self.time_range == "6h":
                start_time = end_time - timedelta(hours=6)
            elif self.time_range == "24h":
                start_time = end_time - timedelta(hours=24)
            elif self.time_range == "7d":
                start_time = end_time - timedelta(days=7)
            elif self.time_range == "30d":
                start_time = end_time - timedelta(days=30)
            else:
                start_time = end_time - timedelta(hours=1)
        
        return start_time, end_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        start_time, end_time = self.get_time_range_dates()
        
        return {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "time_range": self.time_range,
            "task_types": self.task_types,
            "task_statuses": self.task_statuses,
            "alert_levels": [level.value for level in self.alert_levels],
            "alert_sources": self.alert_sources,
            "services": self.services,
            "vendors": self.vendors
        }


@dataclass
class DashboardResponse:
    """Standard API response format for dashboard data"""
    success: bool = True
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    errors: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Pagination info (for list responses)
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    
    def add_error(self, error: str) -> None:
        """Add an error message"""
        self.success = False
        self.errors.append(error)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        response = {
            "success": self.success,
            "message": self.message,
            "data": self.data,
            "errors": self.errors,
            "timestamp": self.timestamp.isoformat()
        }
        
        if self.total_count is not None:
            response["pagination"] = {
                "total_count": self.total_count,
                "page": self.page,
                "page_size": self.page_size
            }
        
        return response


# Utility functions for dashboard data management
def create_dashboard_snapshot(state: DashboardState, config: DashboardConfiguration) -> Dict[str, Any]:
    """Create a complete dashboard snapshot for export or backup"""
    return {
        "snapshot_time": datetime.now().isoformat(),
        "dashboard_state": state.to_dict(),
        "configuration": config.to_dict(),
        "version": "1.0"
    }


def validate_dashboard_data(data: Dict[str, Any]) -> List[str]:
    """Validate dashboard data structure and return list of errors"""
    errors = []
    
    # Check required fields
    required_fields = ["metrics", "alerts", "service_health", "timestamp"]
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")
    
    # Validate metrics structure
    if "metrics" in data:
        metrics = data["metrics"]
        required_metric_types = ["email_metrics", "celery_metrics", "system_metrics"]
        for metric_type in required_metric_types:
            if metric_type not in metrics:
                errors.append(f"Missing metric type: {metric_type}")
    
    # Validate timestamp format
    if "timestamp" in data:
        try:
            datetime.fromisoformat(data["timestamp"])
        except ValueError:
            errors.append("Invalid timestamp format")
    
    return errors


def merge_dashboard_states(states: List[DashboardState]) -> DashboardState:
    """Merge multiple dashboard states (useful for distributed monitoring)"""
    if not states:
        return DashboardState()
    
    if len(states) == 1:
        return states[0]
    
    # Use the most recent state as base
    merged_state = max(states, key=lambda s: s.last_update)
    
    # Merge alerts from all states
    all_alerts = []
    for state in states:
        all_alerts.extend(state.active_alerts)
    
    # Remove duplicates and sort by priority
    unique_alerts = {}
    for alert in all_alerts:
        key = f"{alert.alert_type.value}_{alert.source}_{alert.title}"
        if key not in unique_alerts or alert.triggered_at > unique_alerts[key].triggered_at:
            unique_alerts[key] = alert
    
    merged_state.active_alerts = sorted(unique_alerts.values(), 
                                      key=lambda a: a.get_priority_score(), 
                                      reverse=True)
    
    # Merge service health (keep the worst status for each service)
    all_services = {}
    for state in states:
        for service_name, health in state.service_health.items():
            if (service_name not in all_services or 
                health.last_check > all_services[service_name].last_check):
                all_services[service_name] = health
    
    merged_state.service_health = all_services
    
    # Update overall status
    merged_state.update_overall_status()
    
    return merged_state