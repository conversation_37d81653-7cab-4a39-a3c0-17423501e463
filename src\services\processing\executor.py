"""檔案處理服務 - 執行器
包含執行外部工具的核心邏輯和超時管理
"""

import sys
import asyncio
import threading
from pathlib import Path
from typing import Dict, List, Callable, Optional
from datetime import datetime

from loguru import logger
from .models import (
    ProcessingTask, ProcessingResult, ProcessingStatus, ProcessingTool,
    ProcessingCancellationError, ToolExecutionError
)


class TaskTimeout:
    """任務超時管理器"""
    
    def __init__(self):
        self.timeout_tasks: Dict[str, asyncio.Task] = {}
        self.lock = threading.Lock()
    
    def set_timeout(self, task_id: str, timeout: float, callback: Callable):
        """設定任務超時"""
        async def timeout_handler():
            await asyncio.sleep(timeout)
            await callback(task_id)
        
        with self.lock:
            # 取消現有的超時任務
            if task_id in self.timeout_tasks:
                self.timeout_tasks[task_id].cancel()
            
            # 建立新的超時任務
            self.timeout_tasks[task_id] = asyncio.create_task(timeout_handler())
    
    def cancel_timeout(self, task_id: str):
        """取消任務超時"""
        with self.lock:
            if task_id in self.timeout_tasks:
                self.timeout_tasks[task_id].cancel()
                del self.timeout_tasks[task_id]


class ToolExecutor:
    """工具執行器，負責執行外部處理工具"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.csv_tool_path = project_root / "csv_to_summary.py"
        self.code_tool_path = project_root / "code_comparison.py"
        
        # 驗證工具檔案存在
        if not self.csv_tool_path.exists():
            logger.warning(f"CSV 工具檔案不存在: {self.csv_tool_path}")
        if not self.code_tool_path.exists():
            logger.warning(f"程式碼比較工具檔案不存在: {self.code_tool_path}")
    
    async def execute_csv_summary_workflow(self, task: ProcessingTask, input_path: str) -> ProcessingResult:
        """執行 CSV 摘要工作流程"""
        start_time = datetime.now()
        
        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
            
            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.csv_tool_path),
                str(input_path_obj)
            ]
            
            logger.info(f"執行 CSV 摘要命令: {' '.join(cmd)}")
            
            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            task.process_id = process.pid
            
            try:
                stdout, stderr = await process.communicate()
            except asyncio.CancelledError:
                if process.returncode is None:
                    process.terminate()
                    await process.wait()
                raise ProcessingCancellationError("CSV 摘要執行被取消")
            
            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time
            
            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "csv_summary")
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task.task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"CSV 摘要處理成功: {task.task_id}")
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()
                
                result = ProcessingResult(
                    success=False,
                    task_id=task.task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore'), error_msg]
                )
                
                logger.error(f"CSV 摘要處理失敗: {task.task_id}, 錯誤: {error_msg}")
            
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            task.status = ProcessingStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="csv_to_summary.py",
                error_message=str(e)
            )
            
            logger.error(f"CSV 摘要執行異常: {task.task_id}, 錯誤: {e}")
            return result

    async def execute_code_comparison_workflow(self, task: ProcessingTask, input_path: str) -> ProcessingResult:
        """執行程式碼比較工作流程"""
        start_time = datetime.now()

        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")

            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.code_tool_path),
                str(input_path_obj)
            ]

            logger.info(f"執行程式碼比較命令: {' '.join(cmd)}")

            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )

            task.process_id = process.pid

            try:
                stdout, stderr = await process.communicate()
            except asyncio.CancelledError:
                if process.returncode is None:
                    process.terminate()
                    await process.wait()
                raise ProcessingCancellationError("程式碼比較執行被取消")

            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time

            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0

                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "code_comparison")
                task.output_files = [str(f) for f in output_files]

                result = ProcessingResult(
                    success=True,
                    task_id=task.task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )

                logger.info(f"程式碼比較處理成功: {task.task_id}")

            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()

                result = ProcessingResult(
                    success=False,
                    task_id=task.task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )

                logger.error(f"程式碼比較處理失敗: {task.task_id}, 錯誤: {error_msg}")

            return result

        except ProcessingCancellationError:
            raise
        except Exception as e:
            # 例外處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)

            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.execution_time = processing_time

            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="code_comparison.py",
                error_message=error_msg
            )

            logger.error(f"程式碼比較處理例外: {task.task_id}, 錯誤: {error_msg}")
            return result
        finally:
            task.process_id = None

    def _find_output_files(self, input_path: Path, tool_type: str) -> List[Path]:
        """尋找輸出檔案"""
        output_files = []

        try:
            # 根據工具類型尋找可能的輸出檔案
            if tool_type == "csv_summary":
                # CSV 摘要工具通常會產生 Excel 檔案
                patterns = ["*summary*.xlsx", "*摘要*.xlsx", "*_processed.xlsx"]
            elif tool_type == "code_comparison":
                # 程式碼比較工具通常會產生比較報告
                patterns = ["*comparison*.xlsx", "*比較*.xlsx", "*_diff.xlsx"]
            else:
                patterns = ["*.xlsx", "*.csv"]

            # 在輸入目錄及其父目錄中搜尋
            search_dirs = [input_path]
            if input_path.is_file():
                search_dirs.append(input_path.parent)

            for search_dir in search_dirs:
                if search_dir.is_dir():
                    for pattern in patterns:
                        output_files.extend(search_dir.glob(pattern))

            # 移除重複並按修改時間排序
            output_files = list(set(output_files))
            output_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        except Exception as e:
            logger.warning(f"尋找輸出檔案時發生錯誤: {e}")

        return output_files
