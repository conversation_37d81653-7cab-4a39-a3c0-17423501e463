"""
Dashboard monitoring models package

This package contains all data models for the unified monitoring dashboard:
- dashboard_metrics_models: Core metrics data structures
- dashboard_alert_models: Alert management and notification models  
- dashboard_models: Main dashboard state and configuration models

Requirements covered: 1, 2, 5, 6, 8, 9
"""

# Import core metric models
from .dashboard_metrics_models import (
    # Enums
    MetricType,
    MetricUnit,
    
    # Core metric classes
    MonitoringMetric,
    EmailMetrics,
    CeleryMetrics,
    SystemMetrics,
    FileMetrics,
    BusinessMetrics,
    DashboardMetrics,
    
    # Pydantic models for API validation
    EmailMetricsModel,
    CeleryMetricsModel,
    SystemMetricsModel,
    DashboardMetricsModel
)

# Import alert models
from .dashboard_alert_models import (
    # Enums
    AlertLevel,
    AlertStatus,
    AlertType,
    NotificationChannel,
    
    # Core alert classes
    AlertRule,
    DashboardAlert,
    AlertSummary,
    NotificationConfig,
    AlertHistory,
    
    # Utility functions
    merge_duplicate_alerts,
    prioritize_alerts,
    filter_alerts_by_time,
    get_alert_statistics,
    
    # Pydantic models for API validation
    AlertRuleModel,
    DashboardAlertModel,
    NotificationConfigModel
)

# Import main dashboard models
from .dashboard_models import (
    # Enums
    DashboardStatus,
    ServiceStatus,
    
    # Core dashboard classes
    ServiceHealth,
    DashboardConfiguration,
    DashboardState,
    TaskDetail,
    TrendData,
    DashboardFilter,
    DashboardResponse,
    
    # Utility functions
    create_dashboard_snapshot,
    validate_dashboard_data,
    merge_dashboard_states
)

# Export all public classes and functions
__all__ = [
    # Enums
    "MetricType",
    "MetricUnit", 
    "AlertLevel",
    "AlertStatus",
    "AlertType",
    "NotificationChannel",
    "DashboardStatus",
    "ServiceStatus",
    
    # Metrics models
    "MonitoringMetric",
    "EmailMetrics",
    "CeleryMetrics", 
    "SystemMetrics",
    "FileMetrics",
    "BusinessMetrics",
    "DashboardMetrics",
    
    # Alert models
    "AlertRule",
    "DashboardAlert",
    "AlertSummary",
    "NotificationConfig",
    "AlertHistory",
    
    # Dashboard models
    "ServiceHealth",
    "DashboardConfiguration",
    "DashboardState",
    "TaskDetail",
    "TrendData",
    "DashboardFilter",
    "DashboardResponse",
    
    # Pydantic models
    "EmailMetricsModel",
    "CeleryMetricsModel",
    "SystemMetricsModel", 
    "DashboardMetricsModel",
    "AlertRuleModel",
    "DashboardAlertModel",
    "NotificationConfigModel",
    
    # Utility functions
    "merge_duplicate_alerts",
    "prioritize_alerts",
    "filter_alerts_by_time",
    "get_alert_statistics",
    "create_dashboard_snapshot",
    "validate_dashboard_data",
    "merge_dashboard_states"
]