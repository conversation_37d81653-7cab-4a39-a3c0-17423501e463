"""網路瀏覽器 API - UI和其他路由
處理 UI 介面、健康檢查、儀表板和測試相關功能
"""

import os
import platform
from pathlib import Path
from datetime import datetime
from fastapi import APIRouter
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates

from loguru import logger

try:
    from .network_utils import test_smb_connection
    from .network_models import NetworkCredentials
except ImportError:
    test_smb_connection = None
    NetworkCredentials = None

# 建立路由器
router = APIRouter(tags=["UI & System"])

# 模板設定
templates_path = Path(__file__).parent.parent / "web" / "templates"
templates = Jinja2Templates(directory=str(templates_path)) if templates_path.exists() else None


@router.get("/")
async def network_root():
    """網路瀏覽器根路徑，重定向到 UI 介面"""
    return RedirectResponse(url="/ui", status_code=302)


@router.get("/ui")
async def get_network_browser_ui():
    """提供網路共享瀏覽器的 Web UI 介面"""
    try:
        current_dir = Path(__file__).parent.parent
        template_file = current_dir / "web" / "templates" / "network_browser_new.html"
        
        if template_file.exists():
            with open(template_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            return HTMLResponse(content=html_content)
        else:
            # 回退到簡單的 HTML
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>網路瀏覽器 - 重構版</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .feature { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                    .api-link { color: #007bff; text-decoration: none; }
                    .api-link:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🌐 網路共享瀏覽器 (重構版)</h1>
                    <p>歡迎使用重構後的網路瀏覽器 API！功能已完全模組化。</p>
                    
                    <div class="feature">
                        <h3>📁 網路瀏覽功能</h3>
                        <p>瀏覽網路共享資料夾、下載檔案、管理連接</p>
                    </div>
                    
                    <div class="feature">
                        <h3>🔍 搜尋功能</h3>
                        <p>產品搜尋、智慧搜尋、任務狀態查詢</p>
                    </div>
                    
                    <div class="feature">
                        <h3>📦 暫存功能</h3>
                        <p>檔案暫存、任務管理、進度追蹤</p>
                    </div>
                    
                    <div class="feature">
                        <h3>⚙️ 處理功能</h3>
                        <p>CSV 摘要、程式碼比較、背景任務處理</p>
                    </div>
                    
                    <h3>🔗 快速連結</h3>
                    <ul>
                        <li><a href="/docs" class="api-link">📚 API 文檔</a></li>
                        <li><a href="/health" class="api-link">🏥 健康檢查</a></li>
                        <li><a href="/dashboard" class="api-link">📊 即時儀表板</a></li>
                        <li><a href="/api/staging/tasks" class="api-link">📦 暫存任務列表</a></li>
                        <li><a href="/api/process/tasks" class="api-link">⚙️ 處理任務列表</a></li>
                    </ul>
                    
                    <p><small>重構版本 - 模組化架構 | 更新時間: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</small></p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"載入 UI 介面失敗: {str(e)}")
        return HTMLResponse(content="<h1>網路瀏覽器 UI 載入失敗</h1>", status_code=500)


@router.get("/ui_new")
async def get_network_browser_ui_new():
    """提供網路共享瀏覽器的 Web UI 介面 (新模組化版本)"""
    # 與 /ui 相同的實作，保持向後相容性
    return await get_network_browser_ui()


@router.get("/health")
async def health_check():
    """健康檢查端點"""
    try:
        # 系統資訊
        system_info = {
            "system": platform.system(),
            "node": platform.node(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
        
        # 網路連接測試
        network_status = "unknown"
        mount_points = []
        
        if platform.system() == "Windows":
            try:
                # 簡單的網路連接測試
                import subprocess
                result = subprocess.run(['ping', '-n', '1', '************'], 
                                      capture_output=True, text=True, timeout=5)
                network_status = "connected" if result.returncode == 0 else "disconnected"
            except:
                network_status = "unknown"
        else:
            try:
                # Linux 掛載點檢查
                with open('/proc/mounts', 'r') as f:
                    mounts = f.read()
                    mount_points = [line.split()[1] for line in mounts.split('\n') 
                                  if 'cifs' in line or 'smb' in line]
                network_status = "connected" if mount_points else "no_mounts"
            except:
                network_status = "unknown"
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0-refactored",
            "system": system_info,
            "network": {
                "target_host": "************",
                "connectivity": network_status,
                "available_mounts": mount_points,
                "mount_count": len(mount_points)
            }
        }
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )


@router.get("/dashboard")
async def get_realtime_dashboard():
    """即時監控儀表板"""
    try:
        # 模擬儀表板資料
        dashboard_data = {
            "system_status": "healthy",
            "active_connections": 0,
            "staging_tasks": {
                "total": 0,
                "pending": 0,
                "in_progress": 0,
                "completed": 0,
                "failed": 0
            },
            "processing_tasks": {
                "total": 0,
                "pending": 0,
                "in_progress": 0,
                "completed": 0,
                "failed": 0
            },
            "search_tasks": {
                "total": 0,
                "active": 0,
                "completed": 0
            },
            "network_status": {
                "target_host": "************",
                "connectivity": "unknown",
                "last_check": datetime.now().isoformat()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # 嘗試獲取實際的服務統計
        try:
            from ...services.staging import get_file_staging_service
            staging_service = get_file_staging_service()
            staging_stats = staging_service.get_service_statistics()
            dashboard_data["staging_tasks"] = {
                "total": staging_stats.get("total_tasks", 0),
                "pending": staging_stats.get("pending_tasks", 0),
                "in_progress": staging_stats.get("active_tasks", 0),
                "completed": staging_stats.get("completed_tasks", 0),
                "failed": staging_stats.get("failed_tasks", 0)
            }
        except:
            pass
        
        try:
            from ...services.processing import get_file_processing_service
            processing_service = get_file_processing_service()
            processing_tasks = processing_service.list_tasks()
            dashboard_data["processing_tasks"]["total"] = len(processing_tasks)
        except:
            pass
        
        # 簡單的 HTML 儀表板
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>網路瀏覽器儀表板</title>
            <meta charset="utf-8">
            <meta http-equiv="refresh" content="30">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .card {{ background: white; margin: 15px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .status-healthy {{ color: #28a745; }}
                .status-warning {{ color: #ffc107; }}
                .status-error {{ color: #dc3545; }}
                .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .metric {{ display: flex; justify-content: space-between; margin: 10px 0; }}
                .metric-label {{ font-weight: bold; }}
                .metric-value {{ color: #007bff; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🌐 網路瀏覽器即時儀表板</h1>
                <p class="status-healthy">系統狀態: {dashboard_data['system_status']}</p>
                
                <div class="grid">
                    <div class="card">
                        <h3>📦 暫存任務</h3>
                        <div class="metric">
                            <span class="metric-label">總計:</span>
                            <span class="metric-value">{dashboard_data['staging_tasks']['total']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">進行中:</span>
                            <span class="metric-value">{dashboard_data['staging_tasks']['in_progress']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">已完成:</span>
                            <span class="metric-value">{dashboard_data['staging_tasks']['completed']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">失敗:</span>
                            <span class="metric-value">{dashboard_data['staging_tasks']['failed']}</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3>⚙️ 處理任務</h3>
                        <div class="metric">
                            <span class="metric-label">總計:</span>
                            <span class="metric-value">{dashboard_data['processing_tasks']['total']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">進行中:</span>
                            <span class="metric-value">{dashboard_data['processing_tasks']['in_progress']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">已完成:</span>
                            <span class="metric-value">{dashboard_data['processing_tasks']['completed']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">失敗:</span>
                            <span class="metric-value">{dashboard_data['processing_tasks']['failed']}</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3>🌐 網路狀態</h3>
                        <div class="metric">
                            <span class="metric-label">目標主機:</span>
                            <span class="metric-value">{dashboard_data['network_status']['target_host']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">連接狀態:</span>
                            <span class="metric-value">{dashboard_data['network_status']['connectivity']}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">最後檢查:</span>
                            <span class="metric-value">{dashboard_data['network_status']['last_check'][:19]}</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🔗 快速操作</h3>
                    <p>
                        <a href="/docs">📚 API 文檔</a> | 
                        <a href="/health">🏥 健康檢查</a> | 
                        <a href="/api/staging/tasks">📦 暫存任務</a> | 
                        <a href="/api/process/tasks">⚙️ 處理任務</a>
                    </p>
                </div>
                
                <p><small>更新時間: {dashboard_data['timestamp'][:19]} | 自動刷新: 30秒</small></p>
            </div>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        logger.error(f"載入儀表板失敗: {str(e)}")
        return HTMLResponse(content="<h1>儀表板載入失敗</h1>", status_code=500)


@router.post("/api/test/simple")
async def test_simple():
    """簡單測試端點"""
    logger.info("🧪 簡單測試端點被調用")
    return {"status": "success", "message": "測試端點正常", "timestamp": datetime.now().isoformat()}


@router.get("/test-smb")
async def test_smb_endpoint():
    """測試SMB連接函數"""
    try:
        if test_smb_connection is None or NetworkCredentials is None:
            return {"error": "SMB 測試功能未可用", "function_working": False}
        
        creds = NetworkCredentials(username='telowyield1', password='Telo@2024')
        result = test_smb_connection('************', 'test_log', creds)
        
        return {
            "test_result": result,
            "function_working": True,
            "server": "************",
            "share": "test_log",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"SMB 測試失敗: {str(e)}")
        return {
            "error": str(e),
            "function_working": False,
            "timestamp": datetime.now().isoformat()
        }
