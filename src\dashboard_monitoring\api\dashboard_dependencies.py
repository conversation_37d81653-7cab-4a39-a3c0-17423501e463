"""
統一監控儀表板 - 依賴注入系統
解決服務耦合、測試困難、錯誤處理分散等問題
"""

from fastapi import Depends, HTTPException, status
from typing import Optional, Annotated
import logging
from functools import lru_cache

from ..core.dashboard_monitoring_coordinator import DashboardMonitoringCoordinator
from ..core.dashboard_alert_service import DashboardAlertService
from ..core.dashboard_trend_analyzer import DashboardTrendAnalyzer
from ..repositories.dashboard_monitoring_repository import DashboardMonitoringRepository
from ..repositories.dashboard_alert_repository import DashboardAlertRepository
from ..config.dashboard_config import DashboardConfig

logger = logging.getLogger(__name__)

# ================================
# 配置依賴 - 單例模式
# ================================

@lru_cache()
def get_dashboard_config() -> DashboardConfig:
    """獲取儀表板配置 - 單例模式"""
    try:
        return DashboardConfig()
    except Exception as e:
        logger.error(f"載入儀表板配置失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="系統配置載入失敗"
        )

# ================================
# 資料庫依賴
# ================================

def get_monitoring_repository() -> DashboardMonitoringRepository:
    """獲取監控資料存取服務"""
    try:
        return DashboardMonitoringRepository()
    except Exception as e:
        logger.error(f"初始化監控資料庫失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="監控資料庫服務不可用"
        )

def get_alert_repository() -> DashboardAlertRepository:
    """獲取告警資料存取服務"""
    try:
        return DashboardAlertRepository()
    except Exception as e:
        logger.error(f"初始化告警資料庫失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="告警資料庫服務不可用"
        )

# ================================
# 核心服務依賴 - 必需服務
# ================================

def require_monitoring_coordinator(
    config: Annotated[DashboardConfig, Depends(get_dashboard_config)]
) -> DashboardMonitoringCoordinator:
    """獲取監控協調器 - 必需服務"""
    try:
        coordinator = DashboardMonitoringCoordinator(config)
        if not coordinator.is_healthy():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="監控協調器服務異常"
            )
        return coordinator
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化監控協調器失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="監控協調器初始化失敗"
        )

def require_alert_service(
    config: Annotated[DashboardConfig, Depends(get_dashboard_config)],
    alert_repo: Annotated[DashboardAlertRepository, Depends(get_alert_repository)]
) -> DashboardAlertService:
    """獲取告警服務 - 必需服務"""
    try:
        alert_service = DashboardAlertService(config, alert_repo)
        if not alert_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="告警服務不可用"
            )
        return alert_service
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化告警服務失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="告警服務初始化失敗"
        )

def require_trend_analyzer(
    monitoring_repo: Annotated[DashboardMonitoringRepository, Depends(get_monitoring_repository)]
) -> DashboardTrendAnalyzer:
    """獲取趨勢分析服務 - 必需服務"""
    try:
        analyzer = DashboardTrendAnalyzer(monitoring_repo)
        return analyzer
    except Exception as e:
        logger.error(f"初始化趨勢分析服務失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="趨勢分析服務初始化失敗"
        )

# ================================
# 可選服務依賴 - 降級處理
# ================================

def get_optional_monitoring_coordinator(
    config: Annotated[DashboardConfig, Depends(get_dashboard_config)]
) -> Optional[DashboardMonitoringCoordinator]:
    """獲取監控協調器 - 可選服務，失敗時返回 None"""
    try:
        coordinator = DashboardMonitoringCoordinator(config)
        return coordinator if coordinator.is_healthy() else None
    except Exception as e:
        logger.warning(f"監控協調器不可用: {e}")
        return None

def get_optional_alert_service(
    config: Annotated[DashboardConfig, Depends(get_dashboard_config)]
) -> Optional[DashboardAlertService]:
    """獲取告警服務 - 可選服務，失敗時返回 None"""
    try:
        alert_repo = DashboardAlertRepository()
        alert_service = DashboardAlertService(config, alert_repo)
        return alert_service if alert_service.is_available() else None
    except Exception as e:
        logger.warning(f"告警服務不可用: {e}")
        return None

# ================================
# 複合依賴 - 多服務組合
# ================================

class DashboardServices:
    """儀表板服務集合"""
    def __init__(
        self,
        coordinator: DashboardMonitoringCoordinator,
        alert_service: DashboardAlertService,
        trend_analyzer: DashboardTrendAnalyzer,
        monitoring_repo: DashboardMonitoringRepository,
        alert_repo: DashboardAlertRepository,
        config: DashboardConfig
    ):
        self.coordinator = coordinator
        self.alert_service = alert_service
        self.trend_analyzer = trend_analyzer
        self.monitoring_repo = monitoring_repo
        self.alert_repo = alert_repo
        self.config = config

def get_dashboard_services(
    coordinator: Annotated[DashboardMonitoringCoordinator, Depends(require_monitoring_coordinator)],
    alert_service: Annotated[DashboardAlertService, Depends(require_alert_service)],
    trend_analyzer: Annotated[DashboardTrendAnalyzer, Depends(require_trend_analyzer)],
    monitoring_repo: Annotated[DashboardMonitoringRepository, Depends(get_monitoring_repository)],
    alert_repo: Annotated[DashboardAlertRepository, Depends(get_alert_repository)],
    config: Annotated[DashboardConfig, Depends(get_dashboard_config)]
) -> DashboardServices:
    """獲取完整的儀表板服務集合"""
    return DashboardServices(
        coordinator=coordinator,
        alert_service=alert_service,
        trend_analyzer=trend_analyzer,
        monitoring_repo=monitoring_repo,
        alert_repo=alert_repo,
        config=config
    )

# ================================
# 健康檢查依賴
# ================================

async def check_service_health() -> dict:
    """檢查所有服務健康狀態"""
    health_status = {
        "monitoring_coordinator": False,
        "alert_service": False,
        "trend_analyzer": False,
        "database": False,
        "overall": False
    }
    
    try:
        # 檢查監控協調器
        config = get_dashboard_config()
        coordinator = DashboardMonitoringCoordinator(config)
        health_status["monitoring_coordinator"] = coordinator.is_healthy()
        
        # 檢查告警服務
        alert_repo = DashboardAlertRepository()
        alert_service = DashboardAlertService(config, alert_repo)
        health_status["alert_service"] = alert_service.is_available()
        
        # 檢查趨勢分析
        monitoring_repo = DashboardMonitoringRepository()
        trend_analyzer = DashboardTrendAnalyzer(monitoring_repo)
        health_status["trend_analyzer"] = True
        
        # 檢查資料庫
        health_status["database"] = monitoring_repo.is_healthy()
        
        # 整體健康狀態
        health_status["overall"] = all([
            health_status["monitoring_coordinator"],
            health_status["alert_service"],
            health_status["database"]
        ])
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
    
    return health_status

# ================================
# 類型別名 - 提高可讀性
# ================================

# 必需依賴類型
MonitoringCoordinator = Annotated[DashboardMonitoringCoordinator, Depends(require_monitoring_coordinator)]
AlertService = Annotated[DashboardAlertService, Depends(require_alert_service)]
TrendAnalyzer = Annotated[DashboardTrendAnalyzer, Depends(require_trend_analyzer)]
MonitoringRepository = Annotated[DashboardMonitoringRepository, Depends(get_monitoring_repository)]
AlertRepository = Annotated[DashboardAlertRepository, Depends(get_alert_repository)]
Config = Annotated[DashboardConfig, Depends(get_dashboard_config)]

# 可選依賴類型
OptionalMonitoringCoordinator = Annotated[Optional[DashboardMonitoringCoordinator], Depends(get_optional_monitoring_coordinator)]
OptionalAlertService = Annotated[Optional[DashboardAlertService], Depends(get_optional_alert_service)]

# 複合依賴類型
AllServices = Annotated[DashboardServices, Depends(get_dashboard_services)]