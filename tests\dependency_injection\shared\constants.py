"""測試常數定義
包含所有測試階段使用的常數和配置
"""

from typing import Dict, List

# 測試任務相關常數
TEST_TASK_IDS = {
    "STAGING": "test-staging-task-12345",
    "PROCESSING": "test-processing-task-67890",
    "CSV_SUMMARY": "test-csv-summary-task-11111",
    "CODE_COMPARISON": "test-code-comparison-task-22222"
}

# 測試產品名稱
TEST_PRODUCT_NAMES = {
    "DEFAULT": "TestProduct",
    "SPECIAL_CHARS": "Test-Product_2024",
    "CHINESE": "測試產品",
    "LONG_NAME": "VeryLongProductNameForTestingPurposes2024"
}

# 測試檔案路徑
TEST_SOURCE_FILES = {
    "BASIC": [
        "/test/path/file1.txt",
        "/test/path/file2.csv"
    ],
    "MULTIPLE": [
        "/test/path/data1.csv",
        "/test/path/data2.xlsx",
        "/test/path/config.json",
        "/test/path/readme.md"
    ],
    "NESTED": [
        "/test/project/src/main.py",
        "/test/project/tests/test_main.py",
        "/test/project/docs/api.md"
    ],
    "NETWORK": [
        "\\\\************\\shared\\data\\file1.csv",
        "\\\\************\\shared\\data\\file2.xlsx"
    ]
}

# 錯誤訊息常數
ERROR_MESSAGES = {
    "STAGING_UNAVAILABLE": "檔案暫存服務不可用",
    "PROCESSING_UNAVAILABLE": "檔案處理服務不可用",
    "SERVICE_INIT_FAILED": "服務初始化失敗",
    "DATABASE_CONNECTION_FAILED": "連接資料庫失敗",
    "LLM_SERVICE_TIMEOUT": "LLM 服務連接超時",
    "NETWORK_CONNECTION_FAILED": "網路連接失敗",
    "INSUFFICIENT_PERMISSIONS": "權限不足",
    "DISK_SPACE_INSUFFICIENT": "磁碟空間不足",
    "FILE_NOT_FOUND": "檔案不存在",
    "INVALID_TASK_ID": "無效的任務ID格式"
}

# HTTP 狀態碼
HTTP_STATUS_CODES = {
    "OK": 200,
    "CREATED": 201,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "UNPROCESSABLE_ENTITY": 422,
    "INTERNAL_SERVER_ERROR": 500,
    "STAGING_SERVICE_UNAVAILABLE": 503,  # 修正為統一的錯誤代碼
    "PROCESSING_SERVICE_UNAVAILABLE": 503
}

# 服務 ID 常數
SERVICE_IDS = {
    "STAGING": "file_staging_service",
    "PROCESSING": "file_processing_service",
    "PRODUCT_SEARCH": "product_search_service",
    "LLM_SEARCH": "llm_search_service",
    "MOCK_STAGING": "mock_staging_service",
    "MOCK_PROCESSING": "mock_processing_service"
}

# 任務狀態常數
TASK_STATUSES = {
    "CREATED": "created",
    "PENDING": "pending",
    "RUNNING": "running",
    "COMPLETED": "completed",
    "FAILED": "failed",
    "CANCELLED": "cancelled",
    "TIMEOUT": "timeout"
}

# 處理工具常數
PROCESSING_TOOLS = {
    "CSV_SUMMARY": "csv_summary",
    "CODE_COMPARISON": "code_comparison",
    "EXCEL_CONVERTER": "excel_converter",
    "DATA_ANALYZER": "data_analyzer"
}

# API 端點路徑
API_ENDPOINTS = {
    "STAGING_CREATE": "/api/staging/create",
    "STAGING_EXECUTE": "/api/staging/execute/{task_id}",
    "STAGING_STATUS": "/api/staging/status/{task_id}",
    "STAGING_CANCEL": "/api/staging/cancel/{task_id}",
    "PROCESSING_CSV_SUMMARY": "/api/process/csv-summary-with-staging",
    "PROCESSING_CODE_COMPARISON": "/api/process/code-comparison-with-staging",
    "PROCESSING_EXECUTE": "/api/process/execute/{task_id}",
    "PROCESSING_STATUS": "/api/process/status/{task_id}"
}

# 測試配置
TEST_CONFIG = {
    "DEFAULT_TIMEOUT": 30,
    "MAX_RETRIES": 3,
    "BATCH_SIZE": 10,
    "TEMP_DIR": "/tmp/test_staging",
    "OUTPUT_DIR": "/tmp/test_output",
    "LOG_LEVEL": "DEBUG"
}

# Mock 服務配置
MOCK_SERVICE_CONFIG = {
    "STAGING": {
        "service_id": SERVICE_IDS["MOCK_STAGING"],
        "is_healthy": True,
        "max_tasks": 100,
        "processing_time": 2.5
    },
    "PROCESSING": {
        "service_id": SERVICE_IDS["MOCK_PROCESSING"],
        "is_healthy": True,
        "max_tasks": 50,
        "processing_time": 5.0
    }
}

# 測試數據模板
TEST_DATA_TEMPLATES = {
    "STAGING_TASK_REQUEST": {
        "product_name": TEST_PRODUCT_NAMES["DEFAULT"],
        "source_files": TEST_SOURCE_FILES["BASIC"],
        "preserve_structure": True,
        "use_unique_name": True
    },
    "PROCESSING_TASK_REQUEST": {
        "product_name": TEST_PRODUCT_NAMES["DEFAULT"],
        "source_files": TEST_SOURCE_FILES["BASIC"],
        "preserve_structure": True,
        "use_unique_name": True,
        "tool": PROCESSING_TOOLS["CSV_SUMMARY"]
    },
    "EXPECTED_STAGING_RESPONSE": {
        "success": True,
        "task_id": TEST_TASK_IDS["STAGING"],
        "message": "暫存任務建立成功",
        "product_name": TEST_PRODUCT_NAMES["DEFAULT"],
        "source_files_count": 2,
        "preserve_structure": True,
        "use_unique_name": True
    },
    "EXPECTED_PROCESSING_RESPONSE": {
        "success": True,
        "task_id": TEST_TASK_IDS["PROCESSING"],
        "message": "處理任務建立成功",
        "tool": PROCESSING_TOOLS["CSV_SUMMARY"],
        "product_name": TEST_PRODUCT_NAMES["DEFAULT"],
        "source_files_count": 2,
        "use_staging": True
    }
}

# 測試場景配置
TEST_SCENARIOS = {
    "SERVICE_AVAILABLE": {
        "name": "服務可用",
        "description": "測試服務正常可用時的行為",
        "setup": "mock_service_available"
    },
    "STAGING_SERVICE_UNAVAILABLE": {  # 修正為統一的錯誤代碼
        "name": "暫存服務不可用",
        "description": "測試暫存服務不可用時的錯誤處理",
        "setup": "mock_staging_service_unavailable"
    },
    "PROCESSING_SERVICE_UNAVAILABLE": {  # 新增處理服務不可用場景
        "name": "處理服務不可用",
        "description": "測試處理服務不可用時的錯誤處理",
        "setup": "mock_processing_service_unavailable"
    },
    "SERVICE_INIT_ERROR": {
        "name": "服務初始化錯誤",
        "description": "測試服務初始化失敗時的錯誤處理",
        "setup": "mock_service_init_error"
    },
    "NETWORK_ERROR": {
        "name": "網路錯誤",
        "description": "測試網路連接問題時的錯誤處理",
        "setup": "mock_network_error"
    },
    "TIMEOUT_ERROR": {
        "name": "超時錯誤",
        "description": "測試請求超時時的錯誤處理",
        "setup": "mock_timeout_error"
    }
}

# 驗證規則
VALIDATION_RULES = {
    "TASK_ID_FORMAT": r"^[a-zA-Z0-9\-_]+$",
    "PRODUCT_NAME_MIN_LENGTH": 1,
    "PRODUCT_NAME_MAX_LENGTH": 100,
    "SOURCE_FILES_MIN_COUNT": 1,
    "SOURCE_FILES_MAX_COUNT": 1000,
    "HTTP_TIMEOUT_SECONDS": 30
}

# 性能基準
PERFORMANCE_BENCHMARKS = {
    "API_RESPONSE_TIME_MS": {
        "EXCELLENT": 100,
        "GOOD": 500,
        "ACCEPTABLE": 1000,
        "POOR": 2000
    },
    "MEMORY_USAGE_MB": {
        "LOW": 50,
        "MEDIUM": 100,
        "HIGH": 200,
        "CRITICAL": 500
    },
    "CONCURRENT_REQUESTS": {
        "LIGHT": 10,
        "MEDIUM": 50,
        "HEAVY": 100,
        "STRESS": 500
    }
}

# 測試標籤
TEST_TAGS = {
    "UNIT": "unit",
    "INTEGRATION": "integration",
    "E2E": "e2e",
    "PERFORMANCE": "performance",
    "SMOKE": "smoke",
    "REGRESSION": "regression",
    "DEPENDENCY_INJECTION": "dependency_injection",
    "ERROR_HANDLING": "error_handling",
    "API": "api"
}