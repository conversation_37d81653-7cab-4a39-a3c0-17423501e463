"""
Celery 監控收集器使用範例

展示如何使用 DashboardCeleryCollector 來收集和監控 Celery 任務指標
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.dashboard_monitoring.collectors.dashboard_celery_collector import (
    DashboardCeleryCollector,
    get_celery_collector
)
from src.dashboard_monitoring.config.dashboard_config import DashboardConfig


async def basic_usage_example():
    """基本使用範例"""
    print("🚀 Celery 監控收集器基本使用範例")
    print("=" * 50)
    
    # 創建收集器實例
    collector = DashboardCeleryCollector()
    
    # 收集指標
    print("📊 正在收集 Celery 指標...")
    metrics = await collector.collect_metrics()
    
    # 顯示基本指標
    print(f"⏰ 收集時間: {metrics.timestamp}")
    print(f"🔄 活躍任務: {metrics.total_active}")
    print(f"⏳ 待處理任務: {metrics.total_pending}")
    print(f"✅ 已完成任務: {metrics.total_completed}")
    print(f"❌ 失敗任務: {metrics.total_failed}")
    
    # 顯示任務類型統計
    print("\n📋 任務類型統計:")
    for task_type, counts in metrics.task_type_counts.items():
        print(f"  {task_type}:")
        print(f"    活躍: {counts.get('active', 0)}")
        print(f"    待處理: {counts.get('pending', 0)}")
        print(f"    已完成: {counts.get('completed', 0)}")
        print(f"    失敗: {counts.get('failed', 0)}")
    
    # 顯示工作者狀態
    print("\n👷 工作者狀態:")
    for worker, status in metrics.worker_status.items():
        load = metrics.worker_load.get(worker, 0)
        print(f"  {worker}: {status} (負載: {load})")
    
    # 顯示長時間運行任務
    if metrics.long_running_tasks:
        print("\n⏱️ 長時間運行任務:")
        for task in metrics.long_running_tasks:
            duration_minutes = task['duration'] / 60
            print(f"  {task['name']} ({task['task_id']}): {duration_minutes:.1f} 分鐘")
    else:
        print("\n✨ 沒有長時間運行的任務")


async def health_check_example():
    """健康檢查範例"""
    print("\n🏥 Celery 監控收集器健康檢查範例")
    print("=" * 50)
    
    collector = get_celery_collector()
    
    # 檢查健康狀態
    print("🔍 正在檢查收集器健康狀態...")
    health_status = await collector.get_health_status()
    
    print(f"📊 收集器名稱: {health_status['collector_name']}")
    print(f"💚 狀態: {health_status['status']}")
    print(f"🔗 Celery 連接: {health_status['celery_connected']}")
    print(f"⏰ 最後檢查: {health_status['last_check']}")
    print(f"🛠️ 支援的任務類型: {', '.join(health_status['supported_task_types'])}")


async def continuous_monitoring_example():
    """持續監控範例"""
    print("\n📈 持續監控範例 (收集 5 次)")
    print("=" * 50)
    
    collector = get_celery_collector()
    
    for i in range(5):
        print(f"\n📊 第 {i+1} 次收集:")
        
        # 收集指標
        metrics = await collector.collect_metrics()
        
        # 顯示關鍵指標
        print(f"  時間: {metrics.timestamp.strftime('%H:%M:%S')}")
        print(f"  活躍: {metrics.total_active}, 待處理: {metrics.total_pending}")
        
        # 計算總任務數
        total_tasks = metrics.total_active + metrics.total_pending
        if total_tasks > 0:
            print(f"  📋 總任務數: {total_tasks}")
        else:
            print("  ✨ 目前沒有任務")
        
        # 等待 2 秒
        if i < 4:  # 最後一次不需要等待
            await asyncio.sleep(2)


async def error_handling_example():
    """錯誤處理範例"""
    print("\n🛡️ 錯誤處理範例")
    print("=" * 50)
    
    # 創建收集器
    collector = DashboardCeleryCollector()
    
    try:
        # 嘗試收集指標
        print("🔄 嘗試收集指標...")
        metrics = await collector.collect_metrics()
        
        print("✅ 指標收集成功")
        print(f"📊 收集到的指標類型: {type(metrics).__name__}")
        
    except Exception as e:
        print(f"❌ 收集指標時發生錯誤: {e}")
        print("🔧 收集器應該返回預設指標而不是拋出異常")


async def configuration_example():
    """配置範例"""
    print("\n⚙️ 配置範例")
    print("=" * 50)
    
    # 創建自定義配置
    config = DashboardConfig()
    config.update_intervals.metrics_collection = 60  # 60 秒更新間隔
    
    print(f"📝 自定義配置:")
    print(f"  指標收集間隔: {config.update_intervals.metrics_collection} 秒")
    print(f"  告警評估間隔: {config.update_intervals.alerts_evaluation} 秒")
    print(f"  預設時間範圍: {config.display_config.default_time_range}")
    
    # 使用自定義配置創建收集器
    collector = DashboardCeleryCollector(config)
    
    # 驗證配置生效
    print(f"✅ 收集器配置已套用: {collector.config.update_intervals.metrics_collection} 秒")
    
    # 收集指標
    metrics = await collector.collect_metrics()
    print(f"📊 使用自定義配置收集指標成功")


async def json_export_example():
    """JSON 匯出範例"""
    print("\n💾 JSON 匯出範例")
    print("=" * 50)
    
    collector = get_celery_collector()
    
    # 收集指標
    metrics = await collector.collect_metrics()
    
    # 轉換為字典
    metrics_dict = metrics.to_dict()
    
    # 匯出為 JSON
    json_data = json.dumps(metrics_dict, indent=2, ensure_ascii=False)
    
    print("📄 指標 JSON 格式:")
    print(json_data[:500] + "..." if len(json_data) > 500 else json_data)
    
    # 可選：儲存到文件
    output_file = Path("celery_metrics.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_data)
    
    print(f"💾 指標已儲存到: {output_file}")


async def performance_test_example():
    """效能測試範例"""
    print("\n⚡ 效能測試範例")
    print("=" * 50)
    
    collector = get_celery_collector()
    
    # 測試收集時間
    start_time = datetime.now()
    
    # 進行多次收集
    collection_times = []
    for i in range(10):
        collection_start = datetime.now()
        await collector.collect_metrics()
        collection_end = datetime.now()
        
        collection_time = (collection_end - collection_start).total_seconds()
        collection_times.append(collection_time)
        
        if i == 0:
            print(f"🔄 第一次收集時間: {collection_time:.3f} 秒")
    
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    # 計算統計資料
    avg_time = sum(collection_times) / len(collection_times)
    max_time = max(collection_times)
    min_time = min(collection_times)
    
    print(f"📊 效能統計 (10 次收集):")
    print(f"  總時間: {total_time:.3f} 秒")
    print(f"  平均時間: {avg_time:.3f} 秒")
    print(f"  最大時間: {max_time:.3f} 秒")
    print(f"  最小時間: {min_time:.3f} 秒")
    
    # 檢查效能要求 (應該在 2 秒內)
    if avg_time < 2.0:
        print("✅ 效能符合要求 (< 2 秒)")
    else:
        print("⚠️ 效能可能需要最佳化")


async def main():
    """主函數 - 運行所有範例"""
    print("🎯 Celery 監控收集器完整範例")
    print("=" * 60)
    
    try:
        # 運行各種範例
        await basic_usage_example()
        await health_check_example()
        await continuous_monitoring_example()
        await error_handling_example()
        await configuration_example()
        await json_export_example()
        await performance_test_example()
        
        print("\n🎉 所有範例執行完成！")
        
    except Exception as e:
        print(f"\n❌ 執行範例時發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 運行範例
    asyncio.run(main())