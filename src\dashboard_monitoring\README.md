# 統一監控儀表板 (Unified Monitoring Dashboard)

## 📋 專案概述

統一監控儀表板是一個全方位即時監控系統，專為半導體郵件處理基礎設施設計，提供郵件處理、Celery任務、系統資源和業務指標的統一監控。

## 📁 目錄結構

```
src/dashboard_monitoring/
├── __init__.py                     # 主模組初始化
├── README.md                       # 專案說明文件
├── core/                          # 核心業務邏輯
│   ├── __init__.py
│   ├── dashboard_monitoring_coordinator.py    # 監控協調器
│   ├── dashboard_data_collection_service.py   # 資料收集服務
│   ├── dashboard_alert_service.py             # 告警服務
│   ├── dashboard_trend_analyzer.py            # 趨勢分析服務
│   ├── dashboard_websocket_manager.py         # WebSocket 核心服務 ✅
│   ├── dashboard_cache_service.py             # 快取服務 ✅
│   └── dashboard_cache_manager.py             # 快取管理器 ✅
├── collectors/                    # 資料收集器
│   ├── __init__.py
│   ├── dashboard_email_collector.py           # 郵件監控收集器
│   ├── dashboard_celery_collector.py          # Celery監控收集器 ✅
│   ├── dashboard_system_collector.py          # 系統指標收集器
│   └── dashboard_file_collector.py            # 檔案處理收集器
├── models/                        # 資料模型
│   ├── __init__.py
│   ├── dashboard_models.py                    # 主要儀表板模型
│   ├── dashboard_metrics_models.py            # 指標資料模型
│   └── dashboard_alert_models.py              # 告警資料模型
├── repositories/                  # 資料存取層
│   ├── __init__.py
│   ├── dashboard_monitoring_repository.py     # 監控資料存取
│   └── dashboard_alert_repository.py          # 告警資料存取
├── api/                          # API 端點
│   ├── __init__.py
│   ├── dashboard_dependencies.py              # 依賴注入系統
│   ├── dashboard_monitoring_api.py            # 監控 API
│   └── dashboard_websocket.py                 # WebSocket 端點 ✅ 已完成
├── templates/                     # HTML 模板
│   ├── __init__.py
│   └── dashboard_main.html                    # 主儀表板頁面 ✅ 已完成
├── static/                        # 靜態資源
│   ├── __init__.py
│   ├── css/                                   # CSS 樣式
│   │   ├── dashboard_main.css                 # 主要樣式 ✅ 已完成
│   │   └── dashboard_components.css           # 元件樣式 ✅ 已完成
│   ├── js/                                    # JavaScript
│   │   ├── dashboard_main.js                  # 主要功能 ✅ 已完成
│   │   ├── dashboard_websocket.js             # WebSocket 管理 ✅ 已完成
│   │   └── dashboard_charts.js                # 圖表處理 ✅ 已完成
│   └── images/                                # 圖片資源
│       └── dashboard_icons/
│           └── favicon.ico                    # 網站圖示 ✅ 已完成
├── utils/                         # 工具函數
│   ├── __init__.py
│   ├── dashboard_helpers.py                   # 輔助函數
│   ├── dashboard_formatters.py                # 資料格式化工具
│   └── dashboard_cache_utils.py               # 快取工具函數 ✅
├── config/                        # 配置文件
│   ├── __init__.py
│   ├── dashboard_config.py                    # 儀表板配置
│   ├── dashboard_monitoring_rules.py          # 監控規則配置
│   └── dashboard_cache_config.py              # 快取配置 ✅
├── docs/                          # 文檔
│   ├── cache_service_guide.md                 # 快取服務指南 ✅
│   └── api_documentation.md                   # API 文檔 ✅
└── integration/                   # 系統整合
    ├── __init__.py
    └── dashboard_service_integrator.py        # 服務整合器
```

## 🎯 核心功能

### 監控類型
- **📧 郵件處理監控** - code_comparison.py 任務、廠商分組統計
- **🔄 Celery任務監控** - 長時間任務、工作者狀態、佇列管理  
- **💻 系統資源監控** - CPU/記憶體/磁碟、服務健康狀態
- **📊 業務指標監控** - MO/LOT統計、資料品質、報告生成

### 技術特性
- **✅ 即時更新** - 5秒內反映系統變化 (WebSocket API 已完成)
- **最小侵入** - 不影響現有系統運行
- **歷史分析** - 7天/30天趨勢分析
- **智能告警** - 多管道通知系統
- **高可用性** - 故障自動恢復
- **✅ 高效能快取** - 記憶體快取支援毫秒級回應，TTL 過期機制

## 🏗️ 架構設計

### 分層架構
1. **前端層** - HTML5 + JavaScript + WebSocket
2. **API層** - FastAPI + REST + WebSocket
3. **應用層** - 核心業務邏輯和服務
4. **資料層** - SQLite (擴展現有 outlook.db)

### 設計模式
- **依賴注入** - 統一的服務管理
- **觀察者模式** - 即時資料更新
- **工廠模式** - 收集器創建
- **策略模式** - 不同監控策略

## 🚀 開發指南

### 開發原則
1. **最小侵入原則** - 不影響現有系統
2. **錯誤隔離** - 監控故障不影響主業務
3. **效能優先** - 平衡即時性和系統負載
4. **可擴展性** - 易於添加新監控類型

### 程式碼規範
- 遵循 Python PEP 8 規範
- 使用類型提示 (Type Hints)
- 完整的文檔字串 (Docstrings)
- 單元測試覆蓋率 > 90%

## 📝 相關文件

### 專案文件
- [需求文件](../../.kiro/specs/unified-monitoring-dashboard/requirements.md)
- [設計文件](../../.kiro/specs/unified-monitoring-dashboard/design.md)
- [任務計劃](../../.kiro/specs/unified-monitoring-dashboard/tasks.md)

### 技術文件
- [主儀表板頁面使用指南](./docs/dashboard_main_guide.md) - 完整的儀表板使用說明和功能介紹 ✅
- [快取服務指南](./docs/cache_service_guide.md) - 詳細的快取系統使用指南
- [Celery 收集器指南](./docs/celery_collector_guide.md) - Celery 監控收集器完整使用指南 ✅
- [API 文檔](./docs/api_documentation.md) - 完整的 REST API 和 WebSocket API 文檔
- [WebSocket API 指南](./docs/websocket_api.md) - WebSocket API 完整參考文檔 ✅
- [WebSocket API 使用指南](../../docs/websocket-api-guide.md) - WebSocket 連接和訊息格式詳細說明 ✅

### 已完成功能
- ✅ **主儀表板頁面 (任務 21)** - 統一監控介面
  - 響應式佈局設計，支援桌面、平板、手機
  - 六大監控區域完整實現
  - 即時資料更新顯示 (WebSocket 整合)
  - 互動式 UI 元件 (刷新、展開/收縮、告警確認)
  - 深色模式支援和列印樣式
  - 完整的前端 JavaScript 架構
- ✅ **WebSocket API (任務 20)** - 即時監控資料推送
  - 客戶端連接管理
  - 訂閱和取消訂閱功能
  - 即時資料推送
  - 心跳檢測和錯誤處理
  - 支援多種訊息類型 (metrics_update, alert, system_status 等)
- ✅ **快取服務** - 高效能記憶體快取
- ✅ **Celery 監控收集器** - 任務佇列監控
- ✅ **依賴注入系統** - 統一服務管理

## 🔧 技術棧

- **後端**: Python 3.11+, FastAPI, SQLAlchemy
- **前端**: HTML5, JavaScript ES6+, WebSocket
- **資料庫**: SQLite (outlook.db)
- **測試**: Pytest, 單元測試, 整合測試
- **工具**: Black, Flake8, MyPy

## 📞 聯絡資訊

如有問題或建議，請聯絡開發團隊。