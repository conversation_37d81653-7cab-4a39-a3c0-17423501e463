# Step 1.2.1: 為錯誤處理編寫測試

## 📋 任務概述

**目標：** 在建立統一錯誤處理機制之前，先編寫測試來定義期望的錯誤處理行為  
**狀態：** ✅ 完成
**完成時間：** 2025-08-02 00:55
**執行者：** AI Assistant  

## 🎯 任務目標

根據 TDD 原則，在實現統一錯誤處理機制之前，先編寫測試來定義：

1. **統一的錯誤響應格式** - 標準化所有 API 錯誤響應
2. **錯誤分類和狀態碼** - 不同類型錯誤的正確狀態碼
3. **錯誤訊息國際化** - 支援中文錯誤訊息
4. **錯誤日誌記錄** - 統一的錯誤日誌格式
5. **錯誤恢復機制** - 自動重試和降級處理

## 🔍 當前錯誤處理問題分析

### **發現的問題：**

#### **1. 錯誤響應格式不一致**
```python
# 在 ft_eqc_api.py 中
{
    "status": "error",
    "message": str(exc.detail),
    "error_code": exc.status_code
}

# 在 dependencies.py 中
HTTPException(
    status_code=503,
    detail=f"檔案暫存服務不可用: {error_msg}"
)

# 在 api_utils.py 中
{
    "error": str(exc), 
    "type": "internal_error",
    "timestamp": datetime.now().isoformat()
}
```

#### **2. 狀態碼使用不統一**
- 有些地方用 500，有些用 503
- 缺少明確的錯誤分類

#### **3. 錯誤訊息格式混亂**
- 有些包含時間戳，有些沒有
- 錯誤 ID 不一致
- 缺少追蹤信息

## 🎯 期望的統一錯誤處理行為

### **1. 標準錯誤響應格式**
```json
{
    "success": false,
    "error": {
        "code": "SERVICE_UNAVAILABLE",
        "message": "檔案暫存服務不可用",
        "details": "連接資料庫失敗",
        "timestamp": "2025-08-02T00:35:00Z",
        "trace_id": "req_12345",
        "path": "/api/staging/create"
    },
    "status_code": 503
}
```

### **2. 錯誤分類體系**
```python
class ErrorCategory(Enum):
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
```

### **3. 錯誤恢復策略**
```python
class ErrorRecoveryStrategy(Enum):
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAKER = "circuit_breaker"
    FAIL_FAST = "fail_fast"
```

## 📝 測試設計

### **測試文件結構：**
```
tests/dependency_injection/phase1_foundation/step1.2_error_handling/
├── test_error_handling.py          # 主要錯誤處理測試
├── test_error_categories.py        # 錯誤分類測試
├── test_error_recovery.py          # 錯誤恢復機制測試
└── test_error_formatting.py        # 錯誤格式化測試
```

## 🧪 測試用例設計

### **1. 錯誤響應格式測試**
- 測試標準錯誤響應結構
- 測試必需字段的存在
- 測試時間戳格式
- 測試追蹤 ID 生成

### **2. 錯誤分類測試**
- 測試服務不可用錯誤 (503)
- 測試驗證錯誤 (422)
- 測試認證錯誤 (401)
- 測試授權錯誤 (403)
- 測試資源不存在錯誤 (404)
- 測試超時錯誤 (408)
- 測試內部錯誤 (500)

### **3. 錯誤恢復測試**
- 測試自動重試機制
- 測試降級處理
- 測試熔斷器模式
- 測試快速失敗

### **4. 錯誤日誌測試**
- 測試錯誤日誌格式
- 測試日誌級別
- 測試敏感信息過濾

## ✅ 完成檢查清單

- [x] 📝 創建錯誤處理測試目錄
- [x] 🧪 編寫錯誤響應格式測試
- [x] 🧪 編寫錯誤分類測試
- [x] 🧪 編寫錯誤恢復機制測試
- [x] 🧪 編寫錯誤日誌測試
- [x] 🧪 編寫依賴注入錯誤處理測試
- [x] 🔍 驗證測試覆蓋所有錯誤場景
- [x] 📄 創建詳細測試文檔

## 🧪 測試執行結果

### **執行命令：**
```bash
python tests/dependency_injection/phase1_foundation/step1.2_error_handling/test_error_handling.py
```

### **結果：**
```
📊 測試結果: 0 通過, 10 失敗
⚠️ 有測試失敗，這是正常的（TDD Red 階段）
🔄 下一步：實現統一錯誤處理機制讓測試通過
```

### **創建的測試文件：**
- `test_error_handling.py` - 主要錯誤處理測試 (300行)
- `test_error_categories.py` - 錯誤分類測試 (300行)

### **測試覆蓋範圍：**
- ✅ **統一錯誤響應格式** - 10個測試用例
- ✅ **錯誤分類體系** - 15個錯誤類型
- ✅ **錯誤恢復機制** - 重試、熔斷器、降級
- ✅ **錯誤日誌記錄** - 格式化、敏感數據過濾

## 🔗 相關文件

- **目標實現文件：** `src/presentation/api/dependencies.py`
- **現有錯誤處理：** `src/presentation/api/services/api_utils.py`
- **FastAPI 異常處理：** `src/presentation/api/ft_eqc_api.py`
- **測試常數：** `tests/dependency_injection/shared/constants.py`

## ➡️ 下一步

**Step 1.2.2:** 根據測試實現統一的錯誤處理機制

---

**📝 文檔創建時間：** 2025-08-02 00:35  
**🔄 最後更新：** 2025-08-02 00:35
