"""統一監控儀表板監控規則配置

定義各種監控規則和告警條件：
- 告警規則定義
- 條件評估邏輯
- 規則管理功能
- 動態規則更新

符合需求 12 的告警機制要求
"""

import json
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path

from .dashboard_config import AlertLevel, get_dashboard_config

logger = logging.getLogger(__name__)


class ConditionType(Enum):
    """條件類型枚舉"""
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    REGEX_MATCH = "regex_match"
    RANGE = "range"
    PERCENTAGE_CHANGE = "percentage_change"


class RuleCategory(Enum):
    """規則類別枚舉"""
    EMAIL = "email"
    CELERY = "celery"
    SYSTEM = "system"
    FILE = "file"
    BUSINESS = "business"
    DATABASE = "database"


class RuleStatus(Enum):
    """規則狀態枚舉"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    TESTING = "testing"


@dataclass
class AlertCondition:
    """告警條件定義"""
    condition_type: ConditionType
    threshold_value: Union[int, float, str, List[Union[int, float]]]
    comparison_field: str  # 要比較的欄位路徑，如 "email.pending_count"
    
    # 可選參數
    time_window: Optional[int] = None  # 時間窗口（秒）
    min_occurrences: int = 1  # 最少觸發次數
    description: str = ""
    
    def evaluate(self, current_value: Any, historical_values: Optional[List[Any]] = None) -> bool:
        """評估條件是否滿足"""
        try:
            if self.condition_type == ConditionType.GREATER_THAN:
                return float(current_value) > float(self.threshold_value)
            
            elif self.condition_type == ConditionType.LESS_THAN:
                return float(current_value) < float(self.threshold_value)
            
            elif self.condition_type == ConditionType.EQUALS:
                return current_value == self.threshold_value
            
            elif self.condition_type == ConditionType.NOT_EQUALS:
                return current_value != self.threshold_value
            
            elif self.condition_type == ConditionType.GREATER_EQUAL:
                return float(current_value) >= float(self.threshold_value)
            
            elif self.condition_type == ConditionType.LESS_EQUAL:
                return float(current_value) <= float(self.threshold_value)
            
            elif self.condition_type == ConditionType.CONTAINS:
                return str(self.threshold_value) in str(current_value)
            
            elif self.condition_type == ConditionType.NOT_CONTAINS:
                return str(self.threshold_value) not in str(current_value)
            
            elif self.condition_type == ConditionType.RANGE:
                if isinstance(self.threshold_value, list) and len(self.threshold_value) == 2:
                    min_val, max_val = self.threshold_value
                    return min_val <= float(current_value) <= max_val
                return False
            
            elif self.condition_type == ConditionType.PERCENTAGE_CHANGE:
                if historical_values and len(historical_values) > 0:
                    previous_value = historical_values[-1]
                    if previous_value != 0:
                        change_percent = ((float(current_value) - float(previous_value)) / float(previous_value)) * 100
                        return abs(change_percent) > float(self.threshold_value)
                return False
            
            elif self.condition_type == ConditionType.REGEX_MATCH:
                import re
                pattern = str(self.threshold_value)
                return bool(re.match(pattern, str(current_value)))
            
            return False
            
        except (ValueError, TypeError, AttributeError) as e:
            logger.warning(f"條件評估失敗: {e}")
            return False


@dataclass
class MonitoringRule:
    """監控規則定義"""
    rule_id: str
    rule_name: str
    category: RuleCategory
    alert_level: AlertLevel
    conditions: List[AlertCondition]
    
    # 規則配置
    status: RuleStatus = RuleStatus.ENABLED
    cooldown_period: int = 300  # 冷卻期（秒）
    max_alerts_per_hour: int = 10  # 每小時最大告警數
    
    # 告警內容
    alert_title: str = ""
    alert_message: str = ""
    alert_tags: Dict[str, str] = field(default_factory=dict)
    
    # 通知配置
    notification_channels: List[str] = field(default_factory=list)
    escalation_rules: List[str] = field(default_factory=list)
    
    # 時間配置
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0
    
    def is_enabled(self) -> bool:
        """檢查規則是否啟用"""
        return self.status == RuleStatus.ENABLED
    
    def is_in_cooldown(self) -> bool:
        """檢查是否在冷卻期內"""
        if not self.last_triggered:
            return False
        
        cooldown_end = self.last_triggered + timedelta(seconds=self.cooldown_period)
        return datetime.now() < cooldown_end
    
    def can_trigger_alert(self) -> bool:
        """檢查是否可以觸發告警"""
        if not self.is_enabled():
            return False
        
        if self.is_in_cooldown():
            return False
        
        # 檢查每小時告警限制
        one_hour_ago = datetime.now() - timedelta(hours=1)
        if self.last_triggered and self.last_triggered > one_hour_ago:
            if self.trigger_count >= self.max_alerts_per_hour:
                return False
        
        return True
    
    def evaluate_conditions(self, metrics_data: Dict[str, Any], 
                          historical_data: Optional[Dict[str, List[Any]]] = None) -> bool:
        """評估所有條件"""
        if not self.conditions:
            return False
        
        # 所有條件都必須滿足（AND邏輯）
        for condition in self.conditions:
            field_path = condition.comparison_field
            current_value = self._get_nested_value(metrics_data, field_path)
            
            if current_value is None:
                logger.warning(f"無法獲取欄位值: {field_path}")
                return False
            
            historical_values = None
            if historical_data and field_path in historical_data:
                historical_values = historical_data[field_path]
            
            if not condition.evaluate(current_value, historical_values):
                return False
        
        return True
    
    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """獲取嵌套欄位值"""
        try:
            keys = field_path.split('.')
            value = data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            
            return value
            
        except (KeyError, TypeError, AttributeError):
            return None
    
    def trigger_alert(self) -> None:
        """觸發告警"""
        self.last_triggered = datetime.now()
        self.trigger_count += 1
        self.updated_at = datetime.now()
    
    def reset_trigger_count(self) -> None:
        """重置觸發計數"""
        self.trigger_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "category": self.category.value,
            "alert_level": self.alert_level.value,
            "status": self.status.value,
            "cooldown_period": self.cooldown_period,
            "max_alerts_per_hour": self.max_alerts_per_hour,
            "alert_title": self.alert_title,
            "alert_message": self.alert_message,
            "alert_tags": self.alert_tags,
            "notification_channels": self.notification_channels,
            "escalation_rules": self.escalation_rules,
            "conditions": [
                {
                    "condition_type": cond.condition_type.value,
                    "threshold_value": cond.threshold_value,
                    "comparison_field": cond.comparison_field,
                    "time_window": cond.time_window,
                    "min_occurrences": cond.min_occurrences,
                    "description": cond.description
                }
                for cond in self.conditions
            ],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_triggered": self.last_triggered.isoformat() if self.last_triggered else None,
            "trigger_count": self.trigger_count
        }


class MonitoringRulesManager:
    """監控規則管理器"""
    
    def __init__(self, rules_file: Optional[Path] = None):
        self.rules_file = rules_file or Path("config/dashboard_monitoring_rules.json")
        self.rules: Dict[str, MonitoringRule] = {}
        
        # 載入規則
        self._load_rules()
        
        # 如果沒有規則，創建預設規則
        if not self.rules:
            self._create_default_rules()
    
    def _load_rules(self) -> None:
        """載入規則檔案"""
        if not self.rules_file.exists():
            logger.info(f"規則檔案 {self.rules_file} 不存在，將創建預設規則")
            return
        
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)
            
            for rule_data in rules_data.get("rules", []):
                rule = self._dict_to_rule(rule_data)
                self.rules[rule.rule_id] = rule
            
            logger.info(f"成功載入 {len(self.rules)} 個監控規則")
            
        except Exception as e:
            logger.error(f"載入規則檔案失敗: {e}")
    
    def _dict_to_rule(self, rule_data: Dict[str, Any]) -> MonitoringRule:
        """從字典創建規則物件"""
        conditions = []
        for cond_data in rule_data.get("conditions", []):
            condition = AlertCondition(
                condition_type=ConditionType(cond_data["condition_type"]),
                threshold_value=cond_data["threshold_value"],
                comparison_field=cond_data["comparison_field"],
                time_window=cond_data.get("time_window"),
                min_occurrences=cond_data.get("min_occurrences", 1),
                description=cond_data.get("description", "")
            )
            conditions.append(condition)
        
        rule = MonitoringRule(
            rule_id=rule_data["rule_id"],
            rule_name=rule_data["rule_name"],
            category=RuleCategory(rule_data["category"]),
            alert_level=AlertLevel(rule_data["alert_level"]),
            conditions=conditions,
            status=RuleStatus(rule_data.get("status", "enabled")),
            cooldown_period=rule_data.get("cooldown_period", 300),
            max_alerts_per_hour=rule_data.get("max_alerts_per_hour", 10),
            alert_title=rule_data.get("alert_title", ""),
            alert_message=rule_data.get("alert_message", ""),
            alert_tags=rule_data.get("alert_tags", {}),
            notification_channels=rule_data.get("notification_channels", []),
            escalation_rules=rule_data.get("escalation_rules", []),
            trigger_count=rule_data.get("trigger_count", 0)
        )
        
        # 處理時間欄位
        if "created_at" in rule_data:
            rule.created_at = datetime.fromisoformat(rule_data["created_at"])
        if "updated_at" in rule_data:
            rule.updated_at = datetime.fromisoformat(rule_data["updated_at"])
        if "last_triggered" in rule_data and rule_data["last_triggered"]:
            rule.last_triggered = datetime.fromisoformat(rule_data["last_triggered"])
        
        return rule
    
    def _create_default_rules(self) -> None:
        """創建預設監控規則"""
        config = get_dashboard_config()
        
        # 郵件佇列告警規則
        self.add_rule(MonitoringRule(
            rule_id="email_pending_warning",
            rule_name="郵件待處理佇列警告",
            category=RuleCategory.EMAIL,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.email_pending_warning,
                    comparison_field="email.pending_count",
                    description="郵件待處理數量超過警告閾值"
                )
            ],
            alert_title="郵件佇列警告",
            alert_message="郵件待處理數量過多，請檢查郵件處理服務",
            notification_channels=["system"]
        ))
        
        self.add_rule(MonitoringRule(
            rule_id="email_pending_critical",
            rule_name="郵件待處理佇列嚴重",
            category=RuleCategory.EMAIL,
            alert_level=AlertLevel.CRITICAL,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.email_pending_critical,
                    comparison_field="email.pending_count",
                    description="郵件待處理數量超過嚴重閾值"
                )
            ],
            alert_title="郵件佇列嚴重警告",
            alert_message="郵件待處理數量嚴重過多，需要立即處理",
            notification_channels=["system", "email"]
        ))
        
        # Celery任務告警規則
        self.add_rule(MonitoringRule(
            rule_id="celery_pending_warning",
            rule_name="Celery任務佇列警告",
            category=RuleCategory.CELERY,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.celery_pending_warning,
                    comparison_field="celery.total_pending",
                    description="Celery待處理任務數量超過警告閾值"
                )
            ],
            alert_title="Celery任務佇列警告",
            alert_message="Celery待處理任務過多，請檢查工作者狀態",
            notification_channels=["system"]
        ))
        
        # 系統資源告警規則
        self.add_rule(MonitoringRule(
            rule_id="cpu_usage_warning",
            rule_name="CPU使用率警告",
            category=RuleCategory.SYSTEM,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.cpu_warning,
                    comparison_field="system.cpu_percent",
                    description="CPU使用率超過警告閾值"
                )
            ],
            alert_title="CPU使用率警告",
            alert_message="系統CPU使用率過高，請檢查系統負載",
            notification_channels=["system"]
        ))
        
        self.add_rule(MonitoringRule(
            rule_id="memory_usage_critical",
            rule_name="記憶體使用率嚴重",
            category=RuleCategory.SYSTEM,
            alert_level=AlertLevel.CRITICAL,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.memory_critical,
                    comparison_field="system.memory_percent",
                    description="記憶體使用率超過嚴重閾值"
                )
            ],
            alert_title="記憶體使用率嚴重警告",
            alert_message="系統記憶體使用率嚴重過高，可能影響系統穩定性",
            notification_channels=["system", "email"]
        ))
        
        # 磁碟空間告警規則
        self.add_rule(MonitoringRule(
            rule_id="disk_usage_critical",
            rule_name="磁碟使用率嚴重",
            category=RuleCategory.SYSTEM,
            alert_level=AlertLevel.CRITICAL,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.disk_critical,
                    comparison_field="system.disk_percent",
                    description="磁碟使用率超過嚴重閾值"
                )
            ],
            alert_title="磁碟空間嚴重警告",
            alert_message="磁碟空間嚴重不足，請立即清理或擴展儲存空間",
            notification_channels=["system", "email"]
        ))
        
        # 任務失敗率告警規則
        self.add_rule(MonitoringRule(
            rule_id="task_failure_rate_warning",
            rule_name="任務失敗率警告",
            category=RuleCategory.CELERY,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.celery_failure_rate_warning * 100,  # 轉換為百分比
                    comparison_field="celery.failure_rate_percent",
                    description="任務失敗率超過警告閾值"
                )
            ],
            alert_title="任務失敗率警告",
            alert_message="任務失敗率過高，請檢查任務執行狀況",
            notification_channels=["system"]
        ))
        
        # 資料庫查詢時間告警規則
        self.add_rule(MonitoringRule(
            rule_id="db_query_time_warning",
            rule_name="資料庫查詢時間警告",
            category=RuleCategory.DATABASE,
            alert_level=AlertLevel.WARNING,
            conditions=[
                AlertCondition(
                    condition_type=ConditionType.GREATER_THAN,
                    threshold_value=config.alert_thresholds.db_query_time_warning,
                    comparison_field="system.database_query_avg_time",
                    description="資料庫平均查詢時間超過警告閾值"
                )
            ],
            alert_title="資料庫查詢時間警告",
            alert_message="資料庫查詢時間過長，可能影響系統效能",
            notification_channels=["system"]
        ))
        
        logger.info(f"已創建 {len(self.rules)} 個預設監控規則")
        self.save_rules()
    
    def add_rule(self, rule: MonitoringRule) -> None:
        """新增規則"""
        self.rules[rule.rule_id] = rule
        logger.info(f"新增監控規則: {rule.rule_name}")
    
    def remove_rule(self, rule_id: str) -> bool:
        """移除規則"""
        if rule_id in self.rules:
            rule_name = self.rules[rule_id].rule_name
            del self.rules[rule_id]
            logger.info(f"移除監控規則: {rule_name}")
            return True
        return False
    
    def get_rule(self, rule_id: str) -> Optional[MonitoringRule]:
        """獲取規則"""
        return self.rules.get(rule_id)
    
    def get_rules_by_category(self, category: RuleCategory) -> List[MonitoringRule]:
        """按類別獲取規則"""
        return [rule for rule in self.rules.values() if rule.category == category]
    
    def get_enabled_rules(self) -> List[MonitoringRule]:
        """獲取所有啟用的規則"""
        return [rule for rule in self.rules.values() if rule.is_enabled()]
    
    def enable_rule(self, rule_id: str) -> bool:
        """啟用規則"""
        if rule_id in self.rules:
            self.rules[rule_id].status = RuleStatus.ENABLED
            self.rules[rule_id].updated_at = datetime.now()
            return True
        return False
    
    def disable_rule(self, rule_id: str) -> bool:
        """停用規則"""
        if rule_id in self.rules:
            self.rules[rule_id].status = RuleStatus.DISABLED
            self.rules[rule_id].updated_at = datetime.now()
            return True
        return False
    
    def evaluate_all_rules(self, metrics_data: Dict[str, Any], 
                          historical_data: Optional[Dict[str, List[Any]]] = None) -> List[MonitoringRule]:
        """評估所有規則，返回觸發的規則列表"""
        triggered_rules = []
        
        for rule in self.get_enabled_rules():
            if rule.can_trigger_alert():
                if rule.evaluate_conditions(metrics_data, historical_data):
                    rule.trigger_alert()
                    triggered_rules.append(rule)
        
        return triggered_rules
    
    def save_rules(self) -> None:
        """保存規則到檔案"""
        self.rules_file.parent.mkdir(parents=True, exist_ok=True)
        
        rules_data = {
            "version": "1.0",
            "updated_at": datetime.now().isoformat(),
            "rules": [rule.to_dict() for rule in self.rules.values()]
        }
        
        try:
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, indent=2, ensure_ascii=False)
            logger.info(f"規則已保存到: {self.rules_file}")
        except Exception as e:
            logger.error(f"保存規則失敗: {e}")
            raise
    
    def reload_rules(self) -> None:
        """重新載入規則"""
        self.rules.clear()
        self._load_rules()
        
        if not self.rules:
            self._create_default_rules()
    
    def get_rule_statistics(self) -> Dict[str, Any]:
        """獲取規則統計資訊"""
        total_rules = len(self.rules)
        enabled_rules = len(self.get_enabled_rules())
        disabled_rules = total_rules - enabled_rules
        
        category_counts = {}
        for category in RuleCategory:
            category_counts[category.value] = len(self.get_rules_by_category(category))
        
        level_counts = {}
        for level in AlertLevel:
            level_counts[level.value] = len([
                rule for rule in self.rules.values() 
                if rule.alert_level == level
            ])
        
        return {
            "total_rules": total_rules,
            "enabled_rules": enabled_rules,
            "disabled_rules": disabled_rules,
            "category_counts": category_counts,
            "level_counts": level_counts,
            "last_updated": max(
                (rule.updated_at for rule in self.rules.values()),
                default=datetime.now()
            ).isoformat()
        }


# 全域規則管理器實例
_rules_manager: Optional[MonitoringRulesManager] = None


def get_monitoring_rules_manager() -> MonitoringRulesManager:
    """獲取全域監控規則管理器實例"""
    global _rules_manager
    
    if _rules_manager is None:
        _rules_manager = MonitoringRulesManager()
    
    return _rules_manager


def init_monitoring_rules_manager(rules_file: Optional[Path] = None) -> MonitoringRulesManager:
    """初始化監控規則管理器"""
    global _rules_manager
    _rules_manager = MonitoringRulesManager(rules_file=rules_file)
    return _rules_manager


def reload_monitoring_rules() -> MonitoringRulesManager:
    """重新載入監控規則"""
    global _rules_manager
    if _rules_manager:
        _rules_manager.reload_rules()
    else:
        _rules_manager = MonitoringRulesManager()
    
    return _rules_manager