"""
統一監控儀表板 - WebSocket 管理核心服務

提供 WebSocket 連接管理的核心業務邏輯，與監控協調器整合。
這個模組專注於業務邏輯，而 API 層的 dashboard_websocket.py 專注於 FastAPI 整合。

符合需求 4：即時更新和通知機制
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass

from ..config.dashboard_config import DashboardConfig
from ..models.dashboard_models import DashboardState
from ..utils.dashboard_helpers import log_performance, handle_dashboard_error

logger = logging.getLogger(__name__)


@dataclass
class DashboardWebSocketStats:
    """WebSocket 統計資訊"""
    active_connections: int = 0
    total_connections: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    uptime_seconds: float = 0.0
    last_broadcast: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "active_connections": self.active_connections,
            "total_connections": self.total_connections,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "uptime_seconds": self.uptime_seconds,
            "last_broadcast": self.last_broadcast.isoformat() if self.last_broadcast else None
        }


class DashboardWebSocketService:
    """統一監控儀表板 WebSocket 核心服務
    
    提供 WebSocket 管理的核心業務邏輯：
    - 與監控協調器整合
    - 管理廣播策略
    - 提供統計資訊
    - 處理服務生命週期
    """
    
    def __init__(self, config: DashboardConfig):
        self.config = config
        self.is_running = False
        self.start_time = datetime.now()
        
        # 統計資訊
        self.stats = DashboardWebSocketStats()
        
        # WebSocket 管理器引用（延遲初始化）
        self._websocket_manager = None
        
        logger.info("WebSocket 核心服務已初始化")
    
    async def start_service(self) -> None:
        """啟動 WebSocket 服務"""
        if self.is_running:
            logger.warning("WebSocket 服務已在運行中")
            return
        
        try:
            # 延遲導入避免循環依賴
            from ..api.dashboard_websocket import get_websocket_manager
            
            self._websocket_manager = get_websocket_manager()
            await self._websocket_manager.start_background_tasks()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("WebSocket 服務已啟動")
            
        except Exception as e:
            logger.error(f"啟動 WebSocket 服務失敗: {e}")
            raise
    
    async def stop_service(self) -> None:
        """停止 WebSocket 服務"""
        if not self.is_running:
            return
        
        try:
            if self._websocket_manager:
                await self._websocket_manager.stop_background_tasks()
            
            self.is_running = False
            logger.info("WebSocket 服務已停止")
            
        except Exception as e:
            logger.error(f"停止 WebSocket 服務時發生錯誤: {e}")
    
    @log_performance
    async def broadcast_dashboard_state(self, dashboard_state: DashboardState) -> None:
        """廣播完整的儀表板狀態"""
        if not self.is_running or not self._websocket_manager:
            logger.debug("WebSocket 服務未運行，跳過廣播")
            return
        
        try:
            # 準備廣播資料
            broadcast_data = {
                "dashboard_state": dashboard_state.to_dict(),
                "timestamp": datetime.now().isoformat(),
                "update_type": "full_state"
            }
            
            # 廣播指標更新
            await self._websocket_manager.broadcast_metrics_update(broadcast_data)
            
            # 更新統計
            self.stats.last_broadcast = datetime.now()
            
            logger.debug("儀表板狀態廣播完成")
            
        except Exception as e:
            logger.error(f"廣播儀表板狀態失敗: {e}")
    
    @log_performance
    async def broadcast_metrics_update(self, metrics_data: Dict[str, Any]) -> None:
        """廣播指標更新"""
        if not self.is_running or not self._websocket_manager:
            return
        
        try:
            # 添加時間戳和更新類型
            enhanced_data = {
                **metrics_data,
                "timestamp": datetime.now().isoformat(),
                "update_type": "metrics"
            }
            
            await self._websocket_manager.broadcast_metrics_update(enhanced_data)
            self.stats.last_broadcast = datetime.now()
            
        except Exception as e:
            logger.error(f"廣播指標更新失敗: {e}")
    
    @log_performance
    async def broadcast_alert(self, alert_data: Dict[str, Any]) -> None:
        """廣播告警"""
        if not self.is_running or not self._websocket_manager:
            return
        
        try:
            # 添加時間戳和更新類型
            enhanced_data = {
                **alert_data,
                "timestamp": datetime.now().isoformat(),
                "update_type": "alert"
            }
            
            await self._websocket_manager.broadcast_alert(enhanced_data)
            self.stats.last_broadcast = datetime.now()
            
            logger.info(f"告警廣播完成: {alert_data.get('title', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"廣播告警失敗: {e}")
    
    @log_performance
    async def broadcast_system_status(self, status_data: Dict[str, Any]) -> None:
        """廣播系統狀態"""
        if not self.is_running or not self._websocket_manager:
            return
        
        try:
            # 添加時間戳和更新類型
            enhanced_data = {
                **status_data,
                "timestamp": datetime.now().isoformat(),
                "update_type": "system_status"
            }
            
            await self._websocket_manager.broadcast_system_status(enhanced_data)
            self.stats.last_broadcast = datetime.now()
            
        except Exception as e:
            logger.error(f"廣播系統狀態失敗: {e}")
    
    async def broadcast_email_queue_update(self, email_data: Dict[str, Any]) -> None:
        """廣播郵件佇列更新"""
        enhanced_data = {
            **email_data,
            "category": "email_queue",
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_metrics_update(enhanced_data)
    
    async def broadcast_celery_tasks_update(self, celery_data: Dict[str, Any]) -> None:
        """廣播 Celery 任務更新"""
        enhanced_data = {
            **celery_data,
            "category": "celery_tasks",
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_metrics_update(enhanced_data)
    
    async def broadcast_business_metrics_update(self, business_data: Dict[str, Any]) -> None:
        """廣播業務指標更新"""
        enhanced_data = {
            **business_data,
            "category": "business_metrics",
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_metrics_update(enhanced_data)
    
    def get_service_stats(self) -> DashboardWebSocketStats:
        """獲取服務統計資訊"""
        # 更新運行時間
        if self.is_running:
            self.stats.uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        
        # 從 WebSocket 管理器獲取最新統計
        if self._websocket_manager:
            connection_stats = self._websocket_manager.get_connection_stats()
            self.stats.active_connections = connection_stats["current_connections"]
            self.stats.total_connections = connection_stats["total_connections"]
            self.stats.messages_sent = connection_stats["total_messages_sent"]
            self.stats.messages_received = connection_stats["total_messages_received"]
        
        return self.stats
    
    def get_connection_info(self) -> Dict[str, Any]:
        """獲取連接資訊"""
        if not self._websocket_manager:
            return {
                "service_running": self.is_running,
                "connections": 0,
                "max_connections": self.config.websocket_config.max_connections
            }
        
        connection_stats = self._websocket_manager.get_connection_stats()
        return {
            "service_running": self.is_running,
            "connections": connection_stats["current_connections"],
            "max_connections": connection_stats["max_connections"],
            "total_connections": connection_stats["total_connections"],
            "uptime_seconds": connection_stats["uptime_seconds"],
            "message_queue_size": connection_stats["message_queue_size"]
        }
    
    def is_healthy(self) -> bool:
        """檢查服務健康狀態"""
        if not self.is_running:
            return False
        
        try:
            # 檢查 WebSocket 管理器是否正常
            if not self._websocket_manager:
                return False
            
            # 檢查連接數是否在合理範圍內
            stats = self.get_service_stats()
            if stats.active_connections > self.config.websocket_config.max_connections:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")
            return False
    
    async def cleanup_connections(self) -> int:
        """清理無效連接"""
        if not self._websocket_manager:
            return 0
        
        try:
            # 觸發清理任務
            # 這裡可以添加更多清理邏輯
            stats_before = self._websocket_manager.get_connection_stats()
            connections_before = stats_before["current_connections"]
            
            # WebSocket 管理器的清理任務會自動運行
            # 這裡只是提供一個手動觸發的介面
            
            await asyncio.sleep(0.1)  # 給清理任務一些時間
            
            stats_after = self._websocket_manager.get_connection_stats()
            connections_after = stats_after["current_connections"]
            
            cleaned = connections_before - connections_after
            if cleaned > 0:
                logger.info(f"清理了 {cleaned} 個無效連接")
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清理連接時發生錯誤: {e}")
            return 0


# 全域 WebSocket 服務實例
_websocket_service: Optional[DashboardWebSocketService] = None


def get_websocket_service(config: Optional[DashboardConfig] = None) -> DashboardWebSocketService:
    """獲取 WebSocket 服務單例"""
    global _websocket_service
    
    if _websocket_service is None:
        if config is None:
            from ..config.dashboard_config import get_dashboard_config
            config = get_dashboard_config()
        
        _websocket_service = DashboardWebSocketService(config)
    
    return _websocket_service


async def initialize_websocket_service(config: Optional[DashboardConfig] = None) -> DashboardWebSocketService:
    """初始化並啟動 WebSocket 服務"""
    service = get_websocket_service(config)
    
    if not service.is_running:
        await service.start_service()
    
    return service


async def shutdown_websocket_service() -> None:
    """關閉 WebSocket 服務"""
    global _websocket_service
    
    if _websocket_service and _websocket_service.is_running:
        await _websocket_service.stop_service()