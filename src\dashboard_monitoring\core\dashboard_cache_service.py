"""
統一監控儀表板快取服務
提供高效能的記憶體快取功能，支援 TTL 過期機制和快取統計
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict
import weakref
import gc
import sys


@dataclass
class CacheEntry:
    """快取項目"""
    key: str
    value: Any
    created_at: float
    expires_at: Optional[float]
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    size_bytes: int = 0
    
    def is_expired(self) -> bool:
        """檢查是否已過期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def touch(self) -> None:
        """更新存取時間和計數"""
        self.last_accessed = time.time()
        self.access_count += 1


@dataclass
class CacheStatistics:
    """快取統計資料"""
    total_entries: int = 0
    total_size_bytes: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    expired_count: int = 0
    memory_usage_mb: float = 0.0
    hit_rate: float = 0.0
    
    # 按類型統計
    entries_by_type: Dict[str, int] = field(default_factory=dict)
    size_by_type: Dict[str, int] = field(default_factory=dict)
    
    # 時間統計
    avg_access_time_ms: float = 0.0
    last_cleanup_time: Optional[datetime] = None
    
    def calculate_hit_rate(self) -> None:
        """計算命中率"""
        total_requests = self.hit_count + self.miss_count
        if total_requests > 0:
            self.hit_rate = self.hit_count / total_requests
        else:
            self.hit_rate = 0.0


class DashboardCacheService:
    """統一監控儀表板快取服務"""
    
    def __init__(self, 
                 max_size: int = 1000,
                 default_ttl: int = 300,  # 5分鐘
                 cleanup_interval: int = 60,  # 1分鐘清理一次
                 max_memory_mb: int = 256):  # 最大記憶體使用 256MB
        """
        初始化快取服務
        
        Args:
            max_size: 最大快取項目數量
            default_ttl: 預設 TTL (秒)
            cleanup_interval: 清理間隔 (秒)
            max_memory_mb: 最大記憶體使用 (MB)
        """
        self.logger = logging.getLogger(__name__)
        
        # 快取配置
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cleanup_interval = cleanup_interval
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        # 快取儲存
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        
        # 統計資料
        self.stats = CacheStatistics()
        
        # 清理任務
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 記憶體監控
        self._memory_pressure = False
        
        self.logger.info(f"快取服務已初始化 - 最大項目: {max_size}, 預設TTL: {default_ttl}秒")
    
    async def start(self) -> None:
        """啟動快取服務"""
        if self._running:
            return
            
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.logger.info("快取服務已啟動")
    
    async def stop(self) -> None:
        """停止快取服務"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.clear()
        self.logger.info("快取服務已停止")
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取快取值"""
        start_time = time.time()
        
        with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                self.stats.miss_count += 1
                self.logger.debug(f"快取未命中: {key}")
                return default
            
            if entry.is_expired():
                self._remove_entry(key, entry)
                self.stats.miss_count += 1
                self.stats.expired_count += 1
                self.logger.debug(f"快取已過期: {key}")
                return default
            
            # 更新存取資訊
            entry.touch()
            self.stats.hit_count += 1
            
            # 更新平均存取時間
            access_time_ms = (time.time() - start_time) * 1000
            self._update_avg_access_time(access_time_ms)
            
            self.logger.debug(f"快取命中: {key}")
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """設定快取值"""
        if ttl is None:
            ttl = self.default_ttl
        
        # 計算值的大小
        size_bytes = self._calculate_size(value)
        
        with self._lock:
            # 檢查記憶體壓力
            if self._check_memory_pressure(size_bytes):
                self._handle_memory_pressure()
            
            # 檢查是否需要清理空間
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # 計算過期時間
            expires_at = time.time() + ttl if ttl > 0 else None
            
            # 創建快取項目
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                expires_at=expires_at,
                size_bytes=size_bytes
            )
            
            # 如果已存在，先移除舊項目
            if key in self._cache:
                old_entry = self._cache[key]
                self._remove_entry(key, old_entry)
            
            # 添加新項目
            self._cache[key] = entry
            self._update_stats_on_add(entry)
            
            self.logger.debug(f"快取已設定: {key}, TTL: {ttl}秒, 大小: {size_bytes}位元組")
            return True
    
    def delete(self, key: str) -> bool:
        """刪除快取項目"""
        with self._lock:
            entry = self._cache.get(key)
            if entry:
                self._remove_entry(key, entry)
                self.logger.debug(f"快取已刪除: {key}")
                return True
            return False
    
    def exists(self, key: str) -> bool:
        """檢查快取項目是否存在且未過期"""
        with self._lock:
            entry = self._cache.get(key)
            if entry and not entry.is_expired():
                return True
            return False
    
    def clear(self) -> None:
        """清空所有快取"""
        with self._lock:
            cleared_count = len(self._cache)
            self._cache.clear()
            self._reset_stats()
            self.logger.info(f"快取已清空，移除 {cleared_count} 個項目")
    
    def get_keys(self, pattern: Optional[str] = None) -> List[str]:
        """獲取所有快取鍵"""
        with self._lock:
            keys = list(self._cache.keys())
            
            if pattern:
                import fnmatch
                keys = [key for key in keys if fnmatch.fnmatch(key, pattern)]
            
            return keys
    
    def get_statistics(self) -> CacheStatistics:
        """獲取快取統計資料"""
        with self._lock:
            # 更新統計資料
            self.stats.total_entries = len(self._cache)
            self.stats.total_size_bytes = sum(entry.size_bytes for entry in self._cache.values())
            self.stats.memory_usage_mb = self.stats.total_size_bytes / (1024 * 1024)
            self.stats.calculate_hit_rate()
            
            # 按類型統計
            self._update_type_statistics()
            
            return self.stats
    
    def get_cache_info(self) -> Dict[str, Any]:
        """獲取快取詳細資訊"""
        stats = self.get_statistics()
        
        with self._lock:
            # 獲取最近存取的項目
            recent_entries = sorted(
                self._cache.values(),
                key=lambda x: x.last_accessed,
                reverse=True
            )[:10]
            
            # 獲取最大的項目
            largest_entries = sorted(
                self._cache.values(),
                key=lambda x: x.size_bytes,
                reverse=True
            )[:5]
            
            return {
                'statistics': {
                    'total_entries': stats.total_entries,
                    'total_size_mb': round(stats.memory_usage_mb, 2),
                    'hit_rate': round(stats.hit_rate * 100, 2),
                    'hit_count': stats.hit_count,
                    'miss_count': stats.miss_count,
                    'eviction_count': stats.eviction_count,
                    'expired_count': stats.expired_count,
                    'avg_access_time_ms': round(stats.avg_access_time_ms, 2)
                },
                'configuration': {
                    'max_size': self.max_size,
                    'default_ttl': self.default_ttl,
                    'max_memory_mb': self.max_memory_bytes // (1024 * 1024),
                    'cleanup_interval': self.cleanup_interval
                },
                'recent_entries': [
                    {
                        'key': entry.key,
                        'size_bytes': entry.size_bytes,
                        'access_count': entry.access_count,
                        'last_accessed': datetime.fromtimestamp(entry.last_accessed).isoformat(),
                        'expires_at': datetime.fromtimestamp(entry.expires_at).isoformat() if entry.expires_at else None
                    }
                    for entry in recent_entries
                ],
                'largest_entries': [
                    {
                        'key': entry.key,
                        'size_bytes': entry.size_bytes,
                        'size_mb': round(entry.size_bytes / (1024 * 1024), 2)
                    }
                    for entry in largest_entries
                ],
                'memory_pressure': self._memory_pressure,
                'running': self._running
            }
    
    async def _cleanup_loop(self) -> None:
        """清理循環"""
        while self._running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                if self._running:
                    self._cleanup_expired()
                    self._check_memory_usage()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"清理循環錯誤: {e}")
    
    def _cleanup_expired(self) -> None:
        """清理過期項目"""
        with self._lock:
            expired_keys = []
            current_time = time.time()
            
            for key, entry in self._cache.items():
                if entry.expires_at and current_time > entry.expires_at:
                    expired_keys.append(key)
            
            for key in expired_keys:
                entry = self._cache[key]
                self._remove_entry(key, entry)
                self.stats.expired_count += 1
            
            if expired_keys:
                self.logger.debug(f"清理了 {len(expired_keys)} 個過期項目")
            
            self.stats.last_cleanup_time = datetime.now()
    
    def _evict_lru(self) -> None:
        """移除最少使用的項目"""
        if not self._cache:
            return
        
        # 找到最少使用的項目
        lru_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_accessed)
        entry = self._cache[lru_key]
        
        self._remove_entry(lru_key, entry)
        self.stats.eviction_count += 1
        
        self.logger.debug(f"LRU 移除項目: {lru_key}")
    
    def _remove_entry(self, key: str, entry: CacheEntry) -> None:
        """移除快取項目"""
        if key in self._cache:
            del self._cache[key]
            self._update_stats_on_remove(entry)
    
    def _update_stats_on_add(self, entry: CacheEntry) -> None:
        """添加項目時更新統計"""
        # 這裡可以添加更詳細的統計邏輯
        pass
    
    def _update_stats_on_remove(self, entry: CacheEntry) -> None:
        """移除項目時更新統計"""
        # 這裡可以添加更詳細的統計邏輯
        pass
    
    def _update_type_statistics(self) -> None:
        """更新按類型的統計"""
        type_counts = defaultdict(int)
        type_sizes = defaultdict(int)
        
        for entry in self._cache.values():
            value_type = type(entry.value).__name__
            type_counts[value_type] += 1
            type_sizes[value_type] += entry.size_bytes
        
        self.stats.entries_by_type = dict(type_counts)
        self.stats.size_by_type = dict(type_sizes)
    
    def _calculate_size(self, value: Any) -> int:
        """計算值的大小（位元組）"""
        try:
            return sys.getsizeof(value)
        except Exception:
            # 如果無法計算大小，使用預設值
            return 1024
    
    def _check_memory_pressure(self, additional_size: int = 0) -> bool:
        """檢查記憶體壓力"""
        current_size = sum(entry.size_bytes for entry in self._cache.values())
        total_size = current_size + additional_size
        
        pressure = total_size > self.max_memory_bytes * 0.8  # 80% 閾值
        self._memory_pressure = pressure
        
        return pressure
    
    def _handle_memory_pressure(self) -> None:
        """處理記憶體壓力"""
        self.logger.warning("記憶體壓力過高，開始清理快取")
        
        # 清理過期項目
        self._cleanup_expired()
        
        # 如果仍然有壓力，移除一些 LRU 項目
        while (self._check_memory_pressure() and 
               len(self._cache) > self.max_size * 0.5):  # 保留至少 50%
            self._evict_lru()
        
        # 強制垃圾回收
        gc.collect()
    
    def _check_memory_usage(self) -> None:
        """檢查記憶體使用情況"""
        if self._check_memory_pressure():
            self._handle_memory_pressure()
    
    def _update_avg_access_time(self, access_time_ms: float) -> None:
        """更新平均存取時間"""
        if self.stats.avg_access_time_ms == 0:
            self.stats.avg_access_time_ms = access_time_ms
        else:
            # 使用指數移動平均
            alpha = 0.1
            self.stats.avg_access_time_ms = (
                alpha * access_time_ms + 
                (1 - alpha) * self.stats.avg_access_time_ms
            )
    
    def _reset_stats(self) -> None:
        """重置統計資料"""
        self.stats = CacheStatistics()


# 全域快取服務實例
_cache_service_instance: Optional[DashboardCacheService] = None
_cache_service_lock = threading.Lock()


def get_cache_service(
    max_size: int = 1000,
    default_ttl: int = 300,
    cleanup_interval: int = 60,
    max_memory_mb: int = 256
) -> DashboardCacheService:
    """獲取快取服務單例"""
    global _cache_service_instance
    
    with _cache_service_lock:
        if _cache_service_instance is None:
            _cache_service_instance = DashboardCacheService(
                max_size=max_size,
                default_ttl=default_ttl,
                cleanup_interval=cleanup_interval,
                max_memory_mb=max_memory_mb
            )
        
        return _cache_service_instance


async def initialize_cache_service() -> DashboardCacheService:
    """初始化並啟動快取服務"""
    cache_service = get_cache_service()
    await cache_service.start()
    return cache_service


async def shutdown_cache_service() -> None:
    """關閉快取服務"""
    global _cache_service_instance
    
    if _cache_service_instance:
        await _cache_service_instance.stop()
        _cache_service_instance = None