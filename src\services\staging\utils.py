"""檔案暫存服務 - 工具函數和輔助類別
包含檔案鎖定、進度批次更新器和任務監控器
"""

import os
import time
import threading
from pathlib import Path
from typing import Dict, Any
from datetime import datetime
from contextlib import contextmanager

from loguru import logger
from .models import StagingTimeoutError


@contextmanager
def file_lock(file_path: Path, timeout: float = 30.0):
    """檔案鎖定上下文管理器（跨平台相容）"""
    import platform
    
    # Windows 使用檔案鎖定的簡化版本
    if platform.system() == "Windows":
        lock_file = Path(f"{file_path}.lock")
        start_time = time.time()
        
        while True:
            try:
                # 嘗試建立鎖定檔案
                with open(lock_file, 'x') as f:
                    f.write(f"{os.getpid()}:{datetime.now().isoformat()}")
                break
            except FileExistsError:
                # 檢查超時
                if time.time() - start_time > timeout:
                    raise StagingTimeoutError(f"無法在 {timeout} 秒內獲得檔案鎖: {file_path}")
                
                # 檢查鎖定檔案是否過期
                try:
                    if lock_file.exists():
                        stat = lock_file.stat()
                        # 如果鎖定檔案超過5分鐘，視為過期
                        if time.time() - stat.st_mtime > 300:
                            logger.warning(f"發現過期的鎖定檔案，正在清理: {lock_file}")
                            lock_file.unlink()
                            continue
                except Exception as e:
                    logger.warning(f"檢查鎖定檔案時發生錯誤: {e}")
                
                # 短暫等待後重試
                time.sleep(0.1)
        
        try:
            yield lock_file
        finally:
            # 清理鎖定檔案
            try:
                if lock_file.exists():
                    lock_file.unlink()
            except Exception as e:
                logger.warning(f"清理鎖定檔案時發生錯誤: {e}")
    else:
        # Linux/Unix 使用 fcntl
        try:
            import fcntl
            # 原始的 fcntl 實作
            lock_file = open(file_path, 'a')
            try:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                yield lock_file
            finally:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                lock_file.close()
        except ImportError:
            # fcntl 不可用時的回退方案
            logger.warning("fcntl 不可用，使用簡化的鎖定機制")
            lock_file = Path(f"{file_path}.lock")
            try:
                with open(lock_file, 'x') as f:
                    f.write(f"{os.getpid()}:{datetime.now().isoformat()}")
                yield lock_file
            finally:
                if lock_file.exists():
                    lock_file.unlink()


class ProgressBatcher:
    """進度批次更新器，減少鎖競爭"""
    
    def __init__(self, update_interval: int = 10):
        self.update_interval = update_interval
        self.pending_updates = {}
        self.update_count = 0
        self.lock = threading.Lock()
    
    def add_progress(self, task_id: str, bytes_copied: int):
        """添加進度更新"""
        with self.lock:
            if task_id not in self.pending_updates:
                self.pending_updates[task_id] = 0
            self.pending_updates[task_id] += bytes_copied
            self.update_count += 1
    
    def should_flush(self) -> bool:
        """檢查是否應該刷新更新"""
        return self.update_count >= self.update_interval
    
    def flush_updates(self) -> Dict[str, int]:
        """刷新並返回待更新的進度"""
        with self.lock:
            updates = self.pending_updates.copy()
            self.pending_updates.clear()
            self.update_count = 0
            return updates


class TaskMonitor:
    """任務監控器"""
    
    def __init__(self):
        self.task_metrics = {}
        self.lock = threading.Lock()
    
    def record_metric(self, task_id: str, metric_name: str, value: Any):
        """記錄任務指標"""
        with self.lock:
            if task_id not in self.task_metrics:
                self.task_metrics[task_id] = {}
            self.task_metrics[task_id][metric_name] = value
    
    def get_metrics(self, task_id: str) -> Dict[str, Any]:
        """獲取任務指標"""
        with self.lock:
            return self.task_metrics.get(task_id, {}).copy()
    
    def cleanup_metrics(self, task_id: str):
        """清理任務指標"""
        with self.lock:
            self.task_metrics.pop(task_id, None)
