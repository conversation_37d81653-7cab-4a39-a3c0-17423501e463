# 儀表板工具函數模組

本模組提供統一監控儀表板所需的工具函數，包含錯誤處理、效能監控、資料格式化等功能。

## 模組結構

```
src/dashboard_monitoring/utils/
├── __init__.py                 # 模組初始化和匯出
├── dashboard_helpers.py        # 錯誤處理和效能監控工具
├── dashboard_formatters.py     # 資料格式化工具
└── README.md                   # 本文件
```

## 主要功能

### 1. 錯誤處理裝飾器

提供多種錯誤處理裝飾器，確保監控系統故障不影響主要業務功能：

```python
from src.dashboard_monitoring.utils.dashboard_helpers import (
    dashboard_error_handler,
    dashboard_async_error_handler,
    dashboard_monitor
)

# 基本錯誤處理
@dashboard_error_handler(fallback_value="預設值", suppress_exceptions=True)
def risky_operation():
    # 可能失敗的操作
    pass

# 非同步錯誤處理
@dashboard_async_error_handler(fallback_value=[], suppress_exceptions=True)
async def async_risky_operation():
    # 可能失敗的非同步操作
    pass

# 組合裝飾器（錯誤處理 + 效能監控）
@dashboard_monitor(
    fallback_value={},
    operation_name="重要操作",
    log_performance=True,
    slow_threshold_seconds=2.0
)
def important_operation():
    # 重要的業務操作
    pass
```

### 2. 效能監控工具

提供效能監控和追蹤功能：

```python
from src.dashboard_monitoring.utils.dashboard_helpers import (
    dashboard_performance_monitor,
    DashboardPerformanceTracker
)

# 效能監控裝飾器
@dashboard_performance_monitor(
    operation_name="資料收集",
    log_slow_operations=True,
    slow_threshold_seconds=1.0
)
def collect_data():
    # 資料收集邏輯
    pass

# 手動效能追蹤
with DashboardPerformanceTracker("手動追蹤操作") as tracker:
    # 執行需要追蹤的操作
    tracker.add_metric("處理項目數", 100)
```

### 3. 通用輔助函數

提供常用的資料格式化函數：

```python
from src.dashboard_monitoring.utils.dashboard_helpers import (
    format_timestamp,
    calculate_percentage,
    format_bytes,
    format_duration
)

# 時間戳格式化
formatted_time = format_timestamp(datetime.now())
# 輸出: "2023-12-25 15:30:45"

# 百分比計算
percentage = calculate_percentage(75, 100)
# 輸出: 75.0

# 位元組大小格式化
size_str = format_bytes(1572864)
# 輸出: "1.50 MiB"

# 持續時間格式化
duration_str = format_duration(3665)
# 輸出: "1小時 1分鐘 5秒"
```

### 4. 資料格式化工具

提供專門的資料格式化功能：

```python
from src.dashboard_monitoring.utils.dashboard_formatters import (
    format_metrics_data,
    format_alert_data,
    format_trend_data,
    format_dashboard_response
)

# 指標資料格式化
metrics = {"cpu_percent": 75.5, "memory_usage": 1024000000}
formatted_metrics = format_metrics_data(metrics, include_metadata=True)

# 告警資料格式化
alerts = [{"level": "warning", "message": "CPU 使用率過高"}]
formatted_alerts = format_alert_data(alerts, group_by_level=True)

# 趨勢資料格式化
trend_data = [{"timestamp": datetime.now(), "value": 75.5}]
formatted_trends = format_trend_data(trend_data, time_range="1h")

# 統一回應格式化
response = format_dashboard_response(
    data=formatted_metrics,
    status="success",
    message="資料獲取成功"
)
```

### 5. 統一日誌系統

整合現有的 LoggerManager，提供儀表板專用的日誌功能：

```python
from src.dashboard_monitoring.utils.dashboard_helpers import (
    get_dashboard_logger_manager,
    log_dashboard_operation,
    create_dashboard_log_context
)
from src.infrastructure.logging.logger_manager import LogLevel

# 獲取日誌管理器
logger_manager = get_dashboard_logger_manager()
logger = logger_manager.get_logger("my_component")

# 記錄儀表板操作
log_dashboard_operation(
    operation="資料收集",
    component="email_collector",
    level=LogLevel.INFO,
    message="成功收集郵件指標",
    email_count=25,
    processing_time=1.5
)

# 創建日誌上下文
context = create_dashboard_log_context(
    operation="指標計算",
    component="metrics_calculator",
    metric_type="cpu_usage",
    value=75.5
)
```

## 設計原則

### 1. 最小侵入原則
- 所有錯誤處理都有預設的回退值
- 監控系統故障不會影響主要業務功能
- 使用裝飾器模式，不改變原有函數邏輯

### 2. 錯誤隔離
- 單一收集器失敗不影響其他收集器
- 格式化錯誤不會導致系統崩潰
- 日誌系統錯誤不會影響業務邏輯

### 3. 效能考量
- 非同步日誌處理，避免阻塞主線程
- 智能採樣，控制資料點數量
- 批量操作支援，提高處理效率

### 4. 統一標準
- 統一的命名規範（使用 dashboard_ 前綴）
- 統一的日誌格式和上下文
- 統一的錯誤處理策略

## 使用建議

### 1. 錯誤處理
- 對於關鍵業務邏輯，使用 `suppress_exceptions=False` 確保錯誤被正確處理
- 對於監控和輔助功能，使用 `suppress_exceptions=True` 提供回退值
- 總是提供有意義的 `fallback_value`

### 2. 效能監控
- 為重要操作設置合理的 `slow_threshold_seconds`
- 使用 `operation_name` 提供清晰的操作標識
- 在需要詳細追蹤時使用 `DashboardPerformanceTracker`

### 3. 資料格式化
- 使用 `include_metadata=True` 獲取額外的調試資訊
- 對於大量資料，設置合理的 `max_data_points` 限制
- 使用資料驗證函數確保資料格式正確

### 4. 日誌記錄
- 使用結構化日誌，包含足夠的上下文資訊
- 選擇適當的日誌級別
- 避免在高頻操作中記錄過多日誌

## 測試

模組包含完整的單元測試和整合測試：

```bash
# 執行單元測試
python -m pytest tests/unit/test_dashboard_helpers.py -v
python -m pytest tests/unit/test_dashboard_formatters.py -v

# 執行整合測試
python -m pytest tests/integration/test_dashboard_logging_integration.py -v
```

## 依賴關係

- `src.infrastructure.logging.logger_manager`: 核心日誌系統
- `datetime`, `timedelta`: 時間處理
- `asyncio`: 非同步操作支援
- `json`: JSON 序列化
- `dataclasses`: 資料類別支援

## 版本資訊

- 版本: 1.0.0
- 建立日期: 2025-08-02
- 最後更新: 2025-08-02
- 作者: Kiro AI Assistant

## 更新記錄

### v1.1.0 (2025-08-02)
- ✅ 新增 WebSocket 管理服務支援
- ✅ 新增 WebSocket 相關的錯誤處理和效能監控
- ✅ 新增 WebSocket 訊息格式化工具
- ✅ 更新測試覆蓋，包含 WebSocket 功能測試

### v1.0.0 (2025-08-02)
- ✅ 初始版本發布
- ✅ 實現錯誤處理裝飾器系統
- ✅ 實現效能監控工具
- ✅ 實現資料格式化工具
- ✅ 整合統一日誌系統
- ✅ 完整的測試覆蓋（58個單元測試 + 12個整合測試）
- ✅ 通過 Kiro IDE 自動格式化和代碼品質檢查