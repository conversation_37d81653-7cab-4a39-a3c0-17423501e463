"""錯誤類別定義

包含所有 API 錯誤的基礎類別和具體實現
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from fastapi import HTTPException


class ErrorRecoveryStrategy(Enum):
    """錯誤恢復策略"""
    RETRY = "retry"
    FALLBACK = "fallback" 
    CIRCUIT_BREAKER = "circuit_breaker"
    FAIL_FAST = "fail_fast"


class BaseAPIError(Exception):
    """API 錯誤基礎類別"""
    
    def __init__(
        self,
        error_code: str,
        message: str,
        status_code: int,
        details: Optional[str] = None,
        recovery_strategy: ErrorRecoveryStrategy = ErrorRecoveryStrategy.FAIL_FAST,
        path: Optional[str] = None,
        trace_id: Optional[str] = None
    ):
        super().__init__(message)
        self.error_code = error_code
        self.message = message
        self.status_code = status_code
        self.details = details
        self.recovery_strategy = recovery_strategy
        self.path = path
        self.trace_id = trace_id or str(uuid.uuid4())
        self.timestamp = datetime.now().isoformat() + "Z"
    
    def to_http_exception(self) -> HTTPException:
        return HTTPException(status_code=self.status_code, detail=self.to_error_response())
    
    @property
    def recovery_strategy_value(self) -> str:
        """返回恢復策略的字符串值（用於測試兼容性）"""
        return self.recovery_strategy.value if self.recovery_strategy else None

    def to_error_response(self) -> Dict[str, Any]:
        return {
            "success": False,
            "error": {
                "code": self.error_code,
                "message": self.message,
                "details": self.details,
                "timestamp": self.timestamp,
                "trace_id": self.trace_id,
                "path": self.path
            },
            "status_code": self.status_code
        }


# 基礎分類
class ServiceError(BaseAPIError): pass
class ValidationError(BaseAPIError): pass
class ResourceError(BaseAPIError): pass
class OperationError(BaseAPIError): pass
class SystemError(BaseAPIError): pass


# 服務錯誤
class StagingServiceUnavailableError(ServiceError):
    def __init__(self, details: Optional[str] = None):
        super().__init__(
            error_code="STAGING_SERVICE_UNAVAILABLE",
            message="檔案暫存服務不可用",
            status_code=503,
            details=details,
            recovery_strategy=ErrorRecoveryStrategy.RETRY
        )


class ProcessingServiceUnavailableError(ServiceError):
    def __init__(self, details: Optional[str] = None):
        super().__init__(
            error_code="PROCESSING_SERVICE_UNAVAILABLE", 
            message="檔案處理服務不可用",
            status_code=503,
            details=details,
            recovery_strategy=ErrorRecoveryStrategy.RETRY
        )


class ServiceInitializationError(ServiceError):
    def __init__(self, service_name: str, initialization_error: str):
        super().__init__(
            error_code="SERVICE_INITIALIZATION_ERROR",
            message=f"{service_name}初始化失敗",
            status_code=503,
            details=initialization_error,
            recovery_strategy=ErrorRecoveryStrategy.FAIL_FAST
        )


class ExternalServiceError(ServiceError):
    def __init__(self, service_name: str, service_error: str):
        super().__init__(
            error_code="EXTERNAL_SERVICE_ERROR",
            message=f"外部服務 '{service_name}' 錯誤",
            status_code=502,
            details=service_error,
            recovery_strategy=ErrorRecoveryStrategy.CIRCUIT_BREAKER
        )
        self.service_error = service_error


# 驗證錯誤
class RequiredFieldMissingError(ValidationError):
    def __init__(self, field_name: str):
        super().__init__(
            error_code="REQUIRED_FIELD_MISSING",
            message=f"必需字段 '{field_name}' 缺失",
            status_code=422
        )


class InvalidFieldFormatError(ValidationError):
    def __init__(self, field_name: str, field_value: Any, expected_format: str):
        super().__init__(
            error_code="INVALID_FIELD_FORMAT",
            message=f"字段 '{field_name}' 格式無效，期望格式：{expected_format}",
            status_code=422,
            details=f"實際值：{field_value}"
        )


class FieldValueOutOfRangeError(ValidationError):
    def __init__(self, field_name: str, field_value: Any, min_value: Any, max_value: Any):
        super().__init__(
            error_code="FIELD_VALUE_OUT_OF_RANGE",
            message=f"字段 '{field_name}' 值超出範圍 [{min_value}, {max_value}]",
            status_code=422,
            details=f"實際值：{field_value}"
        )
        self.min_value = min_value
        self.max_value = max_value


# 資源錯誤
class TaskNotFoundError(ResourceError):
    def __init__(self, task_id: str):
        super().__init__(
            error_code="TASK_NOT_FOUND",
            message=f"任務 '{task_id}' 不存在",
            status_code=404
        )


class FileNotFoundError(ResourceError):
    def __init__(self, file_path: str):
        super().__init__(
            error_code="FILE_NOT_FOUND",
            message=f"檔案 '{file_path}' 不存在",
            status_code=404
        )


class InsufficientPermissionsError(ResourceError):
    def __init__(self, resource_path: str, required_permission: str):
        super().__init__(
            error_code="INSUFFICIENT_PERMISSIONS",
            message=f"對資源 '{resource_path}' 缺少 '{required_permission}' 權限",
            status_code=403
        )


# 操作錯誤
class OperationTimeoutError(OperationError):
    def __init__(self, operation: str, timeout_seconds: int):
        super().__init__(
            error_code="OPERATION_TIMEOUT",
            message=f"操作 '{operation}' 超時（{timeout_seconds}秒）",
            status_code=408,
            recovery_strategy=ErrorRecoveryStrategy.RETRY
        )
        self.timeout_seconds = timeout_seconds


class OperationCancelledError(OperationError):
    def __init__(self, operation: str, reason: str):
        super().__init__(
            error_code="OPERATION_CANCELLED",
            message=f"操作 '{operation}' 已取消",
            status_code=409,
            details=reason
        )
        self.reason = reason


class ConcurrentOperationConflictError(OperationError):
    def __init__(self, resource_id: str, conflicting_operation: str):
        super().__init__(
            error_code="CONCURRENT_OPERATION_CONFLICT",
            message=f"資源 '{resource_id}' 存在並發操作衝突: {conflicting_operation}",
            status_code=409,
            details=conflicting_operation
        )


# 系統錯誤
class DiskSpaceInsufficientError(SystemError):
    def __init__(self, required_space_mb: int, available_space_mb: int):
        super().__init__(
            error_code="DISK_SPACE_INSUFFICIENT",
            message=f"磁碟空間不足，需要 {required_space_mb}MB，可用 {available_space_mb}MB",
            status_code=507
        )
        self.required_space_mb = required_space_mb
        self.available_space_mb = available_space_mb


class MemoryInsufficientError(SystemError):
    def __init__(self, required_memory_mb: int, available_memory_mb: int):
        super().__init__(
            error_code="MEMORY_INSUFFICIENT",
            message=f"記憶體不足，需要 {required_memory_mb}MB，可用 {available_memory_mb}MB",
            status_code=507
        )
        self.required_memory_mb = required_memory_mb
        self.available_memory_mb = available_memory_mb
