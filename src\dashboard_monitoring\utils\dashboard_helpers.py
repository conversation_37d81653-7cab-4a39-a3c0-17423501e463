"""
儀表板輔助函數模組
包含錯誤處理裝飾器、效能監控工具和通用輔助函數

遵循最小侵入原則，確保監控系統故障不影響主要業務功能
"""

import asyncio
import functools
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Optional, Union, TypeVar, Awaitable
from pathlib import Path
import logging

from src.infrastructure.logging.logger_manager import LoggerManager, LogLevel, PerformanceLogger

# 類型變數定義
F = TypeVar('F', bound=Callable[..., Any])
AF = TypeVar('AF', bound=Callable[..., Awaitable[Any]])

# 全域日誌管理器實例
_logger_manager: Optional[LoggerManager] = None
_performance_logger: Optional[PerformanceLogger] = None


def get_dashboard_logger_manager() -> LoggerManager:
    """獲取儀表板專用日誌管理器"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager(
            log_dir=Path("logs/dashboard_monitoring"),
            default_level=LogLevel.INFO,
            enable_colors=True,
            include_caller_info=True,
            async_logging=True
        )
    return _logger_manager


def get_dashboard_performance_logger() -> PerformanceLogger:
    """獲取儀表板效能日誌器"""
    global _performance_logger
    if _performance_logger is None:
        logger_manager = get_dashboard_logger_manager()
        _performance_logger = logger_manager.get_performance_logger()
    return _performance_logger


# ==================== 錯誤處理裝飾器 ====================

def dashboard_error_handler(
    fallback_value: Any = None,
    log_level: LogLevel = LogLevel.ERROR,
    suppress_exceptions: bool = True,
    operation_name: Optional[str] = None
):
    """
    儀表板錯誤處理裝飾器
    
    Args:
        fallback_value: 發生錯誤時返回的預設值
        log_level: 日誌級別
        suppress_exceptions: 是否抑制異常（True=返回fallback_value，False=重新拋出）
        operation_name: 操作名稱，用於日誌記錄
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger_manager = get_dashboard_logger_manager()
            logger = logger_manager.get_logger("dashboard_error_handler")
            
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = {
                    "operation": op_name,
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys()),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": traceback.format_exc()
                }
                
                if log_level == LogLevel.ERROR:
                    logger.error(f"儀表板操作失敗: {op_name}", **error_context)
                elif log_level == LogLevel.WARNING:
                    logger.warning(f"儀表板操作警告: {op_name}", **error_context)
                else:
                    logger.info(f"儀表板操作異常: {op_name}", **error_context)
                
                if suppress_exceptions:
                    return fallback_value
                else:
                    raise
                    
        return wrapper
    return decorator


def dashboard_async_error_handler(
    fallback_value: Any = None,
    log_level: LogLevel = LogLevel.ERROR,
    suppress_exceptions: bool = True,
    operation_name: Optional[str] = None
):
    """
    儀表板非同步錯誤處理裝飾器
    """
    def decorator(func: AF) -> AF:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger_manager = get_dashboard_logger_manager()
            logger = logger_manager.get_logger("dashboard_async_error_handler")
            
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_context = {
                    "operation": op_name,
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys()),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": traceback.format_exc()
                }
                
                if log_level == LogLevel.ERROR:
                    logger.error(f"儀表板非同步操作失敗: {op_name}", **error_context)
                elif log_level == LogLevel.WARNING:
                    logger.warning(f"儀表板非同步操作警告: {op_name}", **error_context)
                else:
                    logger.info(f"儀表板非同步操作異常: {op_name}", **error_context)
                
                if suppress_exceptions:
                    return fallback_value
                else:
                    raise
                    
        return wrapper
    return decorator


# ==================== 效能監控裝飾器 ====================

def dashboard_performance_monitor(
    operation_name: Optional[str] = None,
    log_slow_operations: bool = True,
    slow_threshold_seconds: float = 2.0,
    include_args: bool = False
):
    """
    儀表板效能監控裝飾器
    
    Args:
        operation_name: 操作名稱
        log_slow_operations: 是否記錄慢操作
        slow_threshold_seconds: 慢操作閾值（秒）
        include_args: 是否在日誌中包含參數資訊
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            performance_logger = get_dashboard_performance_logger()
            logger_manager = get_dashboard_logger_manager()
            logger = logger_manager.get_logger("dashboard_performance")
            
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 記錄效能指標
                metrics = {
                    "operation": op_name,
                    "duration_seconds": duration,
                    "status": "success",
                    "timestamp": datetime.now().isoformat()
                }
                
                if include_args:
                    metrics.update({
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys())
                    })
                
                performance_logger.log_metrics(metrics)
                
                # 記錄慢操作
                if log_slow_operations and duration > slow_threshold_seconds:
                    logger.warning(
                        f"慢操作檢測: {op_name} 耗時 {duration:.2f}秒",
                        operation=op_name,
                        duration=duration,
                        threshold=slow_threshold_seconds
                    )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # 記錄失敗的效能指標
                metrics = {
                    "operation": op_name,
                    "duration_seconds": duration,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                
                performance_logger.log_metrics(metrics)
                raise
                
        return wrapper
    return decorator


def dashboard_async_performance_monitor(
    operation_name: Optional[str] = None,
    log_slow_operations: bool = True,
    slow_threshold_seconds: float = 2.0,
    include_args: bool = False
):
    """
    儀表板非同步效能監控裝飾器
    """
    def decorator(func: AF) -> AF:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            performance_logger = get_dashboard_performance_logger()
            logger_manager = get_dashboard_logger_manager()
            logger = logger_manager.get_logger("dashboard_async_performance")
            
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 記錄效能指標
                metrics = {
                    "operation": op_name,
                    "duration_seconds": duration,
                    "status": "success",
                    "timestamp": datetime.now().isoformat()
                }
                
                if include_args:
                    metrics.update({
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys())
                    })
                
                performance_logger.log_metrics(metrics)
                
                # 記錄慢操作
                if log_slow_operations and duration > slow_threshold_seconds:
                    logger.warning(
                        f"慢非同步操作檢測: {op_name} 耗時 {duration:.2f}秒",
                        operation=op_name,
                        duration=duration,
                        threshold=slow_threshold_seconds
                    )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # 記錄失敗的效能指標
                metrics = {
                    "operation": op_name,
                    "duration_seconds": duration,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                
                performance_logger.log_metrics(metrics)
                raise
                
        return wrapper
    return decorator


# ==================== 組合裝飾器 ====================

def dashboard_monitor(
    fallback_value: Any = None,
    operation_name: Optional[str] = None,
    suppress_exceptions: bool = True,
    log_performance: bool = True,
    slow_threshold_seconds: float = 2.0
):
    """
    儀表板監控組合裝飾器（錯誤處理 + 效能監控）
    """
    def decorator(func: F) -> F:
        # 先應用效能監控，再應用錯誤處理
        if log_performance:
            func = dashboard_performance_monitor(
                operation_name=operation_name,
                slow_threshold_seconds=slow_threshold_seconds
            )(func)
        
        func = dashboard_error_handler(
            fallback_value=fallback_value,
            operation_name=operation_name,
            suppress_exceptions=suppress_exceptions
        )(func)
        
        return func
    return decorator


def dashboard_async_monitor(
    fallback_value: Any = None,
    operation_name: Optional[str] = None,
    suppress_exceptions: bool = True,
    log_performance: bool = True,
    slow_threshold_seconds: float = 2.0
):
    """
    儀表板非同步監控組合裝飾器（錯誤處理 + 效能監控）
    """
    def decorator(func: AF) -> AF:
        # 先應用效能監控，再應用錯誤處理
        if log_performance:
            func = dashboard_async_performance_monitor(
                operation_name=operation_name,
                slow_threshold_seconds=slow_threshold_seconds
            )(func)
        
        func = dashboard_async_error_handler(
            fallback_value=fallback_value,
            operation_name=operation_name,
            suppress_exceptions=suppress_exceptions
        )(func)
        
        return func
    return decorator


# ==================== 通用輔助函數 ====================

@dashboard_error_handler(fallback_value="", operation_name="format_timestamp")
def format_timestamp(
    timestamp: Union[datetime, float, int, str, None],
    format_string: str = "%Y-%m-%d %H:%M:%S",
    timezone_aware: bool = False
) -> str:
    """
    格式化時間戳
    
    Args:
        timestamp: 時間戳（datetime物件、Unix時間戳或ISO字串）
        format_string: 格式化字串
        timezone_aware: 是否考慮時區
        
    Returns:
        格式化後的時間字串
    """
    if timestamp is None:
        return ""
    
    try:
        if isinstance(timestamp, datetime):
            dt = timestamp
        elif isinstance(timestamp, (int, float)):
            dt = datetime.fromtimestamp(timestamp)
        elif isinstance(timestamp, str):
            # 嘗試解析ISO格式
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        else:
            return str(timestamp)
        
        return dt.strftime(format_string)
        
    except (ValueError, TypeError, OSError) as e:
        # 記錄錯誤但返回字串表示
        return str(timestamp)


def calculate_percentage(
    value: Union[int, float],
    total: Union[int, float],
    decimal_places: int = 2,
    handle_zero_division: bool = True
) -> float:
    """
    計算百分比
    
    Args:
        value: 數值
        total: 總數
        decimal_places: 小數位數
        handle_zero_division: 是否處理除零錯誤
        
    Returns:
        百分比值
    """
    if total == 0:
        if handle_zero_division:
            return 0.0
        else:
            raise ZeroDivisionError("總數不能為零")
    
    percentage = (value / total) * 100
    return round(percentage, decimal_places)


@dashboard_error_handler(fallback_value="0 B", operation_name="format_bytes")
def format_bytes(
    bytes_value: Union[int, float],
    decimal_places: int = 2,
    binary: bool = True
) -> str:
    """
    格式化位元組大小
    
    Args:
        bytes_value: 位元組數值
        decimal_places: 小數位數
        binary: 是否使用二進制單位（1024）而非十進制（1000）
        
    Returns:
        格式化後的大小字串
    """
    if bytes_value == 0:
        return "0 B"
    
    if bytes_value < 0:
        return f"-{format_bytes(-bytes_value, decimal_places, binary)}"
    
    # 單位定義
    if binary:
        units = ["B", "KiB", "MiB", "GiB", "TiB", "PiB"]
        divisor = 1024
    else:
        units = ["B", "KB", "MB", "GB", "TB", "PB"]
        divisor = 1000
    
    # 計算適當的單位
    unit_index = 0
    size = float(bytes_value)
    
    while size >= divisor and unit_index < len(units) - 1:
        size /= divisor
        unit_index += 1
    
    # 格式化結果
    if unit_index == 0:  # 位元組不需要小數
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.{decimal_places}f} {units[unit_index]}"


@dashboard_error_handler(fallback_value="0秒", operation_name="format_duration")
def format_duration(
    seconds: Union[int, float, timedelta],
    precision: str = "auto",
    chinese_units: bool = True
) -> str:
    """
    格式化持續時間
    
    Args:
        seconds: 秒數或timedelta物件
        precision: 精度 ("auto", "seconds", "minutes", "hours", "days")
        chinese_units: 是否使用中文單位
        
    Returns:
        格式化後的持續時間字串
    """
    if isinstance(seconds, timedelta):
        total_seconds = seconds.total_seconds()
    else:
        total_seconds = float(seconds)
    
    if total_seconds < 0:
        return f"-{format_duration(-total_seconds, precision, chinese_units)}"
    
    # 單位定義
    if chinese_units:
        units = {
            "day": "天",
            "hour": "小時", 
            "minute": "分鐘",
            "second": "秒"
        }
    else:
        units = {
            "day": "d",
            "hour": "h",
            "minute": "m", 
            "second": "s"
        }
    
    # 計算各個時間單位
    days = int(total_seconds // 86400)
    hours = int((total_seconds % 86400) // 3600)
    minutes = int((total_seconds % 3600) // 60)
    secs = total_seconds % 60
    
    # 根據精度決定顯示格式
    if precision == "auto":
        if days > 0:
            precision = "days"
        elif hours > 0:
            precision = "hours"
        elif minutes > 0:
            precision = "minutes"
        else:
            precision = "seconds"
    
    # 格式化輸出
    parts = []
    
    # 根據精度級別顯示相應的時間單位
    if precision == "days":
        if days > 0:
            parts.append(f"{days}{units['day']}")
        if hours > 0:
            parts.append(f"{hours}{units['hour']}")
        if minutes > 0:
            parts.append(f"{minutes}{units['minute']}")
        if int(secs) > 0:
            parts.append(f"{int(secs)}{units['second']}")
    elif precision == "hours":
        if hours > 0 or days > 0:
            total_hours = days * 24 + hours
            parts.append(f"{total_hours}{units['hour']}")
        if minutes > 0:
            parts.append(f"{minutes}{units['minute']}")
        if int(secs) > 0:
            parts.append(f"{int(secs)}{units['second']}")
    elif precision == "minutes":
        if days > 0:
            parts.append(f"{days}{units['day']}")
        if hours > 0:
            parts.append(f"{hours}{units['hour']}")
        if minutes > 0 or hours > 0 or days > 0:
            parts.append(f"{minutes}{units['minute']}")
        if int(secs) > 0:
            parts.append(f"{int(secs)}{units['second']}")
    else:  # precision == "seconds"
        if days > 0:
            parts.append(f"{days}{units['day']}")
        if hours > 0:
            parts.append(f"{hours}{units['hour']}")
        if minutes > 0:
            parts.append(f"{minutes}{units['minute']}")
        
        if total_seconds < 1:
            parts.append(f"{secs:.2f}{units['second']}")
        elif total_seconds < 10:
            parts.append(f"{secs:.1f}{units['second']}")
        else:
            parts.append(f"{int(secs)}{units['second']}")
    
    return " ".join(parts) if parts else f"0{units['second']}"


# ==================== 統一日誌格式輔助函數 ====================

def create_dashboard_log_context(
    operation: str,
    component: str,
    **additional_context
) -> Dict[str, Any]:
    """
    創建儀表板日誌上下文
    
    Args:
        operation: 操作名稱
        component: 元件名稱
        **additional_context: 額外的上下文資訊
        
    Returns:
        日誌上下文字典
    """
    context = {
        "dashboard_operation": operation,
        "dashboard_component": component,
        "timestamp": datetime.now().isoformat(),
        **additional_context
    }
    return context


def log_dashboard_operation(
    operation: str,
    component: str,
    level: LogLevel = LogLevel.INFO,
    message: str = "",
    **context
) -> None:
    """
    記錄儀表板操作日誌
    
    Args:
        operation: 操作名稱
        component: 元件名稱
        level: 日誌級別
        message: 日誌訊息
        **context: 額外的上下文資訊
    """
    logger_manager = get_dashboard_logger_manager()
    logger = logger_manager.get_logger("dashboard_operations")
    
    log_context = create_dashboard_log_context(operation, component, **context)
    
    if not message:
        message = f"儀表板操作: {component}.{operation}"
    
    if level == LogLevel.DEBUG:
        logger.debug(message, **log_context)
    elif level == LogLevel.INFO:
        logger.info(message, **log_context)
    elif level == LogLevel.WARNING:
        logger.warning(message, **log_context)
    elif level == LogLevel.ERROR:
        logger.error(message, **log_context)
    elif level == LogLevel.CRITICAL:
        logger.critical(message, **log_context)


# ==================== 效能監控輔助類別 ====================

class DashboardPerformanceTracker:
    """儀表板效能追蹤器"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
        self.metrics = {}
        self.performance_logger = get_dashboard_performance_logger()
    
    def start(self) -> 'DashboardPerformanceTracker':
        """開始追蹤"""
        self.start_time = time.time()
        return self
    
    def stop(self) -> float:
        """停止追蹤並返回持續時間"""
        if self.start_time is None:
            raise ValueError("必須先調用 start() 方法")
        
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        
        # 記錄效能指標
        self.metrics.update({
            "operation": self.operation_name,
            "duration_seconds": duration,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "timestamp": datetime.now().isoformat()
        })
        
        self.performance_logger.log_metrics(self.metrics)
        return duration
    
    def add_metric(self, key: str, value: Any) -> 'DashboardPerformanceTracker':
        """添加自定義指標"""
        self.metrics[key] = value
        return self
    
    def __enter__(self) -> 'DashboardPerformanceTracker':
        """上下文管理器進入"""
        return self.start()
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器退出"""
        self.stop()
        
        # 如果有異常，記錄異常資訊
        if exc_type is not None:
            self.metrics.update({
                "exception_type": exc_type.__name__,
                "exception_message": str(exc_val),
                "status": "failed"
            })
        else:
            self.metrics["status"] = "success"


# ==================== 批量操作輔助函數 ====================

@dashboard_async_monitor(
    fallback_value=[],
    operation_name="safe_batch_operation",
    suppress_exceptions=True
)
async def safe_batch_operation(
    operations: list,
    batch_size: int = 10,
    max_concurrent: int = 5,
    timeout_seconds: float = 30.0
) -> list:
    """
    安全的批量操作執行器
    
    Args:
        operations: 操作列表（可調用物件）
        batch_size: 批次大小
        max_concurrent: 最大並發數
        timeout_seconds: 超時時間
        
    Returns:
        操作結果列表
    """
    results = []
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def execute_operation(operation):
        async with semaphore:
            try:
                if asyncio.iscoroutinefunction(operation):
                    return await asyncio.wait_for(operation(), timeout=timeout_seconds)
                else:
                    return operation()
            except Exception as e:
                log_dashboard_operation(
                    "batch_operation_error",
                    "safe_batch_operation",
                    LogLevel.WARNING,
                    f"批量操作中的單個操作失敗: {e}",
                    error=str(e)
                )
                return None
    
    # 分批處理
    for i in range(0, len(operations), batch_size):
        batch = operations[i:i + batch_size]
        batch_results = await asyncio.gather(
            *[execute_operation(op) for op in batch],
            return_exceptions=True
        )
        results.extend(batch_results)
    
    return results


# ==================== 模組初始化 ====================

def initialize_dashboard_helpers():
    """初始化儀表板輔助函數模組"""
    logger_manager = get_dashboard_logger_manager()
    logger = logger_manager.get_logger("dashboard_helpers_init")
    
    logger.info(
        "儀表板輔助函數模組已初始化",
        module="dashboard_helpers",
        features=[
            "error_handling_decorators",
            "performance_monitoring",
            "utility_functions",
            "unified_logging"
        ]
    )


# 模組載入時自動初始化
initialize_dashboard_helpers()