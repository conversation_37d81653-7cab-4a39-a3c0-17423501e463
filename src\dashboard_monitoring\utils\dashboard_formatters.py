"""
儀表板資料格式化工具模組
包含各種資料格式化函數，用於統一監控儀表板的資料展示

遵循最小侵入原則，確保格式化錯誤不影響資料展示
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict, is_dataclass

from .dashboard_helpers import (
    dashboard_error_handler,
    format_timestamp,
    format_bytes,
    format_duration,
    calculate_percentage,
    log_dashboard_operation,
    LogLevel
)


# ==================== 指標資料格式化 ====================

@dashboard_error_handler(fallback_value={}, operation_name="format_metrics_data")
def format_metrics_data(
    metrics_data: Union[Dict[str, Any], Any],
    include_metadata: bool = True,
    format_timestamps: bool = True,
    format_sizes: bool = True
) -> Dict[str, Any]:
    """
    格式化指標資料
    
    Args:
        metrics_data: 原始指標資料
        include_metadata: 是否包含元資料
        format_timestamps: 是否格式化時間戳
        format_sizes: 是否格式化大小資料
        
    Returns:
        格式化後的指標資料
    """
    # 如果是 dataclass，轉換為字典
    if is_dataclass(metrics_data):
        data = asdict(metrics_data)
    elif isinstance(metrics_data, dict):
        data = metrics_data.copy()
    else:
        # 嘗試轉換為字典
        try:
            data = dict(metrics_data)
        except (TypeError, ValueError):
            data = {"raw_data": str(metrics_data)}
    
    formatted_data = {}
    
    for key, value in data.items():
        formatted_key = _format_metric_key(key)
        formatted_value = _format_metric_value(
            value, 
            format_timestamps=format_timestamps,
            format_sizes=format_sizes
        )
        formatted_data[formatted_key] = formatted_value
    
    # 添加元資料
    if include_metadata:
        formatted_data["_metadata"] = {
            "formatted_at": datetime.now().isoformat(),
            "original_keys_count": len(data),
            "formatted_keys_count": len(formatted_data) - 1,  # 排除 _metadata
            "data_type": type(metrics_data).__name__
        }
    
    return formatted_data


def _format_metric_key(key: str) -> str:
    """格式化指標鍵名"""
    # 將下劃線轉換為駝峰命名
    if "_" in key:
        parts = key.split("_")
        return parts[0] + "".join(word.capitalize() for word in parts[1:])
    return key


def _format_metric_value(
    value: Any,
    format_timestamps: bool = True,
    format_sizes: bool = True
) -> Any:
    """格式化指標值"""
    if value is None:
        return None
    
    # 處理時間戳
    if format_timestamps and isinstance(value, (datetime, str)):
        if isinstance(value, str) and ("T" in value or ":" in value):
            return format_timestamp(value)
        elif isinstance(value, datetime):
            return format_timestamp(value)
    
    # 處理大小資料（以 bytes, size, memory 等結尾的鍵）
    if format_sizes and isinstance(value, (int, float)):
        # 這裡可以根據鍵名判斷是否需要格式化為大小
        # 暫時返回原值，具體邏輯可以根據需求調整
        pass
    
    # 處理嵌套字典
    if isinstance(value, dict):
        return {k: _format_metric_value(v, format_timestamps, format_sizes) for k, v in value.items()}
    
    # 處理列表
    if isinstance(value, list):
        return [_format_metric_value(item, format_timestamps, format_sizes) for item in value]
    
    return value


# ==================== 告警資料格式化 ====================

@dashboard_error_handler(fallback_value=[], operation_name="format_alert_data")
def format_alert_data(
    alerts: Union[List[Dict[str, Any]], List[Any], Dict[str, Any], Any],
    include_summary: bool = True,
    group_by_level: bool = False,
    sort_by_time: bool = True
) -> Dict[str, Any]:
    """
    格式化告警資料
    
    Args:
        alerts: 告警資料列表或單個告警
        include_summary: 是否包含摘要資訊
        group_by_level: 是否按級別分組
        sort_by_time: 是否按時間排序
        
    Returns:
        格式化後的告警資料
    """
    # 統一處理為列表
    if not isinstance(alerts, list):
        if isinstance(alerts, dict):
            alert_list = [alerts]
        elif is_dataclass(alerts):
            alert_list = [asdict(alerts)]
        else:
            alert_list = []
    else:
        alert_list = []
        for alert in alerts:
            if is_dataclass(alert):
                alert_list.append(asdict(alert))
            elif isinstance(alert, dict):
                alert_list.append(alert)
            else:
                alert_list.append({"raw_alert": str(alert)})
    
    # 格式化每個告警
    formatted_alerts = []
    for alert in alert_list:
        formatted_alert = _format_single_alert(alert)
        formatted_alerts.append(formatted_alert)
    
    # 按時間排序
    if sort_by_time:
        formatted_alerts.sort(
            key=lambda x: x.get("triggeredAt", ""),
            reverse=True  # 最新的在前
        )
    
    result = {
        "alerts": formatted_alerts,
        "count": len(formatted_alerts)
    }
    
    # 按級別分組
    if group_by_level:
        grouped = {}
        for alert in formatted_alerts:
            level = alert.get("level", "unknown")
            if level not in grouped:
                grouped[level] = []
            grouped[level].append(alert)
        result["groupedByLevel"] = grouped
    
    # 添加摘要資訊
    if include_summary:
        result["summary"] = _generate_alert_summary(formatted_alerts)
    
    return result


def _format_single_alert(alert: Dict[str, Any]) -> Dict[str, Any]:
    """格式化單個告警"""
    formatted = {}
    
    # 標準化欄位名稱
    field_mapping = {
        "id": "id",
        "alert_id": "id",
        "alert_type": "type",
        "type": "type",
        "alert_level": "level",
        "level": "level",
        "title": "title",
        "message": "message",
        "source": "source",
        "source_system": "source",
        "triggered_at": "triggeredAt",
        "acknowledged_at": "acknowledgedAt",
        "resolved_at": "resolvedAt",
        "status": "status",
        "metadata": "metadata"
    }
    
    for original_key, standard_key in field_mapping.items():
        if original_key in alert:
            value = alert[original_key]
            
            # 格式化時間欄位
            if "At" in standard_key and value:
                formatted[standard_key] = format_timestamp(value)
            else:
                formatted[standard_key] = value
    
    # 添加額外的格式化資訊
    formatted["severity"] = _get_alert_severity_score(formatted.get("level", "info"))
    formatted["isActive"] = formatted.get("status", "active") == "active"
    formatted["ageMinutes"] = _calculate_alert_age_minutes(formatted.get("triggeredAt"))
    
    return formatted


def _generate_alert_summary(alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
    """生成告警摘要"""
    if not alerts:
        return {
            "total": 0,
            "byLevel": {},
            "activeCount": 0,
            "oldestAlert": None,
            "newestAlert": None
        }
    
    # 按級別統計
    level_counts = {}
    active_count = 0
    
    for alert in alerts:
        level = alert.get("level", "unknown")
        level_counts[level] = level_counts.get(level, 0) + 1
        
        if alert.get("isActive", False):
            active_count += 1
    
    # 找出最舊和最新的告警
    sorted_alerts = sorted(alerts, key=lambda x: x.get("triggeredAt", ""))
    oldest_alert = sorted_alerts[0] if sorted_alerts else None
    newest_alert = sorted_alerts[-1] if sorted_alerts else None
    
    return {
        "total": len(alerts),
        "byLevel": level_counts,
        "activeCount": active_count,
        "resolvedCount": len(alerts) - active_count,
        "oldestAlert": oldest_alert.get("triggeredAt") if oldest_alert else None,
        "newestAlert": newest_alert.get("triggeredAt") if newest_alert else None,
        "criticalCount": level_counts.get("critical", 0),
        "warningCount": level_counts.get("warning", 0)
    }


def _get_alert_severity_score(level: str) -> int:
    """獲取告警嚴重程度分數"""
    severity_map = {
        "info": 1,
        "warning": 2,
        "error": 3,
        "critical": 4
    }
    return severity_map.get(level.lower(), 0)


def _calculate_alert_age_minutes(triggered_at: Optional[str]) -> Optional[int]:
    """計算告警存在時間（分鐘）"""
    if not triggered_at:
        return None
    
    try:
        if isinstance(triggered_at, str):
            # 嘗試解析時間字串
            trigger_time = datetime.fromisoformat(triggered_at.replace('Z', '+00:00'))
        else:
            trigger_time = triggered_at
        
        age = datetime.now() - trigger_time.replace(tzinfo=None)
        return int(age.total_seconds() / 60)
    except (ValueError, TypeError, AttributeError):
        return None


# ==================== 趨勢資料格式化 ====================

@dashboard_error_handler(fallback_value={}, operation_name="format_trend_data")
def format_trend_data(
    trend_data: Union[List[Dict[str, Any]], Dict[str, Any], Any],
    time_range: str = "1h",
    aggregate_method: str = "avg",
    include_statistics: bool = True,
    max_data_points: int = 100
) -> Dict[str, Any]:
    """
    格式化趨勢資料
    
    Args:
        trend_data: 原始趨勢資料
        time_range: 時間範圍
        aggregate_method: 聚合方法 ("avg", "sum", "max", "min")
        include_statistics: 是否包含統計資訊
        max_data_points: 最大資料點數
        
    Returns:
        格式化後的趨勢資料
    """
    # 統一處理為列表
    if isinstance(trend_data, dict):
        if "data" in trend_data:
            data_points = trend_data["data"]
        else:
            data_points = [trend_data]
    elif isinstance(trend_data, list):
        data_points = trend_data
    else:
        data_points = []
    
    # 格式化資料點
    formatted_points = []
    for point in data_points:
        if isinstance(point, dict):
            formatted_point = _format_trend_point(point)
            formatted_points.append(formatted_point)
    
    # 按時間排序
    formatted_points.sort(key=lambda x: x.get("timestamp", ""))
    
    # 限制資料點數量
    if len(formatted_points) > max_data_points:
        # 使用採樣來減少資料點
        step = len(formatted_points) // max_data_points
        if step < 1:
            step = 1
        formatted_points = formatted_points[::step][:max_data_points]
    
    result = {
        "data": formatted_points,
        "timeRange": time_range,
        "aggregateMethod": aggregate_method,
        "dataPoints": len(formatted_points),
        "startTime": formatted_points[0].get("timestamp") if formatted_points else None,
        "endTime": formatted_points[-1].get("timestamp") if formatted_points else None
    }
    
    # 添加統計資訊
    if include_statistics and formatted_points:
        result["statistics"] = _calculate_trend_statistics(formatted_points)
    
    return result


def _format_trend_point(point: Dict[str, Any]) -> Dict[str, Any]:
    """格式化單個趨勢資料點"""
    formatted = {}
    
    # 標準化欄位
    if "timestamp" in point:
        formatted["timestamp"] = format_timestamp(point["timestamp"])
    elif "time" in point:
        formatted["timestamp"] = format_timestamp(point["time"])
    elif "created_at" in point:
        formatted["timestamp"] = format_timestamp(point["created_at"])
    
    # 處理數值欄位
    numeric_fields = ["value", "metric_value", "count", "duration", "size", "percentage"]
    for field in numeric_fields:
        if field in point:
            value = point[field]
            if isinstance(value, (int, float)):
                formatted[field] = round(float(value), 2)
            else:
                formatted[field] = value
    
    # 複製其他欄位
    for key, value in point.items():
        if key not in formatted:
            formatted[key] = value
    
    return formatted


def _calculate_trend_statistics(data_points: List[Dict[str, Any]]) -> Dict[str, Any]:
    """計算趨勢統計資訊"""
    if not data_points:
        return {}
    
    # 提取數值
    values = []
    for point in data_points:
        value = point.get("value") or point.get("metric_value") or point.get("count")
        if isinstance(value, (int, float)):
            values.append(float(value))
    
    if not values:
        return {"message": "無可用的數值資料"}
    
    # 計算統計指標
    stats = {
        "count": len(values),
        "min": round(min(values), 2),
        "max": round(max(values), 2),
        "avg": round(sum(values) / len(values), 2),
        "sum": round(sum(values), 2)
    }
    
    # 計算變化趨勢
    if len(values) >= 2:
        first_value = values[0]
        last_value = values[-1]
        change = last_value - first_value
        change_percent = (change / first_value * 100) if first_value != 0 else 0
        
        stats.update({
            "firstValue": round(first_value, 2),
            "lastValue": round(last_value, 2),
            "change": round(change, 2),
            "changePercent": round(change_percent, 2),
            "trend": "up" if change > 0 else "down" if change < 0 else "stable"
        })
    
    return stats


# ==================== 通用格式化輔助函數 ====================

@dashboard_error_handler(fallback_value={}, operation_name="format_dashboard_response")
def format_dashboard_response(
    data: Any,
    status: str = "success",
    message: str = "",
    include_timestamp: bool = True,
    include_metadata: bool = False
) -> Dict[str, Any]:
    """
    格式化儀表板 API 回應
    
    Args:
        data: 回應資料
        status: 狀態 ("success", "error", "warning")
        message: 訊息
        include_timestamp: 是否包含時間戳
        include_metadata: 是否包含元資料
        
    Returns:
        格式化後的回應
    """
    response = {
        "status": status,
        "data": data
    }
    
    if message:
        response["message"] = message
    
    if include_timestamp:
        response["timestamp"] = datetime.now().isoformat()
    
    if include_metadata:
        response["metadata"] = {
            "dataType": type(data).__name__,
            "dataSize": len(str(data)),
            "generatedAt": datetime.now().isoformat()
        }
    
    return response


@dashboard_error_handler(fallback_value="", operation_name="format_json_safe")
def format_json_safe(
    data: Any,
    indent: Optional[int] = None,
    ensure_ascii: bool = False
) -> str:
    """
    安全的 JSON 格式化
    
    Args:
        data: 要格式化的資料
        indent: 縮排空格數
        ensure_ascii: 是否確保 ASCII 編碼
        
    Returns:
        JSON 字串
    """
    try:
        # 處理不可序列化的物件
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, timedelta):
                return obj.total_seconds()
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            else:
                return str(obj)
        
        return json.dumps(
            data,
            indent=indent,
            ensure_ascii=ensure_ascii,
            default=json_serializer
        )
    except (TypeError, ValueError) as e:
        log_dashboard_operation(
            "json_format_error",
            "format_json_safe",
            LogLevel.WARNING,
            f"JSON 格式化失敗: {e}",
            error=str(e),
            data_type=type(data).__name__
        )
        return json.dumps({"error": "無法格式化資料", "raw": str(data)})


# ==================== 資料驗證輔助函數 ====================

@dashboard_error_handler(fallback_value=False, operation_name="validate_metrics_data")
def validate_metrics_data(data: Any) -> bool:
    """
    驗證指標資料格式
    
    Args:
        data: 要驗證的資料
        
    Returns:
        是否有效
    """
    if data is None:
        return False
    
    # 基本類型檢查
    if not isinstance(data, (dict, list)):
        if not is_dataclass(data):
            return False
    
    # 如果是字典，檢查是否有必要的欄位
    if isinstance(data, dict):
        # 至少應該有一些數值欄位
        numeric_fields = 0
        for value in data.values():
            if isinstance(value, (int, float)):
                numeric_fields += 1
        
        return numeric_fields > 0
    
    return True


@dashboard_error_handler(fallback_value=False, operation_name="validate_alert_data")
def validate_alert_data(data: Any) -> bool:
    """
    驗證告警資料格式
    
    Args:
        data: 要驗證的資料
        
    Returns:
        是否有效
    """
    if data is None:
        return False
    
    # 處理單個告警
    if isinstance(data, dict):
        required_fields = ["level", "message"]
        return all(field in data for field in required_fields)
    
    # 處理告警列表
    if isinstance(data, list):
        if not data:  # 空列表也是有效的
            return True
        return all(validate_alert_data(alert) for alert in data)
    
    # 處理 dataclass
    if is_dataclass(data):
        return hasattr(data, 'level') and hasattr(data, 'message')
    
    return False


# ==================== 模組初始化 ====================

def initialize_dashboard_formatters():
    """初始化儀表板格式化工具模組"""
    log_dashboard_operation(
        "module_init",
        "dashboard_formatters",
        LogLevel.INFO,
        "儀表板格式化工具模組已初始化",
        features=[
            "metrics_data_formatting",
            "alert_data_formatting",
            "trend_data_formatting",
            "json_safe_formatting",
            "data_validation"
        ]
    )


# 模組載入時自動初始化
initialize_dashboard_formatters()