"""
Celery 監控收集器 - 收集 Celery 任務佇列和工作者相關指標

此模組負責收集 Celery 任務系統的監控資料，包括：
- 任務佇列指標收集
- 工作者狀態監控  
- 任務類型分組統計
- 任務效能指標收集

需求覆蓋: 2
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from ..models.dashboard_metrics_models import CeleryMetrics
from ..config.dashboard_config import DashboardConfig


class DashboardCeleryCollector:
    """Celery 監控收集器"""
    
    def __init__(self, config: Optional[DashboardConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or DashboardConfig()
        self._celery_app = None
        self._inspect = None
        
        # 任務類型映射
        self.task_type_mapping = {
            'tasks.search_product': 'search_product',
            'tasks.run_csv_summary': 'csv_summary', 
            'tasks.run_code_comparison': 'code_comparison',
            'tasks.health_check': 'health_check'
        }
        
    async def collect_metrics(self) -> CeleryMetrics:
        """收集 Celery 監控指標"""
        try:
            self.logger.debug("開始收集 Celery 監控指標")
            
            # 獲取 Celery 應用程式和檢查器
            if not await self._initialize_celery_connection():
                self.logger.warning("無法連接到 Celery，返回預設指標")
                return self._get_default_metrics()
            
            # 並行收集各種指標
            queue_metrics = await self._collect_queue_metrics()
            worker_metrics = await self._collect_worker_metrics()
            task_type_metrics = await self._collect_task_type_metrics()
            performance_metrics = await self._collect_performance_metrics()
            long_running_tasks = await self._collect_long_running_tasks()
            
            # 組合所有指標
            metrics = CeleryMetrics(
                **queue_metrics,
                **worker_metrics,
                **task_type_metrics,
                **performance_metrics,
                long_running_tasks=long_running_tasks,
                timestamp=datetime.now()
            )
            
            self.logger.debug(f"Celery 指標收集完成: {metrics.total_active} 活躍, {metrics.total_pending} 待處理")
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集 Celery 指標失敗: {e}")
            return self._get_default_metrics()
    
    async def _initialize_celery_connection(self) -> bool:
        """初始化 Celery 連接"""
        try:
            if self._celery_app is None:
                self._celery_app = await self._get_celery_app()
                
            if self._celery_app is None:
                return False
                
            # 創建檢查器實例
            self._inspect = self._celery_app.control.inspect()
            
            # 測試連接
            if await self._test_celery_connection():
                return True
            else:
                self.logger.warning("Celery 連接測試失敗")
                return False
                
        except Exception as e:
            self.logger.error(f"初始化 Celery 連接失敗: {e}")
            return False
    
    async def _get_celery_app(self):
        """安全獲取 Celery 應用程式實例"""
        try:
            # 嘗試從 tasks 模組導入
            from src.tasks import celery_app
            return celery_app
        except ImportError as e:
            self.logger.error(f"無法導入 Celery 應用程式: {e}")
            return None
        except Exception as e:
            self.logger.error(f"獲取 Celery 應用程式時發生錯誤: {e}")
            return None
    
    async def _test_celery_connection(self) -> bool:
        """測試 Celery 連接是否正常"""
        try:
            if not self._inspect:
                return False
                
            # 嘗試獲取工作者統計資訊（超時 2 秒）
            stats = self._inspect.stats()
            
            # 檢查是否有回應
            if stats is None:
                return False
                
            return True
            
        except Exception as e:
            self.logger.debug(f"Celery 連接測試失敗: {e}")
            return False
    
    async def _collect_queue_metrics(self) -> Dict[str, int]:
        """收集任務佇列指標"""
        try:
            if not self._inspect:
                return {'total_active': 0, 'total_pending': 0, 'total_completed': 0, 'total_failed': 0}
            
            # 獲取活躍任務
            active_tasks = self._inspect.active() or {}
            total_active = sum(len(tasks) for tasks in active_tasks.values())
            
            # 獲取待處理任務（reserved）
            reserved_tasks = self._inspect.reserved() or {}
            total_pending = sum(len(tasks) for tasks in reserved_tasks.values())
            
            # 獲取已完成和失敗任務數量（從歷史記錄或統計中）
            completed_failed = await self._get_completed_failed_counts()
            
            return {
                'total_active': total_active,
                'total_pending': total_pending,
                'total_completed': completed_failed.get('completed', 0),
                'total_failed': completed_failed.get('failed', 0)
            }
            
        except Exception as e:
            self.logger.error(f"收集佇列指標失敗: {e}")
            return {'total_active': 0, 'total_pending': 0, 'total_completed': 0, 'total_failed': 0}
    
    async def _collect_worker_metrics(self) -> Dict[str, Dict[str, Any]]:
        """收集工作者狀態指標"""
        try:
            if not self._inspect:
                return {'worker_status': {}, 'worker_load': {}}
            
            # 獲取工作者統計資訊
            stats = self._inspect.stats() or {}
            active_tasks = self._inspect.active() or {}
            
            worker_status = {}
            worker_load = {}
            
            # 處理每個工作者
            for worker_name in stats.keys():
                worker_status[worker_name] = 'online'
                worker_load[worker_name] = len(active_tasks.get(worker_name, []))
            
            # 檢查離線工作者（如果有配置的工作者列表）
            expected_workers = await self._get_expected_workers()
            for worker_name in expected_workers:
                if worker_name not in worker_status:
                    worker_status[worker_name] = 'offline'
                    worker_load[worker_name] = 0
            
            return {
                'worker_status': worker_status,
                'worker_load': worker_load
            }
            
        except Exception as e:
            self.logger.error(f"收集工作者指標失敗: {e}")
            return {'worker_status': {}, 'worker_load': {}}
    
    async def _collect_task_type_metrics(self) -> Dict[str, Dict[str, Dict[str, int]]]:
        """收集任務類型分組統計"""
        try:
            if not self._inspect:
                return {'task_type_counts': {}}
            
            # 獲取活躍和待處理任務
            active_tasks = self._inspect.active() or {}
            reserved_tasks = self._inspect.reserved() or {}
            
            task_type_counts = {}
            
            # 初始化任務類型計數
            for task_type in self.task_type_mapping.values():
                task_type_counts[task_type] = {
                    'active': 0,
                    'pending': 0,
                    'completed': 0,
                    'failed': 0
                }
            
            # 統計活躍任務
            for worker_tasks in active_tasks.values():
                for task in worker_tasks:
                    task_name = task.get('name', '')
                    task_type = self.task_type_mapping.get(task_name, 'unknown')
                    if task_type in task_type_counts:
                        task_type_counts[task_type]['active'] += 1
            
            # 統計待處理任務
            for worker_tasks in reserved_tasks.values():
                for task in worker_tasks:
                    task_name = task.get('name', '')
                    task_type = self.task_type_mapping.get(task_name, 'unknown')
                    if task_type in task_type_counts:
                        task_type_counts[task_type]['pending'] += 1
            
            # 獲取歷史完成和失敗統計
            historical_counts = await self._get_task_type_historical_counts()
            for task_type, counts in historical_counts.items():
                if task_type in task_type_counts:
                    task_type_counts[task_type]['completed'] = counts.get('completed', 0)
                    task_type_counts[task_type]['failed'] = counts.get('failed', 0)
            
            return {'task_type_counts': task_type_counts}
            
        except Exception as e:
            self.logger.error(f"收集任務類型指標失敗: {e}")
            return {'task_type_counts': {}}
    
    async def _collect_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """收集任務效能指標"""
        try:
            # 獲取任務平均執行時間和成功率
            avg_duration = await self._calculate_avg_task_duration()
            success_rate = await self._calculate_task_success_rate()
            
            return {
                'avg_task_duration': avg_duration,
                'task_success_rate': success_rate
            }
            
        except Exception as e:
            self.logger.error(f"收集效能指標失敗: {e}")
            return {'avg_task_duration': {}, 'task_success_rate': {}}
    
    async def _collect_long_running_tasks(self) -> List[Dict[str, Any]]:
        """收集長時間運行的任務"""
        try:
            if not self._inspect:
                return []
            
            active_tasks = self._inspect.active() or {}
            long_running_tasks = []
            current_time = datetime.now()
            
            # 檢查每個活躍任務的運行時間
            for worker_name, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    task_name = task.get('name', 'unknown')
                    task_id = task.get('id', 'unknown')
                    
                    # 計算任務運行時間
                    time_start = task.get('time_start')
                    if time_start:
                        try:
                            start_time = datetime.fromtimestamp(time_start)
                            duration = (current_time - start_time).total_seconds()
                            
                            # 如果運行時間超過閾值（預設 30 分鐘）
                            if duration > 1800:  # 30 分鐘
                                long_running_tasks.append({
                                    'task_id': task_id,
                                    'name': task_name,
                                    'worker': worker_name,
                                    'duration': duration,
                                    'started_at': start_time.isoformat()
                                })
                        except (ValueError, TypeError) as e:
                            self.logger.debug(f"解析任務開始時間失敗: {e}")
            
            return long_running_tasks
            
        except Exception as e:
            self.logger.error(f"收集長時間運行任務失敗: {e}")
            return []
    
    async def _get_completed_failed_counts(self) -> Dict[str, int]:
        """獲取已完成和失敗任務數量"""
        try:
            # 這裡可以從 Redis 結果後端或資料庫獲取歷史資料
            # 暫時返回模擬資料
            return {
                'completed': 0,
                'failed': 0
            }
        except Exception as e:
            self.logger.error(f"獲取完成/失敗任務數量失敗: {e}")
            return {'completed': 0, 'failed': 0}
    
    async def _get_expected_workers(self) -> List[str]:
        """獲取預期的工作者列表"""
        try:
            # 這裡可以從配置或註冊表獲取預期的工作者
            # 暫時返回空列表
            return []
        except Exception as e:
            self.logger.error(f"獲取預期工作者列表失敗: {e}")
            return []
    
    async def _get_task_type_historical_counts(self) -> Dict[str, Dict[str, int]]:
        """獲取任務類型的歷史統計"""
        try:
            # 這裡可以從資料庫獲取歷史統計資料
            # 暫時返回空字典
            return {}
        except Exception as e:
            self.logger.error(f"獲取任務類型歷史統計失敗: {e}")
            return {}
    
    async def _calculate_avg_task_duration(self) -> Dict[str, float]:
        """計算各任務類型的平均執行時間"""
        try:
            # 這裡可以從歷史記錄計算平均執行時間
            # 暫時返回空字典
            return {}
        except Exception as e:
            self.logger.error(f"計算平均任務執行時間失敗: {e}")
            return {}
    
    async def _calculate_task_success_rate(self) -> Dict[str, float]:
        """計算各任務類型的成功率"""
        try:
            # 這裡可以從歷史記錄計算成功率
            # 暫時返回空字典
            return {}
        except Exception as e:
            self.logger.error(f"計算任務成功率失敗: {e}")
            return {}
    
    def _get_default_metrics(self) -> CeleryMetrics:
        """獲取預設指標（錯誤時使用）"""
        return CeleryMetrics(
            total_active=0,
            total_pending=0,
            total_completed=0,
            total_failed=0,
            task_type_counts={},
            worker_status={},
            worker_load={},
            avg_task_duration={},
            task_success_rate={},
            long_running_tasks=[],
            timestamp=datetime.now()
        )
    
    async def get_health_status(self) -> Dict[str, Any]:
        """獲取收集器健康狀態"""
        try:
            is_connected = await self._initialize_celery_connection()
            
            return {
                'collector_name': 'celery_collector',
                'status': 'healthy' if is_connected else 'unhealthy',
                'celery_connected': is_connected,
                'last_check': datetime.now().isoformat(),
                'supported_task_types': list(self.task_type_mapping.values())
            }
        except Exception as e:
            return {
                'collector_name': 'celery_collector',
                'status': 'error',
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }


# 全域實例管理
_collector_instance: Optional[DashboardCeleryCollector] = None

def get_celery_collector(config: Optional[DashboardConfig] = None) -> DashboardCeleryCollector:
    """獲取 Celery 收集器單例"""
    global _collector_instance
    
    if _collector_instance is None:
        _collector_instance = DashboardCeleryCollector(config)
        
    return _collector_instance