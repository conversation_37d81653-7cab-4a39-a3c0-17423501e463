---
inclusion: fileMatch
fileMatchPattern: 'src/dashboard_monitoring/**/*'
---

# 統一監控儀表板開發指導原則

## 專案概述

統一監控儀表板是一個**全方位即時監控系統**，專為半導體郵件處理基礎設施設計。

### 核心目標
- 📧 **郵件處理監控** - code_comparison.py 任務、廠商分組統計
- 🔄 **Celery任務監控** - 長時間任務、工作者狀態、佇列管理  
- 💻 **系統資源監控** - CPU/記憶體/磁碟、服務健康狀態
- 📊 **業務指標監控** - MO/LOT統計、資料品質、報告生成
- 🚨 **智能告警系統** - 多級告警、自動通知、告警合併

### 技術架構
- **前端**: HTML5 + JavaScript + WebSocket (即時更新)
- **後端**: FastAPI + Python 3.11+ + 非同步處理
- **資料庫**: SQLite (擴展現有 outlook.db)
- **整合**: start_integrated_services.py (5555端口)

## 開發原則

### 最小侵入原則 (CRITICAL)
- **MUST NOT** 影響現有系統運行
- **MUST** 實現錯誤隔離 - 監控系統故障不應影響主要業務功能
- **MUST** 使用獨立模組設計，避免與現有代碼耦合
- **MUST** 在 `src/dashboard_monitoring/` 目錄下組織所有相關代碼

### 目錄結構規範

**MUST** 遵循以下目錄結構：
```
src/dashboard_monitoring/
├── core/                    # 核心業務邏輯
├── collectors/              # 資料收集器
├── models/                  # 資料模型
├── repositories/            # 資料存取層
├── api/                     # API 端點
├── templates/               # HTML 模板
├── static/                  # 靜態資源
├── utils/                   # 工具函數
└── config/                  # 配置文件
```

### 命名規範

**MUST** 使用 `dashboard_` 前綴：
- **檔案**: `dashboard_config.py`, `dashboard_monitoring_api.py`
- **類別**: `DashboardConfig`, `MonitoringCoordinator`
- **環境變數**: `DASHBOARD_METRICS_UPDATE_INTERVAL`
- **資料庫表**: `dashboard_metrics_history`, `dashboard_alert_rules`

### 資料收集原則

**MUST** 遵循以下資料收集規範：
- **非侵入式收集**: 只讀取現有系統狀態，不修改業務邏輯
- **錯誤隔離**: 單一收集器失敗不影響其他收集器
- **效能考量**: 資料收集頻率要平衡即時性和系統負載
- **批量處理**: 減少資料庫查詢次數，使用連接池

### 整合現有系統

**MUST** 正確整合現有元件：
- **EmailDatabase**: 從 `src/infrastructure/database/email_database.py` 讀取郵件狀態
- **ConcurrentTaskManager**: 從現有任務管理器獲取 Celery 狀態
- **start_integrated_services.py**: 在主程式中註冊監控服務

### 配置管理

**MUST** 實現靈活的配置系統：
- **環境變數覆蓋**: 支援 `DASHBOARD_*` 環境變數
- **預設值**: 提供合理的預設配置
- **驗證機制**: 配置載入時進行驗證
- **動態更新**: 支援運行時配置更新

### 告警系統設計

**MUST** 實現智能告警機制：
- **多級告警**: info, warning, error, critical
- **告警合併**: 避免重複告警造成通知氾濫
- **多管道通知**: 支援郵件、LINE、系統通知
- **告警歷史**: 記錄所有告警事件供分析

### 即時更新機制

**MUST** 實現高效的即時更新：
- **WebSocket**: 用於前端即時資料推送
- **更新頻率**: 5秒內反映系統變化
- **連接管理**: 處理 WebSocket 連接異常和重連
- **訂閱機制**: 客戶端可選擇訂閱特定類型的更新

### 資料庫設計

**MUST** 擴展現有 outlook.db：
- **新增表格**: 監控指標、告警記錄、任務歷史
- **索引最佳化**: 建立適當索引提升查詢效能
- **資料清理**: 定期清理過期資料
- **向後相容**: 不影響現有資料結構

### 前端設計原則

**MUST** 實現使用者友好的介面：
- **響應式設計**: 支援不同螢幕尺寸
- **即時更新**: 無需重新整理頁面
- **視覺化**: 使用圖表展示趨勢資料
- **互動性**: 支援篩選、搜尋、詳細檢視

### 效能要求

**MUST** 滿足效能指標：
- **資料收集**: 單次收集不超過 2 秒
- **API 回應**: 95% 請求在 500ms 內回應
- **WebSocket**: 支援至少 100 個並發連接
- **記憶體使用**: 監控服務記憶體使用不超過 256MB

### 安全考量

**MUST** 實現安全機制：
- **資料保護**: 不洩露敏感的業務資料
- **訪問控制**: API 端點需要適當的權限檢查
- **審計日誌**: 記錄所有重要操作
- **錯誤處理**: 不在錯誤訊息中洩露系統資訊

### 測試要求

**MUST** 實現完整的測試覆蓋：
- **單元測試**: 覆蓋率達到 90% 以上
- **整合測試**: 測試與現有系統的整合
- **效能測試**: 驗證系統負載能力
- **端到端測試**: 測試完整的監控流程

### 文檔要求

**MUST** 提供完整的文檔：
- **API 文檔**: 所有端點的詳細說明
- **部署指南**: 安裝和配置步驟
- **操作手冊**: 日常使用和維護指南
- **故障排除**: 常見問題和解決方案

## 業務領域知識

### 監控指標定義
- **郵件佇列**: pending, processing, completed, failed
- **Celery 任務**: code_comparison, csv_to_summary, compression, decompression
- **系統資源**: CPU, 記憶體, 磁碟使用率
- **業務指標**: MO/LOT 處理統計, 資料品質分數

### 告警閾值
- **郵件佇列**: 待處理 >10 警告, >50 嚴重
- **Celery 任務**: 待處理 >20 警告, >100 嚴重
- **系統資源**: CPU/記憶體 >80% 警告, >95% 嚴重
- **失敗率**: >10% 警告, >25% 嚴重

### 廠商支援
監控系統需要支援現有的廠商分類：
- **ETD**: `anf` 關鍵字
- **GTK**: `ft hold`, `ft lot` 關鍵字
- **JCET**: `jcet` 關鍵字
- **LINGSEN**: `lingsen` 關鍵字
- **XAHT**: `tianshui`, `西安` 關鍵字

## 實作檢查清單

開發任何監控功能時，**MUST** 確認：
- [ ] 不影響現有系統運行
- [ ] 實現適當的錯誤隔離
- [ ] 使用正確的命名規範
- [ ] 實現配置驗證機制
- [ ] 添加適當的日誌記錄
- [ ] 撰寫對應的單元測試
- [ ] 更新相關文檔