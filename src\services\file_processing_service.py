"""檔案處理服務
整合 csv_to_summary.py 和 code_comparison.py 為可呼叫的服務函式
包含自動檔案暫存功能，支援重試機制、超時處理和失敗回滾
"""

import os
import sys
import asyncio
import subprocess
import uuid
import signal
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from contextlib import asynccontextmanager

from loguru import logger
from .file_staging_service import get_file_staging_service, StagingResult


class ProcessingStatus(str, Enum):
    """處理狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    TIMEOUT = "timeout"


class ProcessingTool(str, Enum):
    """處理工具類型"""
    CSV_SUMMARY = "csv_summary"
    CODE_COMPARISON = "code_comparison"


class ProcessingError(Exception):
    """處理相關錯誤基類"""
    pass


class ProcessingTimeoutError(ProcessingError):
    """處理超時錯誤"""
    pass


class ProcessingRetryExhaustedError(ProcessingError):
    """重試次數耗盡錯誤"""
    pass


class ProcessingCancellationError(ProcessingError):
    """處理取消錯誤"""
    pass


class ToolExecutionError(ProcessingError):
    """工具執行錯誤"""
    pass


@dataclass
class RetryConfig:
    """重試配置"""
    max_retries: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True  # 添加隨機抖動避免重試風暴


@dataclass
class ProcessingTask:
    """處理任務"""
    task_id: str
    tool: ProcessingTool
    input_path: str
    status: ProcessingStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    output_files: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    progress: float = 0.0
    staging_task_id: Optional[str] = None  # 關聯的暫存任務ID
    staged_path: Optional[str] = None  # 暫存後的路徑
    use_staging: bool = False  # 是否使用暫存功能
    
    # 錯誤處理和重試相關
    retry_count: int = 0
    retry_config: RetryConfig = field(default_factory=RetryConfig)
    last_retry_at: Optional[datetime] = None
    timeout: Optional[float] = None  # 任務超時時間（秒）
    cancelled: bool = False
    cancel_event: Optional[asyncio.Event] = field(default_factory=lambda: asyncio.Event())
    
    # 回滾相關
    rollback_actions: List[Callable] = field(default_factory=list)
    rollback_completed: bool = False
    
    # 監控資料
    process_id: Optional[int] = None
    cpu_usage: float = 0.0
    memory_usage: int = 0
    execution_time: float = 0.0


@dataclass
class ProcessingResult:
    """處理結果"""
    success: bool
    task_id: str
    output_files: List[str]
    processing_time: float
    tool_used: str
    error_message: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    
    # 錯誤處理相關
    retries_used: int = 0
    timeout_occurred: bool = False
    cancelled: bool = False
    rollback_performed: bool = False
    
    # 效能統計
    peak_cpu_usage: float = 0.0
    peak_memory_usage: int = 0
    average_speed: float = 0.0  # 處理速度（檔案/秒或MB/秒）


class TaskTimeout:
    """任務超時管理器"""
    
    def __init__(self):
        self.timeout_tasks: Dict[str, asyncio.Task] = {}
        self.lock = threading.Lock()
    
    def set_timeout(self, task_id: str, timeout: float, callback: Callable):
        """設定任務超時"""
        async def timeout_handler():
            await asyncio.sleep(timeout)
            await callback(task_id)
        
        with self.lock:
            # 取消現有的超時任務
            if task_id in self.timeout_tasks:
                self.timeout_tasks[task_id].cancel()
            
            # 建立新的超時任務
            self.timeout_tasks[task_id] = asyncio.create_task(timeout_handler())
    
    def cancel_timeout(self, task_id: str):
        """取消任務超時"""
        with self.lock:
            if task_id in self.timeout_tasks:
                self.timeout_tasks[task_id].cancel()
                del self.timeout_tasks[task_id]


class FileProcessingService:
    """檔案處理服務
    
    整合現有的處理工具為可呼叫的服務函式
    支援重試機制、超時處理、失敗回滾和並發安全
    """
    
    def __init__(
        self,
        default_timeout: float = 1800.0,  # 30分鐘預設超時
        max_concurrent_tasks: int = 3,
        enable_rollback: bool = True,
        default_retry_config: Optional[RetryConfig] = None
    ):
        self.tasks: Dict[str, ProcessingTask] = {}
        self.project_root = Path(__file__).parent.parent.parent
        self.csv_tool_path = self.project_root / "csv_to_summary.py"
        self.code_tool_path = self.project_root / "code_comparison.py"
        
        # 配置參數
        self.default_timeout = default_timeout
        self.max_concurrent_tasks = max_concurrent_tasks
        self.enable_rollback = enable_rollback
        self.default_retry_config = default_retry_config or RetryConfig()
        
        # 並發控制
        self._lock = threading.RLock()
        self.active_task_count = 0
        
        # 超時管理
        self.timeout_manager = TaskTimeout()
        
        # 驗證工具檔案存在
        if not self.csv_tool_path.exists():
            logger.warning(f"CSV 工具檔案不存在: {self.csv_tool_path}")
        if not self.code_tool_path.exists():
            logger.warning(f"程式碼比較工具檔案不存在: {self.code_tool_path}")
            
        logger.info(f"檔案處理服務已初始化 - 預設超時: {default_timeout}秒, "
                   f"最大並發任務: {max_concurrent_tasks}")
    
    def create_task(
        self, 
        tool: ProcessingTool, 
        input_path: str, 
        use_staging: bool = False,
        timeout: Optional[float] = None,
        retry_config: Optional[RetryConfig] = None
    ) -> str:
        """建立處理任務"""
        task_id = str(uuid.uuid4())
        task = ProcessingTask(
            task_id=task_id,
            tool=tool,
            input_path=input_path,
            status=ProcessingStatus.PENDING,
            created_at=datetime.now(),
            use_staging=use_staging,
            timeout=timeout or self.default_timeout,
            retry_config=retry_config or self.default_retry_config
        )
        
        with self._lock:
            self.tasks[task_id] = task
        
        logger.info(f"建立處理任務: {task_id}, 工具: {tool}, 輸入: {input_path}, "
                   f"使用暫存: {use_staging}, 超時: {task.timeout}秒")
        return task_id
    
    def create_task_with_staging(
        self, 
        tool: ProcessingTool, 
        source_files: List[Union[str, Path]], 
        product_name: str,
        preserve_structure: bool = True,
        timeout: Optional[float] = None,
        retry_config: Optional[RetryConfig] = None,
        use_unique_name: bool = True
    ) -> str:
        """建立帶有暫存功能的處理任務"""
        task_id = str(uuid.uuid4())
        
        # 將來源檔案列表轉換為字串（用於顯示）
        input_path_display = f"多個檔案 ({len(source_files)} 個)"
        
        task = ProcessingTask(
            task_id=task_id,
            tool=tool,
            input_path=input_path_display,
            status=ProcessingStatus.PENDING,
            created_at=datetime.now(),
            use_staging=True,
            timeout=timeout or self.default_timeout,
            retry_config=retry_config or self.default_retry_config
        )
        
        with self._lock:
            self.tasks[task_id] = task
        
        logger.info(f"建立帶暫存的處理任務: {task_id}, 工具: {tool}, 產品: {product_name}, "
                   f"檔案數: {len(source_files)}, 超時: {task.timeout}秒, 使用唯一名稱: {use_unique_name}")
        return task_id
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        with self._lock:
            task = self.tasks.get(task_id)
        
        if not task:
            logger.warning(f"嘗試取消不存在的任務: {task_id}")
            return False
        
        if task.status not in [ProcessingStatus.PENDING, ProcessingStatus.IN_PROGRESS, ProcessingStatus.RETRYING]:
            logger.warning(f"無法取消任務 {task_id}，當前狀態: {task.status}")
            return False
        
        task.cancelled = True
        task.status = ProcessingStatus.CANCELLED
        task.completed_at = datetime.now()
        
        # 取消超時任務
        self.timeout_manager.cancel_timeout(task_id)
        
        # 設定取消事件
        if task.cancel_event:
            task.cancel_event.set()
        
        # 如果任務正在執行，嘗試終止進程
        if task.process_id:
            try:
                os.kill(task.process_id, signal.SIGTERM)
                logger.info(f"已發送終止信號給進程 {task.process_id}")
            except ProcessLookupError:
                logger.info(f"進程 {task.process_id} 已不存在")
            except Exception as e:
                logger.warning(f"無法終止進程 {task.process_id}: {e}")
        
        # 執行回滾
        if self.enable_rollback:
            await self._perform_rollback(task)
        
        logger.info(f"任務已取消: {task_id}")
        return True
    
    async def _perform_rollback(self, task: ProcessingTask):
        """執行任務回滾"""
        if task.rollback_completed:
            return
        
        try:
            logger.info(f"開始執行任務 {task.task_id} 的回滾操作")
            
            # 執行回滾動作
            for rollback_action in reversed(task.rollback_actions):
                try:
                    if asyncio.iscoroutinefunction(rollback_action):
                        await rollback_action()
                    else:
                        rollback_action()
                except Exception as e:
                    logger.error(f"回滾動作執行失敗: {e}")
            
            # 清理暫存檔案
            if task.staging_task_id:
                staging_service = get_file_staging_service()
                await staging_service.cleanup_staging_directory(task.staging_task_id)
            
            # 清理輸出檔案
            for output_file in task.output_files:
                try:
                    output_path = Path(output_file)
                    if output_path.exists():
                        output_path.unlink()
                        logger.debug(f"已清理輸出檔案: {output_file}")
                except Exception as e:
                    logger.warning(f"清理輸出檔案失敗 {output_file}: {e}")
            
            task.rollback_completed = True
            logger.info(f"任務 {task.task_id} 回滾完成")
            
        except Exception as e:
            logger.error(f"任務 {task.task_id} 回滾失敗: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[ProcessingTask]:
        """取得任務狀態"""
        with self._lock:
            return self.tasks.get(task_id)
    
    async def _handle_task_timeout(self, task_id: str):
        """處理任務超時"""
        with self._lock:
            task = self.tasks.get(task_id)
        
        if not task or task.status not in [ProcessingStatus.IN_PROGRESS, ProcessingStatus.RETRYING]:
            return
        
        logger.warning(f"任務 {task_id} 執行超時 ({task.timeout}秒)")
        
        task.status = ProcessingStatus.TIMEOUT
        task.error_message = f"任務執行超時 ({task.timeout}秒)"
        task.completed_at = datetime.now()
        
        # 終止進程
        if task.process_id:
            try:
                os.kill(task.process_id, signal.SIGTERM)
                # 等待進程終止
                await asyncio.sleep(5)
                # 如果進程仍然存在，強制殺死
                try:
                    os.kill(task.process_id, signal.SIGKILL)
                except ProcessLookupError:
                    pass
            except Exception as e:
                logger.error(f"終止超時任務進程失敗: {e}")
        
        # 執行回滾
        if self.enable_rollback:
            await self._perform_rollback(task)
    
    async def _execute_with_retry(
        self,
        task: ProcessingTask,
        execution_func: Callable,
        *args,
        **kwargs
    ) -> ProcessingResult:
        """帶重試機制的執行函式"""
        last_error = None
        
        for attempt in range(task.retry_config.max_retries + 1):
            if task.cancelled:
                raise ProcessingCancellationError("任務已被取消")
            
            try:
                if attempt > 0:
                    task.status = ProcessingStatus.RETRYING
                    task.retry_count = attempt
                    task.last_retry_at = datetime.now()
                    
                    # 計算重試延遲（指數退避 + 抖動）
                    delay = min(
                        task.retry_config.initial_delay * (task.retry_config.exponential_base ** (attempt - 1)),
                        task.retry_config.max_delay
                    )
                    
                    if task.retry_config.jitter:
                        import random
                        delay *= (0.5 + random.random() * 0.5)  # 50-100% 的延遲
                    
                    logger.info(f"任務 {task.task_id} 第 {attempt} 次重試，延遲 {delay:.2f} 秒")
                    await asyncio.sleep(delay)
                
                # 執行任務
                result = await execution_func(*args, **kwargs)
                result.retries_used = attempt
                return result
                
            except ProcessingCancellationError:
                raise
            except Exception as e:
                last_error = e
                logger.warning(f"任務 {task.task_id} 第 {attempt + 1} 次執行失敗: {e}")
                
                if attempt < task.retry_config.max_retries:
                    continue
                else:
                    # 重試次數耗盡
                    raise ProcessingRetryExhaustedError(
                        f"任務執行失敗，已重試 {task.retry_config.max_retries} 次。最後錯誤: {e}"
                    ) from last_error
        
        # 這行理論上不會到達
        raise ProcessingRetryExhaustedError(f"未知錯誤，最後錯誤: {last_error}")
    
    async def execute_with_staging(
        self,
        task_id: str,
        source_files: List[Union[str, Path]],
        product_name: str,
        preserve_structure: bool = True,
        use_unique_name: bool = True
    ) -> ProcessingResult:
        """執行帶有暫存功能的處理任務"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                raise ValueError(f"處理任務不存在: {task_id}")
            
            if not task.use_staging:
                raise ValueError(f"任務未啟用暫存功能: {task_id}")
            
            # 檢查並發限制
            if self.active_task_count >= self.max_concurrent_tasks:
                raise ProcessingError(f"超過最大並發任務數: {self.max_concurrent_tasks}")
            
            self.active_task_count += 1
            task.status = ProcessingStatus.IN_PROGRESS
            task.started_at = datetime.now()
        
        # 設定超時處理
        self.timeout_manager.set_timeout(task_id, task.timeout, self._handle_task_timeout)
        
        try:
            return await self._execute_with_retry(
                task,
                self._execute_staging_workflow,
                task,
                source_files,
                product_name,
                preserve_structure
            )
        finally:
            # 清理資源
            with self._lock:
                self.active_task_count = max(0, self.active_task_count - 1)
            self.timeout_manager.cancel_timeout(task_id)
    
    async def _execute_staging_workflow(
        self,
        task: ProcessingTask,
        source_files: List[Union[str, Path]],
        product_name: str,
        preserve_structure: bool
    ) -> ProcessingResult:
        """執行暫存工作流程"""
        start_time = datetime.now()
        staging_service = get_file_staging_service()
        
        try:
            logger.info(f"開始執行帶暫存的處理任務: {task.task_id}")
            
            # 步驟1: 建立暫存任務
            logger.info(f"建立暫存任務: 產品={product_name}, 檔案數={len(source_files)}")
            staging_task_id = staging_service.create_staging_task(
                product_name=product_name,
                source_files=source_files,
                preserve_structure=preserve_structure,
                use_unique_name=True  # 預設使用唯一名稱
            )
            task.staging_task_id = staging_task_id
            task.progress = 10.0
            
            # 添加回滾動作
            task.rollback_actions.append(
                lambda: staging_service.cleanup_staging_directory(staging_task_id)
            )
            
            # 步驟2: 執行暫存
            logger.info(f"執行暫存任務: {staging_task_id}")
            staging_result = await staging_service.execute_staging_task(staging_task_id)
            
            if not staging_result.success:
                raise ToolExecutionError(f"檔案暫存失敗: {staging_result.error_message}")
            
            task.staged_path = str(staging_result.staging_directory)
            task.progress = 40.0
            logger.info(f"檔案暫存完成: {staging_result.staging_directory}")
            
            # 步驟3: 使用暫存路徑執行處理工具
            if task.tool == ProcessingTool.CSV_SUMMARY:
                processing_result = await self._execute_tool_on_staged_files(
                    task, staging_result, "csv_summary"
                )
            elif task.tool == ProcessingTool.CODE_COMPARISON:
                processing_result = await self._execute_tool_on_staged_files(
                    task, staging_result, "code_comparison"
                )
            else:
                raise ValueError(f"不支援的處理工具: {task.tool}")
            
            task.progress = 90.0
            
            # 檢查工具執行結果
            if not processing_result.success:
                raise ToolExecutionError(processing_result.error_message)
            
            # 步驟4: 完成任務
            task.status = ProcessingStatus.COMPLETED
            task.completed_at = datetime.now()
            task.progress = 100.0
            
            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time
            
            result = ProcessingResult(
                success=True,
                task_id=task.task_id,
                output_files=processing_result.output_files,
                processing_time=processing_time,
                tool_used=processing_result.tool_used,
                logs=processing_result.logs
            )
            
            logger.info(f"帶暫存的處理任務完成: {task.task_id}, 耗時: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            # 任務失敗處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.execution_time = processing_time
            
            # 執行回滾
            if self.enable_rollback:
                await self._perform_rollback(task)
            
            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used=task.tool.value,
                error_message=error_msg,
                timeout_occurred=isinstance(e, ProcessingTimeoutError),
                cancelled=isinstance(e, ProcessingCancellationError),
                rollback_performed=self.enable_rollback
            )
            
            logger.error(f"帶暫存的處理任務失敗: {task.task_id}, 錯誤: {error_msg}")
            return result
    
    async def _execute_tool_on_staged_files(
        self,
        task: ProcessingTask,
        staging_result: StagingResult,
        tool_type: str
    ) -> ProcessingResult:
        """在暫存檔案上執行處理工具"""
        try:
            # 選擇工具路徑
            if tool_type == "csv_summary":
                tool_path = self.csv_tool_path
                tool_name = "csv_to_summary.py"
            elif tool_type == "code_comparison":
                tool_path = self.code_tool_path
                tool_name = "code_comparison.py"
            else:
                raise ValueError(f"不支援的工具類型: {tool_type}")
            
            # 準備執行命令 - 使用暫存目錄作為輸入
            cmd = [
                sys.executable,
                str(tool_path),
                str(staging_result.staging_directory)
            ]
            
            logger.info(f"在暫存檔案上執行工具: {' '.join(cmd)}")
            
            # 執行命令（帶取消支援）
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            # 記錄進程ID以便取消時終止
            task.process_id = process.pid
            
            # 等待進程完成或取消
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=None  # 由外層超時控制
                )
            except asyncio.CancelledError:
                # 任務被取消，終止進程
                if process.returncode is None:
                    process.terminate()
                    await process.wait()
                raise ProcessingCancellationError("工具執行被取消")
            
            if process.returncode == 0:
                # 成功
                output_files = self._find_output_files(staging_result.staging_directory, tool_type)
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task.task_id,
                    output_files=task.output_files,
                    processing_time=0.0,  # 這裡會在上層計算
                    tool_used=tool_name,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"工具執行成功: {tool_name}")
                return result
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                
                result = ProcessingResult(
                    success=False,
                    task_id=task.task_id,
                    output_files=[],
                    processing_time=0.0,
                    tool_used=tool_name,
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.error(f"工具執行失敗: {tool_name}, 錯誤: {error_msg}")
                return result
                
        except ProcessingCancellationError:
            raise
        except Exception as e:
            error_msg = str(e)
            
            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=0.0,
                tool_used=tool_type,
                error_message=error_msg
            )
            
            logger.error(f"工具執行異常: {tool_type}, 錯誤: {error_msg}")
            return result
        finally:
            task.process_id = None
    
    async def execute_csv_summary(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult:
        """執行 CSV 摘要生成"""
        if not task_id:
            task_id = self.create_task(ProcessingTool.CSV_SUMMARY, input_path)
        
        with self._lock:
            task = self.tasks[task_id]
            
            # 檢查並發限制
            if self.active_task_count >= self.max_concurrent_tasks:
                raise ProcessingError(f"超過最大並發任務數: {self.max_concurrent_tasks}")
            
            self.active_task_count += 1
            task.status = ProcessingStatus.IN_PROGRESS
            task.started_at = datetime.now()
        
        # 設定超時處理
        self.timeout_manager.set_timeout(task_id, task.timeout, self._handle_task_timeout)
        
        try:
            return await self._execute_with_retry(
                task,
                self._execute_csv_summary_workflow,
                task,
                input_path
            )
        finally:
            with self._lock:
                self.active_task_count = max(0, self.active_task_count - 1)
            self.timeout_manager.cancel_timeout(task_id)
    
    async def _execute_csv_summary_workflow(self, task: ProcessingTask, input_path: str) -> ProcessingResult:
        """執行 CSV 摘要工作流程"""
        start_time = datetime.now()
        
        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
            
            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.csv_tool_path),
                str(input_path_obj)
            ]
            
            logger.info(f"執行 CSV 摘要命令: {' '.join(cmd)}")
            
            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            task.process_id = process.pid
            
            try:
                stdout, stderr = await process.communicate()
            except asyncio.CancelledError:
                if process.returncode is None:
                    process.terminate()
                    await process.wait()
                raise ProcessingCancellationError("CSV 摘要執行被取消")
            
            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time
            
            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "csv_summary")
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task.task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"CSV 摘要處理成功: {task.task_id}")
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()
                
                result = ProcessingResult(
                    success=False,
                    task_id=task.task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.error(f"CSV 摘要處理失敗: {task.task_id}, 錯誤: {error_msg}")
            
            return result
            
        except ProcessingCancellationError:
            raise
        except Exception as e:
            # 例外處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.execution_time = processing_time
            
            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="csv_to_summary.py",
                error_message=error_msg
            )
            
            logger.error(f"CSV 摘要處理例外: {task.task_id}, 錯誤: {error_msg}")
            return result
        finally:
            task.process_id = None
    
    async def execute_code_comparison(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult:
        """執行程式碼比較"""
        if not task_id:
            task_id = self.create_task(ProcessingTool.CODE_COMPARISON, input_path)
        
        with self._lock:
            task = self.tasks[task_id]
            
            # 檢查並發限制
            if self.active_task_count >= self.max_concurrent_tasks:
                raise ProcessingError(f"超過最大並發任務數: {self.max_concurrent_tasks}")
            
            self.active_task_count += 1
            task.status = ProcessingStatus.IN_PROGRESS
            task.started_at = datetime.now()
        
        # 設定超時處理
        self.timeout_manager.set_timeout(task_id, task.timeout, self._handle_task_timeout)
        
        try:
            return await self._execute_with_retry(
                task,
                self._execute_code_comparison_workflow,
                task,
                input_path
            )
        finally:
            with self._lock:
                self.active_task_count = max(0, self.active_task_count - 1)
            self.timeout_manager.cancel_timeout(task_id)
    
    async def _execute_code_comparison_workflow(self, task: ProcessingTask, input_path: str) -> ProcessingResult:
        """執行程式碼比較工作流程"""
        start_time = datetime.now()
        
        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
            
            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.code_tool_path),
                str(input_path_obj)
            ]
            
            logger.info(f"執行程式碼比較命令: {' '.join(cmd)}")
            
            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            task.process_id = process.pid
            
            try:
                stdout, stderr = await process.communicate()
            except asyncio.CancelledError:
                if process.returncode is None:
                    process.terminate()
                    await process.wait()
                raise ProcessingCancellationError("程式碼比較執行被取消")
            
            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time
            
            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "code_comparison")
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task.task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"程式碼比較處理成功: {task.task_id}")
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()
                
                result = ProcessingResult(
                    success=False,
                    task_id=task.task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.error(f"程式碼比較處理失敗: {task.task_id}, 錯誤: {error_msg}")
            
            return result
            
        except ProcessingCancellationError:
            raise
        except Exception as e:
            # 例外處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.execution_time = processing_time
            
            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="code_comparison.py",
                error_message=error_msg
            )
            
            logger.error(f"程式碼比較處理例外: {task.task_id}, 錯誤: {error_msg}")
            return result
        finally:
            task.process_id = None
    
    def _find_output_files(self, input_path: Path, tool_type: str) -> List[Path]:
        """尋找輸出檔案"""
        output_files = []
        
        try:
            # 根據工具類型尋找可能的輸出檔案
            if tool_type == "csv_summary":
                # CSV 摘要工具通常會產生 Excel 檔案
                patterns = ["*summary*.xlsx", "*摘要*.xlsx", "*_processed.xlsx"]
            elif tool_type == "code_comparison":
                # 程式碼比較工具通常會產生比較報告
                patterns = ["*comparison*.xlsx", "*比較*.xlsx", "*_diff.xlsx"]
            else:
                patterns = ["*.xlsx", "*.csv"]
            
            # 在輸入目錄及其父目錄中搜尋
            search_dirs = [input_path]
            if input_path.is_file():
                search_dirs.append(input_path.parent)
            
            for search_dir in search_dirs:
                if search_dir.is_dir():
                    for pattern in patterns:
                        output_files.extend(search_dir.glob(pattern))
            
            # 移除重複並按修改時間排序
            output_files = list(set(output_files))
            output_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
        except Exception as e:
            logger.warning(f"尋找輸出檔案時發生錯誤: {e}")
        
        return output_files
    
    async def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """取得任務進度"""
        with self._lock:
            task = self.tasks.get(task_id)
        
        if not task:
            return {"error": "任務不存在"}
        
        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.progress,
            "tool": task.tool.value,
            "input_path": task.input_path,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "output_files": task.output_files,
            "error_message": task.error_message,
            "staging_task_id": task.staging_task_id,
            "staged_path": task.staged_path,
            "use_staging": task.use_staging,
            # 錯誤處理相關
            "retry_count": task.retry_count,
            "max_retries": task.retry_config.max_retries,
            "last_retry_at": task.last_retry_at.isoformat() if task.last_retry_at else None,
            "timeout": task.timeout,
            "cancelled": task.cancelled,
            "rollback_completed": task.rollback_completed,
            # 效能監控
            "process_id": task.process_id,
            "cpu_usage": task.cpu_usage,
            "memory_usage": task.memory_usage,
            "execution_time": task.execution_time
        }
    
    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任務"""
        with self._lock:
            return [
                {
                    "task_id": task.task_id,
                    "status": task.status.value,
                    "tool": task.tool.value,
                    "input_path": task.input_path,
                    "created_at": task.created_at.isoformat(),
                    "progress": task.progress,
                    "retry_count": task.retry_count,
                    "cancelled": task.cancelled,
                    "execution_time": task.execution_time
                }
                for task in self.tasks.values()
            ]
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """獲取服務統計資料"""
        with self._lock:
            total_tasks = len(self.tasks)
            status_counts = {}
            for status in ProcessingStatus:
                status_counts[status.value] = sum(
                    1 for task in self.tasks.values() if task.status == status
                )
            
            total_execution_time = sum(task.execution_time for task in self.tasks.values())
            completed_tasks = status_counts.get(ProcessingStatus.COMPLETED.value, 0)
            failed_tasks = status_counts.get(ProcessingStatus.FAILED.value, 0)
            
            return {
                "total_tasks": total_tasks,
                "status_counts": status_counts,
                "success_rate": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                "failure_rate": (failed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                "total_execution_time": total_execution_time,
                "average_execution_time": total_execution_time / total_tasks if total_tasks > 0 else 0,
                "active_task_count": self.active_task_count,
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "default_timeout": self.default_timeout,
                "rollback_enabled": self.enable_rollback
            }
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """清理已完成的舊任務"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._lock:
            tasks_to_remove = [
                task_id for task_id, task in self.tasks.items()
                if task.status in [
                    ProcessingStatus.COMPLETED, 
                    ProcessingStatus.FAILED,
                    ProcessingStatus.CANCELLED,
                    ProcessingStatus.TIMEOUT
                ]
                and task.completed_at
                and task.completed_at < cutoff_time
            ]
            
            for task_id in tasks_to_remove:
                # 取消超時任務
                self.timeout_manager.cancel_timeout(task_id)
                del self.tasks[task_id]
                logger.info(f"清理舊任務: {task_id}")
        
        return len(tasks_to_remove)


# 全域服務實例
_file_processing_service = None


def get_file_processing_service() -> FileProcessingService:
    """取得檔案處理服務實例（單例模式）"""
    global _file_processing_service
    if _file_processing_service is None:
        _file_processing_service = FileProcessingService()
    return _file_processing_service