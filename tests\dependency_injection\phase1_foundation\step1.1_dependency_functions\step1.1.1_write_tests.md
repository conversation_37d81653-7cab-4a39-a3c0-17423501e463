# Step 1.1.1: 為依賴函數編寫測試

## 📋 任務概述

**目標：** 在修正依賴函數之前，先編寫測試來定義預期行為  
**狀態：** ✅ 完成  
**完成時間：** 2025-08-02 00:11  
**執行者：** AI Assistant  

## 🎯 任務目標

根據 TDD (Test-Driven Development) 原則，在修正 `dependencies.py` 中的錯誤實現之前，先編寫測試來：

1. **定義正確的依賴函數行為**
2. **驗證當前實現的問題**
3. **為後續修正提供測試保護**

## 📁 創建的文件

### **1. 測試目錄結構**
```
tests/dependency_injection/
├── __init__.py                  # 測試套件初始化
├── conftest.py                  # 共用測試配置
├── test_dependencies.py         # 完整依賴函數測試
└── test_simple.py              # 簡化驗證測試
```

### **2. 主要測試文件**

#### **📄 test_dependencies.py (300行)**
- **MockFileStagingService** - Mock 暫存服務類別
- **MockFileProcessingService** - Mock 處理服務類別
- **TestRequireStagingService** - 暫存服務依賴函數測試
- **TestRequireProcessingService** - 處理服務依賴函數測試
- **TestDependencyFunctionBehavior** - 整體行為測試

#### **📄 conftest.py (150行)**
- **Fixtures** - 共用測試對象
- **測試輔助函數** - 簡化測試編寫
- **常數定義** - 測試用常數

#### **📄 test_simple.py (110行)**
- **簡化測試** - 不依賴 FastAPI TestClient
- **問題驗證** - 直接測試當前實現問題

## 🧪 測試覆蓋範圍

### **1. 核心功能測試**
```python
def test_require_staging_service_when_available():
    """測試：當暫存服務可用時，應該正確返回服務實例"""
    
def test_require_staging_service_when_unavailable():
    """測試：當暫存服務不可用時，應該拋出 HTTPException"""
    
def test_require_staging_service_with_initialization_error():
    """測試：當服務初始化有錯誤時，應該包含錯誤訊息"""
```

### **2. 行為驗證測試**
```python
def test_dependency_functions_are_callable():
    """測試：依賴函數應該是可調用的，而不是返回函數的函數"""
    
def test_dependency_functions_work_with_fastapi_depends():
    """測試：依賴函數應該能夠與 FastAPI 的 Depends() 正常工作"""
```

### **3. 錯誤處理測試**
- ✅ 服務不可用時拋出 503 錯誤
- ✅ 錯誤訊息包含詳細信息
- ✅ 初始化錯誤的正確傳播

## 🔍 發現的問題

### **當前實現問題：**
```python
# ❌ 當前錯誤實現
def require_staging_service():
    def _get_required_staging_service() -> FileStagingService:
        # ... 邏輯
        return service
    return _get_required_staging_service  # 返回函數！
```

### **測試驗證結果：**
```
require_staging_service() 返回的類型: <class 'function'>
是否為可調用對象: True
❌ 測試失敗：應該返回服務實例
⚠️  當前實現返回函數，需要額外調用
```

## ✅ 期望的正確行為

### **正確實現應該：**
```python
# ✅ 期望的正確實現
def require_staging_service() -> FileStagingService:
    service = get_staging_service()
    if service is None:
        raise HTTPException(status_code=503, detail="服務不可用")
    return service  # 直接返回服務實例
```

### **測試驗證標準：**
1. **服務可用時** → 直接返回服務實例
2. **服務不可用時** → 拋出 HTTPException(503)
3. **錯誤訊息** → 包含詳細的錯誤信息
4. **FastAPI 集成** → 與 Depends() 正常工作

## 🎯 測試執行結果

### **運行命令：**
```bash
python tests/dependency_injection/test_simple.py
```

### **輸出結果：**
```
🧪 測試當前依賴函數實現的問題
==================================================

1. 測試當前實現的問題:
require_staging_service() 返回的類型: <class 'function'>
是否為可調用對象: True

2. 測試期望的行為:
❌ 測試失敗：應該返回服務實例
⚠️  當前實現返回函數，需要額外調用

3. 測試服務不可用的情況:
當前實現返回函數，調用它來觸發異常...
✅ 正確拋出異常: HTTPException:

🎯 測試完成！
```

## 📊 完成檢查清單

- [x] 📝 創建測試目錄結構
- [x] 🧪 編寫完整的依賴函數測試
- [x] 🔍 編寫簡化驗證測試
- [x] ⚙️ 設置測試配置和 Fixtures
- [x] 🔴 驗證當前實現問題 (TDD Red 階段)
- [x] 📋 定義期望的正確行為
- [x] 📄 創建詳細文檔

## 🔗 相關文件

- **測試文件：** `tests/dependency_injection/test_dependencies.py`
- **簡化測試：** `tests/dependency_injection/test_simple.py`
- **配置文件：** `tests/dependency_injection/conftest.py`
- **目標文件：** `src/presentation/api/dependencies.py`

## ➡️ 下一步

**Step 1.1.2:** 根據測試修正 `require_staging_service()` 和 `require_processing_service()` 函數實現

---

**📝 文檔創建時間：** 2025-08-02 00:15  
**🔄 最後更新：** 2025-08-02 00:15
