"""
核心業務邏輯模組

包含監控系統的核心服務：
- 監控協調器 (MonitoringCoordinator)
- 資料收集服務 (DataCollectionService)
- 告警服務 (AlertService)
- 趨勢分析服務 (TrendAnalyzer)
"""

# Import core services with fallbacks for missing modules
try:
    from .dashboard_monitoring_coordinator import MonitoringCoordinator
except ImportError:
    MonitoringCoordinator = None

try:
    from .dashboard_data_collection_service import DataCollectionService
except ImportError:
    DataCollectionService = None

try:
    from .dashboard_alert_service import AlertService
except ImportError:
    AlertService = None

try:
    from .dashboard_trend_analyzer import TrendAnalyzer
except ImportError:
    TrendAnalyzer = None

# Import WebSocket manager (this exists)
try:
    from .dashboard_websocket_manager import (
        DashboardWebSocketService,
        DashboardWebSocketStats,
        get_websocket_service,
        initialize_websocket_service,
        shutdown_websocket_service
    )
    websocket_available = True
except ImportError:
    DashboardWebSocketService = None
    DashboardWebSocketStats = None
    get_websocket_service = None
    initialize_websocket_service = None
    shutdown_websocket_service = None
    websocket_available = False

__all__ = [
    "MonitoringCoordinator",
    "DataCollectionService", 
    "AlertService",
    "TrendAnalyzer",
    "DashboardWebSocketService",
    "DashboardWebSocketStats",
    "get_websocket_service",
    "initialize_websocket_service",
    "shutdown_websocket_service",
    "websocket_available"
]