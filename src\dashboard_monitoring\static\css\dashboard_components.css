/* 統一監控儀表板 - 元件樣式 */

/* 廠商統計元件 */
.vendor-stats {
    margin-bottom: var(--spacing-lg);
}

.vendor-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.vendor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.vendor-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.vendor-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.vendor-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.vendor-count {
    display: block;
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.vendor-success-rate {
    display: block;
    font-size: var(--font-xs);
    color: var(--success-color);
    margin-top: var(--spacing-xs);
}

/* Code Comparison 統計元件 */
.code-comparison-stats {
    margin-bottom: var(--spacing-lg);
}

.code-comparison-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.comparison-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-sm);
}

/* 任務類型統計元件 */
.task-type-stats {
    margin-bottom: var(--spacing-lg);
}

.task-type-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.task-type-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.task-type-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--secondary-color);
}

.task-type-item.code-comparison {
    border-left-color: var(--info-color);
}

.task-type-item.csv-to-summary {
    border-left-color: var(--success-color);
}

.task-type-item.compression {
    border-left-color: var(--warning-color);
}

.task-type-item.decompression {
    border-left-color: var(--error-color);
}

.task-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.task-type-name {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.task-type-status {
    font-size: var(--font-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background: var(--success-color);
    color: var(--text-light);
}

.task-type-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    text-align: center;
}

.task-metric {
    display: flex;
    flex-direction: column;
}

.task-metric-value {
    font-size: var(--font-md);
    font-weight: 700;
    color: var(--text-primary);
}

.task-metric-label {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    text-transform: uppercase;
}

/* 工作者狀態元件 */
.worker-stats {
    margin-bottom: var(--spacing-lg);
}

.worker-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.worker-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.worker-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.worker-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.worker-status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--success-color);
}

.worker-status-indicator.offline {
    background: var(--error-color);
}

.worker-status-indicator.busy {
    background: var(--warning-color);
}

.worker-name {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.worker-load {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

/* 資源圖表元件 */
.resource-charts {
    margin-bottom: var(--spacing-lg);
}

.chart-container {
    margin-bottom: var(--spacing-md);
}

.chart-container h4 {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.progress-bar {
    position: relative;
    height: 24px;
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--info-color));
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-color), #f1c40f);
}

.progress-fill.error {
    background: linear-gradient(90deg, var(--error-color), #e67e22);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 服務健康狀態元件 */
.service-health h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
}

.service-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
    transition: transform var(--transition-fast);
}

.service-item:hover {
    transform: translateY(-1px);
}

.service-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.service-status {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    font-weight: 600;
    background: var(--success-color);
    color: var(--text-light);
}

.service-status.warning {
    background: var(--warning-color);
}

.service-status.error {
    background: var(--error-color);
}

/* 檔案統計元件 */
.file-stats {
    display: grid;
    gap: var(--spacing-lg);
}

.file-type-stats h4, .storage-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.file-type-grid, .storage-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.file-type-item, .storage-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

.file-type-name, .storage-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.file-type-count, .storage-size {
    display: block;
    font-size: var(--font-md);
    font-weight: 700;
    color: var(--text-primary);
}

/* 告警元件 */
.alert-summary {
    margin-bottom: var(--spacing-lg);
}

.alert-count-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.alert-count-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    text-align: center;
    border-left: 4px solid var(--info-color);
}

.alert-count-item.critical {
    border-left-color: var(--critical-color);
}

.alert-count-item.error {
    border-left-color: var(--error-color);
}

.alert-count-item.warning {
    border-left-color: var(--warning-color);
}

.alert-count-item.info {
    border-left-color: var(--info-color);
}

.alert-count-item .count {
    display: block;
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.alert-count-item .label {
    display: block;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.alert-list {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    border-left: 4px solid var(--info-color);
    transition: transform var(--transition-fast);
}

.alert-item:hover {
    transform: translateX(2px);
}

.alert-item.critical {
    border-left-color: var(--critical-color);
    background: rgba(192, 57, 43, 0.05);
}

.alert-item.error {
    border-left-color: var(--error-color);
    background: rgba(231, 76, 60, 0.05);
}

.alert-item.warning {
    border-left-color: var(--warning-color);
    background: rgba(243, 156, 18, 0.05);
}

.alert-item.info {
    border-left-color: var(--info-color);
    background: rgba(23, 162, 184, 0.05);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.alert-title {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.alert-time {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

.alert-message {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

.alert-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.alert-action-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.alert-action-btn:hover {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.alert-action-btn.acknowledge {
    background: var(--success-color);
    color: var(--text-light);
    border-color: var(--success-color);
}

.alert-action-btn.dismiss {
    background: var(--text-secondary);
    color: var(--text-light);
    border-color: var(--text-secondary);
}

/* 響應式調整 */
@media (max-width: 768px) {
    .vendor-grid, .task-type-metrics, .service-grid, .file-type-grid, .storage-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .alert-count-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .comparison-metrics {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .worker-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .alert-header {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .alert-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .vendor-grid, .service-grid, .file-type-grid, .storage-grid, .alert-count-grid {
        grid-template-columns: 1fr;
    }
    
    .task-type-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .comparison-metrics {
        grid-template-columns: 1fr;
    }
    
    .progress-text {
        font-size: var(--font-xs);
    }
}

/* 動畫效果 */
.fade-in {
    animation: fadeIn var(--transition-normal) ease-in;
}

.slide-up {
    animation: slideUp var(--transition-normal) ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 滾動條樣式 */
.alert-list::-webkit-scrollbar {
    width: 6px;
}

.alert-list::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
}

.alert-list::-webkit-scrollbar-thumb {
    background: var(--text-secondary);
    border-radius: var(--border-radius-sm);
}

.alert-list::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}