# 主儀表板頁面使用指南

## 📋 概述

統一監控儀表板主頁面是一個全功能的即時監控介面，提供半導體郵件處理系統的全方位監控視圖。本指南詳細說明如何使用儀表板的各項功能。

**狀態**: ✅ **已實現並可用**

**訪問地址**: `http://localhost:8000/dashboard`

## 🎯 主要功能特色

### ✅ 響應式設計
- **桌面版**: 完整的多欄佈局，最佳化大螢幕體驗
- **平板版**: 自適應兩欄佈局，保持功能完整性
- **手機版**: 單欄垂直佈局，觸控友好的操作介面
- **深色模式**: 自動適應系統主題設定

### ✅ 即時資料更新
- **WebSocket 連接**: 5 秒內反映系統變化
- **自動重連**: 連接斷線時自動重新連接
- **心跳檢測**: 30 秒心跳間隔確保連接穩定
- **連接狀態指示**: 即時顯示 WebSocket 連接狀態

### ✅ 六大監控區域
1. **📧 郵件處理監控**
2. **🔄 Celery 任務監控**
3. **💻 系統資源監控**
4. **📁 檔案處理監控**
5. **📊 業務指標監控**
6. **🚨 告警管理**

## 🖥️ 介面佈局說明

### 頂部導航欄
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 統一監控儀表板    [系統狀態: 正常]    [最後更新: 12:34:56] │
│                                      [WebSocket: 已連接]    │
└─────────────────────────────────────────────────────────────┘
```

**功能說明**:
- **系統狀態指示器**: 顯示整體系統健康狀態
- **最後更新時間**: 顯示資料最後更新時間
- **WebSocket 連接狀態**: 即時連接狀態指示

### 告警通知橫幅
```
┌─────────────────────────────────────────────────────────────┐
│ ⚠️ 警告: 郵件佇列過多 - 待處理郵件數量超過 10 個        [×] │
└─────────────────────────────────────────────────────────────┘
```

**功能說明**:
- **自動顯示**: 有新告警時自動顯示
- **自動隱藏**: 一般告警 5 秒後自動隱藏
- **手動關閉**: 點擊 [×] 按鈕手動關閉

## 📊 概覽統計卡片

### 郵件處理概覽
```
┌─────────────────────────────────────┐
│ 📧 郵件處理              [正常]    │
├─────────────────────────────────────┤
│ 待處理: 10  處理中: 5              │
│ 已完成: 100 失敗: 2                │
└─────────────────────────────────────┘
```

**指標說明**:
- **待處理**: 等待處理的郵件數量
- **處理中**: 正在處理的郵件數量
- **已完成**: 已成功處理的郵件數量
- **失敗**: 處理失敗的郵件數量

### Celery 任務概覽
```
┌─────────────────────────────────────┐
│ 🔄 Celery 任務           [正常]    │
├─────────────────────────────────────┤
│ 活躍: 8    待處理: 15              │
│ 工作者: 3  成功率: 95%             │
└─────────────────────────────────────┘
```

**指標說明**:
- **活躍**: 正在執行的任務數量
- **待處理**: 佇列中等待執行的任務數量
- **工作者**: 線上工作者數量
- **成功率**: 任務執行成功率

### 系統資源概覽
```
┌─────────────────────────────────────┐
│ 💻 系統資源              [正常]    │
├─────────────────────────────────────┤
│ CPU: 45%   記憶體: 68%             │
│ 磁碟: 32%  連接數: 25              │
└─────────────────────────────────────┘
```

**指標說明**:
- **CPU**: CPU 使用率百分比
- **記憶體**: 記憶體使用率百分比
- **磁碟**: 磁碟使用率百分比
- **連接數**: 活躍網路連接數量

### 業務指標概覽
```
┌─────────────────────────────────────┐
│ 📊 業務指標              [正常]    │
├─────────────────────────────────────┤
│ 今日MO: 50  今日LOT: 150           │
│ 品質分數: 95 報告生成: 25          │
└─────────────────────────────────────┘
```

**指標說明**:
- **今日 MO**: 今日處理的 MO (Manufacturing Order) 數量
- **今日 LOT**: 今日處理的 LOT 數量
- **品質分數**: 資料品質評分 (0-100)
- **報告生成**: 今日生成的報告數量

## 🔍 詳細監控面板

### 1. 📧 郵件處理監控面板

#### 廠商分組統計
```
┌─────────────────────────────────────┐
│ 廠商分組統計                        │
├─────────────────────────────────────┤
│ GTK    JCET   ETD    LINGSEN  XAHT  │
│  5      3      2       1       0    │
│ 95%    88%    92%    100%     -     │
└─────────────────────────────────────┘
```

**功能說明**:
- **上排數字**: 各廠商待處理郵件數量
- **下排百分比**: 各廠商處理成功率

#### Code Comparison 任務統計
```
┌─────────────────────────────────────┐
│ Code Comparison 任務                │
├─────────────────────────────────────┤
│ 活躍任務: 2  待處理: 5  平均時間: 45s │
└─────────────────────────────────────┘
```

**控制按鈕**:
- **🔄 刷新**: 手動刷新郵件資料
- **⛶ 展開**: 展開/收縮面板

### 2. 🔄 Celery 任務監控面板

#### 任務類型統計
```
┌─────────────────────────────────────┐
│ Code Comparison              [運行中] │
│ 活躍:3  待處理:5  已完成:100  成功率:98% │
├─────────────────────────────────────┤
│ CSV to Summary               [空閒]  │
│ 活躍:1  待處理:2  已完成:50   成功率:96% │
├─────────────────────────────────────┤
│ Compression                  [空閒]  │
│ 活躍:0  待處理:1  已完成:30   成功率:100%│
└─────────────────────────────────────┘
```

#### 工作者狀態
```
┌─────────────────────────────────────┐
│ 工作者狀態                          │
├─────────────────────────────────────┤
│ ● worker1  負載: 3                  │
│ ● worker2  負載: 2                  │
│ ○ worker3  負載: 0 (離線)           │
└─────────────────────────────────────┘
```

**狀態指示**:
- **● 綠色**: 工作者線上
- **● 黃色**: 工作者忙碌
- **○ 紅色**: 工作者離線

### 3. 💻 系統資源監控面板

#### 資源使用率圖表
```
┌─────────────────────────────────────┐
│ CPU 使用率                          │
│ ████████████░░░░░░░░░░░░ 45%        │
├─────────────────────────────────────┤
│ 記憶體使用率                        │
│ ████████████████░░░░░░░░ 68%        │
├─────────────────────────────────────┤
│ 磁碟使用率                          │
│ ████████░░░░░░░░░░░░░░░░ 32%        │
└─────────────────────────────────────┘
```

**顏色編碼**:
- **綠色**: 正常使用率 (< 80%)
- **黃色**: 警告使用率 (80-95%)
- **紅色**: 危險使用率 (> 95%)

#### 服務健康狀態
```
┌─────────────────────────────────────┐
│ 服務健康狀態                        │
├─────────────────────────────────────┤
│ 郵件服務    [正常]  資料庫     [正常] │
│ Celery服務  [正常]  排程器     [警告] │
│ 檔案系統    [正常]                   │
└─────────────────────────────────────┘
```

### 4. 📁 檔案處理監控面板

#### 檔案類型統計
```
┌─────────────────────────────────────┐
│ 檔案類型統計                        │
├─────────────────────────────────────┤
│ CSV: 10   EXCEL: 5   ZIP: 3         │
└─────────────────────────────────────┘
```

#### 儲存空間使用
```
┌─────────────────────────────────────┐
│ 儲存空間使用                        │
├─────────────────────────────────────┤
│ 暫存資料夾: 1.2 GB                  │
│ 上傳資料夾: 500 MB                  │
│ 處理完成: 2.1 GB                    │
└─────────────────────────────────────┘
```

### 5. 🚨 告警管理面板

#### 告警統計
```
┌─────────────────────────────────────┐
│ 嚴重: 0   錯誤: 1   警告: 3   資訊: 2 │
└─────────────────────────────────────┘
```

#### 告警列表
```
┌─────────────────────────────────────┐
│ ⚠️ 警告 - 郵件佇列過多        12:34  │
│ 待處理郵件數量超過 10 個             │
│ [確認] [忽略]                       │
├─────────────────────────────────────┤
│ ℹ️ 資訊 - 系統維護通知        12:30  │
│ 系統將於今晚進行例行維護             │
│ [確認] [忽略]                       │
└─────────────────────────────────────┘
```

**告警操作**:
- **確認**: 標記告警為已確認狀態
- **忽略**: 從列表中移除告警

## 🎮 互動功能

### 刷新功能
- **全域刷新**: 點擊任何面板的 🔄 按鈕刷新所有資料
- **自動刷新**: WebSocket 連接時自動更新資料
- **手動刷新**: WebSocket 斷線時可手動刷新

### 面板控制
- **展開/收縮**: 點擊 ⛶ 按鈕展開或收縮面板
- **響應式調整**: 面板會根據螢幕大小自動調整佈局

### 告警管理
- **確認告警**: 點擊 [確認] 按鈕確認告警
- **忽略告警**: 點擊 [忽略] 按鈕移除告警
- **清除全部**: 點擊 [清除全部] 按鈕清除所有告警

## 🔧 技術特性

### WebSocket 整合
- **即時連接**: 頁面載入時自動建立 WebSocket 連接
- **自動重連**: 連接斷線時自動嘗試重新連接（最多 5 次）
- **心跳檢測**: 每 30 秒發送心跳確保連接穩定
- **錯誤處理**: 完整的錯誤處理和降級機制

### 效能最佳化
- **動畫效果**: 使用 CSS3 動畫和 JavaScript requestAnimationFrame
- **資料快取**: 前端資料快取減少不必要的更新
- **增量更新**: 只更新變化的資料，避免全頁面重繪
- **虛擬滾動**: 大量資料時使用虛擬滾動提升效能

### 可訪問性支援
- **鍵盤導航**: 支援 Tab 鍵導航
- **螢幕閱讀器**: 完整的 ARIA 標籤支援
- **高對比度**: 支援高對比度模式
- **字體縮放**: 支援瀏覽器字體縮放

## 🎨 主題和樣式

### 深色模式
系統會自動檢測使用者的系統主題設定：
- **淺色模式**: 白色背景，深色文字
- **深色模式**: 深色背景，淺色文字
- **自動切換**: 跟隨系統主題自動切換

### 響應式斷點
- **桌面**: ≥ 1200px - 4 欄網格佈局
- **平板**: 768px - 1199px - 2 欄網格佈局
- **手機**: < 768px - 1 欄垂直佈局

### 列印支援
- **列印樣式**: 專門的列印 CSS 樣式
- **版面最佳化**: 列印時隱藏互動元素
- **分頁控制**: 避免內容跨頁分割

## 🚀 使用最佳實踐

### 1. 監控策略
- **定期檢查**: 建議每小時檢查一次儀表板
- **告警回應**: 及時回應嚴重和錯誤級別的告警
- **趨勢觀察**: 關注系統資源使用趨勢

### 2. 效能考量
- **保持連接**: 盡量保持 WebSocket 連接以獲得即時更新
- **適度刷新**: 避免頻繁手動刷新，信任自動更新機制
- **面板管理**: 收縮不常用的面板以提升頁面效能

### 3. 故障排除
- **連接問題**: 檢查網路連接和服務器狀態
- **資料異常**: 使用手動刷新功能重新載入資料
- **瀏覽器相容**: 建議使用現代瀏覽器（Chrome, Firefox, Safari, Edge）

## 🔍 故障排除

### 常見問題

#### WebSocket 連接失敗
**症狀**: 連接狀態顯示「已斷線」或「連接失敗」
**解決方案**:
1. 檢查網路連接
2. 確認服務器運行狀態
3. 重新整理頁面
4. 檢查防火牆設定

#### 資料不更新
**症狀**: 指標數據長時間不變化
**解決方案**:
1. 檢查 WebSocket 連接狀態
2. 點擊刷新按鈕手動更新
3. 檢查後端服務狀態
4. 查看瀏覽器控制台錯誤訊息

#### 頁面載入緩慢
**症狀**: 頁面載入時間過長
**解決方案**:
1. 清除瀏覽器快取
2. 檢查網路頻寬
3. 關閉不必要的瀏覽器分頁
4. 使用較新版本的瀏覽器

#### 響應式佈局問題
**症狀**: 在某些裝置上佈局異常
**解決方案**:
1. 重新整理頁面
2. 檢查瀏覽器縮放設定
3. 嘗試不同的瀏覽器
4. 清除瀏覽器快取和 Cookie

## 📱 行動裝置使用

### 手機瀏覽
- **垂直佈局**: 所有面板垂直排列
- **觸控友好**: 按鈕和連結適合觸控操作
- **滑動支援**: 支援滑動操作
- **縮放適應**: 自動適應螢幕大小

### 平板瀏覽
- **混合佈局**: 2 欄網格佈局
- **觸控和滑鼠**: 同時支援觸控和滑鼠操作
- **橫豎屏**: 支援橫豎屏自動調整

## 🔒 安全考量

### 資料保護
- **敏感資料過濾**: 自動過濾敏感業務資料
- **訪問控制**: 基於角色的訪問權限
- **審計日誌**: 記錄所有操作活動

### 網路安全
- **HTTPS 支援**: 支援 HTTPS 加密傳輸
- **WSS 支援**: WebSocket 安全連接
- **CSRF 保護**: 跨站請求偽造保護

## 📞 技術支援

如遇到問題或需要協助，請：
1. 查看瀏覽器控制台錯誤訊息
2. 檢查網路連接狀態
3. 聯絡系統管理員
4. 提供詳細的錯誤描述和重現步驟

---

**版本**: v1.0.0  
**最後更新**: 2024-01-01  
**相容瀏覽器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+