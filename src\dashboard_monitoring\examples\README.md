# 統一監控儀表板使用範例

## 📋 概述

本目錄包含統一監控儀表板各個元件的使用範例，幫助開發者快速了解和使用監控系統的各項功能。

## 📁 範例文件

### Celery 監控收集器範例

#### [celery_collector_example.py](./celery_collector_example.py)
完整的 Celery 監控收集器使用範例，包含：

- **基本使用範例** - 展示如何創建收集器並收集指標
- **健康檢查範例** - 檢查收集器和 Celery 服務的健康狀態
- **持續監控範例** - 演示持續收集指標的方法
- **錯誤處理範例** - 展示錯誤隔離和恢復機制
- **配置範例** - 自定義配置的使用方法
- **JSON 匯出範例** - 將指標資料匯出為 JSON 格式
- **效能測試範例** - 測試收集器的效能表現

**運行方式：**
```bash
python src/dashboard_monitoring/examples/celery_collector_example.py
```

**預期輸出：**
- 顯示各種使用場景的執行結果
- 展示指標收集的詳細資訊
- 演示錯誤處理和恢復機制
- 提供效能測試結果

## 🚀 快速開始

### 1. 環境準備

確保已安裝必要的依賴：
```bash
pip install -r requirements.txt
```

### 2. 運行範例

選擇想要運行的範例文件：
```bash
# Celery 收集器範例
python src/dashboard_monitoring/examples/celery_collector_example.py
```

### 3. 查看結果

範例會輸出詳細的執行過程和結果，包括：
- 指標收集結果
- 健康狀態檢查
- 效能測試數據
- 錯誤處理演示

## 📊 範例特色

### 實用性
- 所有範例都基於真實的使用場景
- 包含完整的錯誤處理和邊界情況
- 提供詳細的輸出和說明

### 教育性
- 每個範例都有清晰的註釋和說明
- 展示最佳實踐和常見模式
- 包含效能考量和最佳化建議

### 可擴展性
- 範例代碼易於修改和擴展
- 提供了自定義配置的方法
- 支援不同的使用場景

## 🔧 自定義範例

### 創建新範例

1. **複製現有範例**：
   ```bash
   cp celery_collector_example.py my_custom_example.py
   ```

2. **修改導入路徑**：
   ```python
   import sys
   import os
   sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
   ```

3. **實現自定義邏輯**：
   ```python
   async def my_custom_example():
       """自定義範例"""
       collector = get_celery_collector()
       # 添加自定義邏輯
   ```

### 範例模板

```python
"""
自定義監控範例模板
"""

import asyncio
import sys
import os

# 添加路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.dashboard_monitoring.collectors.dashboard_celery_collector import get_celery_collector

async def my_custom_example():
    """自定義範例實現"""
    print("🎯 自定義監控範例")
    print("=" * 50)
    
    collector = get_celery_collector()
    
    # 實現自定義邏輯
    metrics = await collector.collect_metrics()
    print(f"收集到的指標: {metrics}")

async def main():
    """主函數"""
    try:
        await my_custom_example()
        print("✅ 範例執行完成")
    except Exception as e:
        print(f"❌ 執行失敗: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 📝 範例說明

### Celery 收集器範例詳解

#### 基本使用
```python
# 創建收集器
collector = DashboardCeleryCollector()

# 收集指標
metrics = await collector.collect_metrics()

# 查看結果
print(f"活躍任務: {metrics.total_active}")
print(f"待處理任務: {metrics.total_pending}")
```

#### 健康檢查
```python
# 檢查收集器健康狀態
health_status = await collector.get_health_status()

# 判斷狀態
if health_status['status'] == 'healthy':
    print("✅ 收集器運行正常")
else:
    print(f"❌ 收集器狀態異常: {health_status['status']}")
```

#### 持續監控
```python
# 持續監控循環
while True:
    metrics = await collector.collect_metrics()
    
    # 檢查告警條件
    if metrics.total_pending > 20:
        print("⚠️ 待處理任務過多")
    
    # 等待下次收集
    await asyncio.sleep(30)
```

## 🧪 測試範例

### 運行測試
```bash
# 測試 Celery 收集器範例
python -m pytest tests/unit/test_dashboard_celery_collector.py -v

# 測試整合功能
python -m pytest tests/integration/test_dashboard_celery_integration.py -v
```

### 效能測試
範例中包含效能測試，會測量：
- 指標收集時間
- 記憶體使用情況
- 並發處理能力
- 錯誤恢復時間

## 🔗 相關資源

### 文檔連結
- [Celery 收集器指南](../docs/celery_collector_guide.md)
- [API 文檔](../docs/api_documentation.md)
- [統一監控儀表板 README](../README.md)

### 程式碼參考
- [Celery 收集器實現](../collectors/dashboard_celery_collector.py)
- [資料模型定義](../models/dashboard_metrics_models.py)
- [配置系統](../config/dashboard_config.py)

### 測試文件
- [單元測試](../../tests/unit/test_dashboard_celery_collector.py)
- [整合測試](../../tests/integration/test_dashboard_celery_integration.py)

## 🐛 故障排除

### 常見問題

#### 1. 導入錯誤
**問題**: `ModuleNotFoundError: No module named 'src'`
**解決方案**: 確保正確設定了 Python 路徑，或從專案根目錄運行範例

#### 2. Celery 連接失敗
**問題**: 範例顯示 "Celery 連接測試失敗"
**解決方案**: 
- 檢查 Celery 服務是否運行
- 確認配置文件 `celeryconfig.py` 正確
- 在開發模式下這是正常現象

#### 3. 權限錯誤
**問題**: 無法寫入 JSON 文件
**解決方案**: 確保有寫入當前目錄的權限

### 調試技巧

1. **啟用詳細日誌**：
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **檢查配置**：
   ```python
   config = DashboardConfig()
   print(config.to_dict())
   ```

3. **測試連接**：
   ```python
   collector = get_celery_collector()
   health = await collector.get_health_status()
   print(health)
   ```

## 📞 支援

如果在使用範例時遇到問題：

1. 檢查本文檔的故障排除章節
2. 查看相關的測試文件
3. 參考完整的 API 文檔
4. 聯絡開發團隊獲取協助

---

**版本**: 1.0.0  
**最後更新**: 2025-08-02  
**維護者**: 統一監控儀表板開發團隊