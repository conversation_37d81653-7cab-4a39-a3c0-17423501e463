"""
統一監控儀表板快取配置
管理快取服務的配置參數和策略
"""

import os
from dataclasses import dataclass
from typing import Dict, Optional
from enum import Enum


class CacheStrategy(Enum):
    """快取策略"""
    LRU = "lru"  # 最少使用
    LFU = "lfu"  # 最少頻率
    FIFO = "fifo"  # 先進先出
    TTL_ONLY = "ttl_only"  # 僅基於 TTL


@dataclass
class CacheConfig:
    """快取配置"""
    
    # 基本配置
    max_size: int = 1000
    default_ttl: int = 300  # 5分鐘
    cleanup_interval: int = 60  # 1分鐘
    max_memory_mb: int = 256
    
    # 策略配置
    eviction_strategy: CacheStrategy = CacheStrategy.LRU
    memory_pressure_threshold: float = 0.8  # 80%
    
    # 不同類型資料的 TTL 配置
    ttl_by_type: Dict[str, int] = None
    
    # 效能配置
    enable_statistics: bool = True
    enable_memory_monitoring: bool = True
    statistics_update_interval: int = 30
    
    # 調試配置
    enable_debug_logging: bool = False
    log_cache_operations: bool = False
    
    def __post_init__(self):
        """初始化後處理"""
        if self.ttl_by_type is None:
            self.ttl_by_type = {
                # 監控指標 - 短期快取
                'email_metrics': 30,      # 30秒
                'celery_metrics': 30,     # 30秒
                'system_metrics': 60,     # 1分鐘
                'file_metrics': 60,       # 1分鐘
                
                # 業務資料 - 中期快取
                'business_metrics': 300,  # 5分鐘
                'vendor_stats': 300,      # 5分鐘
                'task_history': 600,      # 10分鐘
                
                # 配置資料 - 長期快取
                'alert_rules': 1800,      # 30分鐘
                'dashboard_config': 3600, # 1小時
                'user_preferences': 3600, # 1小時
                
                # 趨勢資料 - 長期快取
                'trend_data': 1800,       # 30分鐘
                'historical_data': 3600,  # 1小時
                
                # 靜態資料 - 超長期快取
                'vendor_mapping': 7200,   # 2小時
                'system_info': 7200,      # 2小時
            }
    
    @classmethod
    def from_env(cls) -> 'CacheConfig':
        """從環境變數創建配置"""
        return cls(
            max_size=int(os.getenv('DASHBOARD_CACHE_MAX_SIZE', '1000')),
            default_ttl=int(os.getenv('DASHBOARD_CACHE_DEFAULT_TTL', '300')),
            cleanup_interval=int(os.getenv('DASHBOARD_CACHE_CLEANUP_INTERVAL', '60')),
            max_memory_mb=int(os.getenv('DASHBOARD_CACHE_MAX_MEMORY_MB', '256')),
            memory_pressure_threshold=float(os.getenv('DASHBOARD_CACHE_MEMORY_THRESHOLD', '0.8')),
            enable_statistics=os.getenv('DASHBOARD_CACHE_ENABLE_STATS', 'true').lower() == 'true',
            enable_memory_monitoring=os.getenv('DASHBOARD_CACHE_ENABLE_MEMORY_MONITORING', 'true').lower() == 'true',
            enable_debug_logging=os.getenv('DASHBOARD_CACHE_DEBUG', 'false').lower() == 'true',
            log_cache_operations=os.getenv('DASHBOARD_CACHE_LOG_OPERATIONS', 'false').lower() == 'true'
        )
    
    def get_ttl_for_type(self, data_type: str) -> int:
        """獲取特定類型的 TTL"""
        return self.ttl_by_type.get(data_type, self.default_ttl)
    
    def validate(self) -> bool:
        """驗證配置"""
        if self.max_size <= 0:
            raise ValueError("max_size 必須大於 0")
        
        if self.default_ttl < 0:
            raise ValueError("default_ttl 不能為負數")
        
        if self.cleanup_interval <= 0:
            raise ValueError("cleanup_interval 必須大於 0")
        
        if self.max_memory_mb <= 0:
            raise ValueError("max_memory_mb 必須大於 0")
        
        if not 0 < self.memory_pressure_threshold <= 1:
            raise ValueError("memory_pressure_threshold 必須在 0 到 1 之間")
        
        return True
    
    def to_dict(self) -> Dict:
        """轉換為字典"""
        return {
            'max_size': self.max_size,
            'default_ttl': self.default_ttl,
            'cleanup_interval': self.cleanup_interval,
            'max_memory_mb': self.max_memory_mb,
            'eviction_strategy': self.eviction_strategy.value,
            'memory_pressure_threshold': self.memory_pressure_threshold,
            'ttl_by_type': self.ttl_by_type,
            'enable_statistics': self.enable_statistics,
            'enable_memory_monitoring': self.enable_memory_monitoring,
            'statistics_update_interval': self.statistics_update_interval,
            'enable_debug_logging': self.enable_debug_logging,
            'log_cache_operations': self.log_cache_operations
        }


class CacheKeyBuilder:
    """快取鍵建構器"""
    
    @staticmethod
    def build_metrics_key(metric_type: str, timestamp: Optional[str] = None) -> str:
        """建構監控指標快取鍵"""
        if timestamp:
            return f"metrics:{metric_type}:{timestamp}"
        return f"metrics:{metric_type}:current"
    
    @staticmethod
    def build_alert_key(alert_type: str, alert_id: Optional[str] = None) -> str:
        """建構告警快取鍵"""
        if alert_id:
            return f"alert:{alert_type}:{alert_id}"
        return f"alerts:{alert_type}:active"
    
    @staticmethod
    def build_trend_key(metric_type: str, time_range: str) -> str:
        """建構趨勢資料快取鍵"""
        return f"trend:{metric_type}:{time_range}"
    
    @staticmethod
    def build_business_key(business_type: str, date: Optional[str] = None) -> str:
        """建構業務資料快取鍵"""
        if date:
            return f"business:{business_type}:{date}"
        return f"business:{business_type}:today"
    
    @staticmethod
    def build_system_key(system_component: str) -> str:
        """建構系統資料快取鍵"""
        return f"system:{system_component}"
    
    @staticmethod
    def build_config_key(config_type: str) -> str:
        """建構配置資料快取鍵"""
        return f"config:{config_type}"
    
    @staticmethod
    def build_user_key(user_id: str, data_type: str) -> str:
        """建構使用者資料快取鍵"""
        return f"user:{user_id}:{data_type}"


class CacheNamespace:
    """快取命名空間"""
    
    # 監控指標
    METRICS = "metrics"
    EMAIL_METRICS = "email_metrics"
    CELERY_METRICS = "celery_metrics"
    SYSTEM_METRICS = "system_metrics"
    FILE_METRICS = "file_metrics"
    BUSINESS_METRICS = "business_metrics"
    
    # 告警
    ALERTS = "alerts"
    ALERT_RULES = "alert_rules"
    ALERT_HISTORY = "alert_history"
    
    # 趨勢分析
    TRENDS = "trends"
    HISTORICAL_DATA = "historical_data"
    PREDICTIONS = "predictions"
    
    # 系統資訊
    SYSTEM_INFO = "system_info"
    SERVICE_STATUS = "service_status"
    HEALTH_CHECKS = "health_checks"
    
    # 配置
    CONFIG = "config"
    USER_PREFERENCES = "user_preferences"
    DASHBOARD_SETTINGS = "dashboard_settings"
    
    # 業務資料
    BUSINESS = "business"
    VENDOR_STATS = "vendor_stats"
    MO_LOT_DATA = "mo_lot_data"
    
    # 任務資料
    TASKS = "tasks"
    TASK_HISTORY = "task_history"
    TASK_STATISTICS = "task_statistics"


def get_default_cache_config() -> CacheConfig:
    """獲取預設快取配置"""
    return CacheConfig.from_env()


def create_cache_config(
    max_size: Optional[int] = None,
    default_ttl: Optional[int] = None,
    max_memory_mb: Optional[int] = None,
    **kwargs
) -> CacheConfig:
    """創建自訂快取配置"""
    config = get_default_cache_config()
    
    if max_size is not None:
        config.max_size = max_size
    
    if default_ttl is not None:
        config.default_ttl = default_ttl
    
    if max_memory_mb is not None:
        config.max_memory_mb = max_memory_mb
    
    # 更新其他參數
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    config.validate()
    return config