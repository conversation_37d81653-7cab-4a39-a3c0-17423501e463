"""
統一監控儀表板快取管理器
提供高階快取管理功能，整合監控系統
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Union
import json
import pickle
from dataclasses import asdict

from .dashboard_cache_service import DashboardCacheService, get_cache_service
from ..config.dashboard_cache_config import (
    CacheConfig, CacheKeyBuilder, CacheNamespace, get_default_cache_config
)
from ..models.dashboard_metrics_models import (
    DashboardMetrics, EmailMetrics, CeleryMetrics, SystemMetrics, FileMetrics, BusinessMetrics
)


class DashboardCacheManager:
    """統一監控儀表板快取管理器"""
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """
        初始化快取管理器
        
        Args:
            config: 快取配置，如果為 None 則使用預設配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or get_default_cache_config()
        
        # 初始化快取服務
        self.cache_service = get_cache_service(
            max_size=self.config.max_size,
            default_ttl=self.config.default_ttl,
            cleanup_interval=self.config.cleanup_interval,
            max_memory_mb=self.config.max_memory_mb
        )
        
        # 快取鍵建構器
        self.key_builder = CacheKeyBuilder()
        
        # 序列化器
        self._serializers = {
            'json': self._json_serializer,
            'pickle': self._pickle_serializer,
            'raw': self._raw_serializer
        }
        
        self.logger.info("快取管理器已初始化")
    
    async def start(self) -> None:
        """啟動快取管理器"""
        await self.cache_service.start()
        self.logger.info("快取管理器已啟動")
    
    async def stop(self) -> None:
        """停止快取管理器"""
        await self.cache_service.stop()
        self.logger.info("快取管理器已停止")
    
    # === 監控指標快取 ===
    
    async def cache_email_metrics(self, metrics: EmailMetrics, ttl: Optional[int] = None) -> bool:
        """快取郵件監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.EMAIL_METRICS)
        ttl = ttl or self.config.get_ttl_for_type('email_metrics')
        
        return self.cache_service.set(key, metrics, ttl)
    
    async def get_email_metrics(self) -> Optional[EmailMetrics]:
        """獲取快取的郵件監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.EMAIL_METRICS)
        return self.cache_service.get(key)
    
    async def cache_celery_metrics(self, metrics: CeleryMetrics, ttl: Optional[int] = None) -> bool:
        """快取 Celery 監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.CELERY_METRICS)
        ttl = ttl or self.config.get_ttl_for_type('celery_metrics')
        
        return self.cache_service.set(key, metrics, ttl)
    
    async def get_celery_metrics(self) -> Optional[CeleryMetrics]:
        """獲取快取的 Celery 監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.CELERY_METRICS)
        return self.cache_service.get(key)
    
    async def cache_system_metrics(self, metrics: SystemMetrics, ttl: Optional[int] = None) -> bool:
        """快取系統監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.SYSTEM_METRICS)
        ttl = ttl or self.config.get_ttl_for_type('system_metrics')
        
        return self.cache_service.set(key, metrics, ttl)
    
    async def get_system_metrics(self) -> Optional[SystemMetrics]:
        """獲取快取的系統監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.SYSTEM_METRICS)
        return self.cache_service.get(key)
    
    async def cache_file_metrics(self, metrics: FileMetrics, ttl: Optional[int] = None) -> bool:
        """快取檔案監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.FILE_METRICS)
        ttl = ttl or self.config.get_ttl_for_type('file_metrics')
        
        return self.cache_service.set(key, metrics, ttl)
    
    async def get_file_metrics(self) -> Optional[FileMetrics]:
        """獲取快取的檔案監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.FILE_METRICS)
        return self.cache_service.get(key)
    
    async def cache_business_metrics(self, metrics: BusinessMetrics, ttl: Optional[int] = None) -> bool:
        """快取業務監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.BUSINESS_METRICS)
        ttl = ttl or self.config.get_ttl_for_type('business_metrics')
        
        return self.cache_service.set(key, metrics, ttl)
    
    async def get_business_metrics(self) -> Optional[BusinessMetrics]:
        """獲取快取的業務監控指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.BUSINESS_METRICS)
        return self.cache_service.get(key)
    
    async def cache_dashboard_metrics(self, metrics: DashboardMetrics, ttl: Optional[int] = None) -> bool:
        """快取完整的儀表板指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.METRICS)
        ttl = ttl or self.config.get_ttl_for_type('dashboard_metrics')
        
        # 將 dataclass 轉換為字典以便序列化
        metrics_dict = asdict(metrics)
        return self.cache_service.set(key, metrics_dict, ttl)
    
    async def get_dashboard_metrics(self) -> Optional[Dict[str, Any]]:
        """獲取快取的完整儀表板指標"""
        key = self.key_builder.build_metrics_key(CacheNamespace.METRICS)
        return self.cache_service.get(key)
    
    # === 告警快取 ===
    
    async def cache_active_alerts(self, alerts: List[Dict], ttl: Optional[int] = None) -> bool:
        """快取活躍告警"""
        key = self.key_builder.build_alert_key("active")
        ttl = ttl or self.config.get_ttl_for_type('alerts')
        
        return self.cache_service.set(key, alerts, ttl)
    
    async def get_active_alerts(self) -> Optional[List[Dict]]:
        """獲取快取的活躍告警"""
        key = self.key_builder.build_alert_key("active")
        return self.cache_service.get(key)
    
    async def cache_alert_rules(self, rules: List[Dict], ttl: Optional[int] = None) -> bool:
        """快取告警規則"""
        key = self.key_builder.build_config_key("alert_rules")
        ttl = ttl or self.config.get_ttl_for_type('alert_rules')
        
        return self.cache_service.set(key, rules, ttl)
    
    async def get_alert_rules(self) -> Optional[List[Dict]]:
        """獲取快取的告警規則"""
        key = self.key_builder.build_config_key("alert_rules")
        return self.cache_service.get(key)
    
    # === 趨勢資料快取 ===
    
    async def cache_trend_data(self, metric_type: str, time_range: str, 
                              data: List[Dict], ttl: Optional[int] = None) -> bool:
        """快取趨勢資料"""
        key = self.key_builder.build_trend_key(metric_type, time_range)
        ttl = ttl or self.config.get_ttl_for_type('trend_data')
        
        return self.cache_service.set(key, data, ttl)
    
    async def get_trend_data(self, metric_type: str, time_range: str) -> Optional[List[Dict]]:
        """獲取快取的趨勢資料"""
        key = self.key_builder.build_trend_key(metric_type, time_range)
        return self.cache_service.get(key)
    
    # === 業務資料快取 ===
    
    async def cache_vendor_stats(self, stats: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """快取廠商統計資料"""
        key = self.key_builder.build_business_key("vendor_stats")
        ttl = ttl or self.config.get_ttl_for_type('vendor_stats')
        
        return self.cache_service.set(key, stats, ttl)
    
    async def get_vendor_stats(self) -> Optional[Dict[str, Any]]:
        """獲取快取的廠商統計資料"""
        key = self.key_builder.build_business_key("vendor_stats")
        return self.cache_service.get(key)
    
    async def cache_mo_lot_data(self, date: str, data: Dict[str, Any], 
                               ttl: Optional[int] = None) -> bool:
        """快取 MO/LOT 資料"""
        key = self.key_builder.build_business_key("mo_lot_data", date)
        ttl = ttl or self.config.get_ttl_for_type('mo_lot_data')
        
        return self.cache_service.set(key, data, ttl)
    
    async def get_mo_lot_data(self, date: str) -> Optional[Dict[str, Any]]:
        """獲取快取的 MO/LOT 資料"""
        key = self.key_builder.build_business_key("mo_lot_data", date)
        return self.cache_service.get(key)
    
    # === 系統資訊快取 ===
    
    async def cache_system_info(self, info: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """快取系統資訊"""
        key = self.key_builder.build_system_key("info")
        ttl = ttl or self.config.get_ttl_for_type('system_info')
        
        return self.cache_service.set(key, info, ttl)
    
    async def get_system_info(self) -> Optional[Dict[str, Any]]:
        """獲取快取的系統資訊"""
        key = self.key_builder.build_system_key("info")
        return self.cache_service.get(key)
    
    async def cache_service_status(self, status: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """快取服務狀態"""
        key = self.key_builder.build_system_key("service_status")
        ttl = ttl or self.config.get_ttl_for_type('service_status')
        
        return self.cache_service.set(key, status, ttl)
    
    async def get_service_status(self) -> Optional[Dict[str, Any]]:
        """獲取快取的服務狀態"""
        key = self.key_builder.build_system_key("service_status")
        return self.cache_service.get(key)
    
    # === 通用快取操作 ===
    
    async def cache_with_callback(self, key: str, callback: Callable, 
                                 ttl: Optional[int] = None, 
                                 force_refresh: bool = False) -> Any:
        """使用回調函數的快取操作"""
        if not force_refresh:
            cached_value = self.cache_service.get(key)
            if cached_value is not None:
                return cached_value
        
        # 執行回調函數獲取資料
        try:
            if asyncio.iscoroutinefunction(callback):
                value = await callback()
            else:
                value = callback()
            
            # 快取結果
            self.cache_service.set(key, value, ttl)
            return value
            
        except Exception as e:
            self.logger.error(f"快取回調執行失敗 {key}: {e}")
            # 如果有舊的快取資料，返回舊資料
            return self.cache_service.get(key)
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """根據模式失效快取"""
        keys = self.cache_service.get_keys(pattern)
        count = 0
        
        for key in keys:
            if self.cache_service.delete(key):
                count += 1
        
        self.logger.info(f"失效快取模式 '{pattern}': {count} 個項目")
        return count
    
    async def invalidate_namespace(self, namespace: str) -> int:
        """失效整個命名空間的快取"""
        pattern = f"{namespace}:*"
        return await self.invalidate_pattern(pattern)
    
    async def warm_up_cache(self) -> None:
        """預熱快取"""
        self.logger.info("開始快取預熱")
        
        try:
            # 這裡可以添加預熱邏輯
            # 例如：預載入常用的配置資料、系統資訊等
            
            # 預載入系統資訊
            system_info = {
                'startup_time': datetime.now().isoformat(),
                'cache_config': self.config.to_dict()
            }
            await self.cache_system_info(system_info)
            
            self.logger.info("快取預熱完成")
            
        except Exception as e:
            self.logger.error(f"快取預熱失敗: {e}")
    
    # === 快取統計和監控 ===
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """獲取快取統計資料"""
        return self.cache_service.get_cache_info()
    
    def get_cache_health(self) -> Dict[str, Any]:
        """獲取快取健康狀態"""
        stats = self.cache_service.get_statistics()
        
        # 計算健康分數
        health_score = 100
        
        # 命中率影響
        if stats.hit_rate < 0.5:  # 命中率低於 50%
            health_score -= 20
        elif stats.hit_rate < 0.7:  # 命中率低於 70%
            health_score -= 10
        
        # 記憶體使用影響
        if stats.memory_usage_mb > self.config.max_memory_mb * 0.9:  # 超過 90%
            health_score -= 30
        elif stats.memory_usage_mb > self.config.max_memory_mb * 0.8:  # 超過 80%
            health_score -= 15
        
        # 平均存取時間影響
        if stats.avg_access_time_ms > 10:  # 超過 10ms
            health_score -= 10
        elif stats.avg_access_time_ms > 5:  # 超過 5ms
            health_score -= 5
        
        # 確定健康狀態
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 70:
            status = "good"
        elif health_score >= 50:
            status = "fair"
        else:
            status = "poor"
        
        return {
            'status': status,
            'health_score': max(0, health_score),
            'statistics': asdict(stats),
            'recommendations': self._get_health_recommendations(stats)
        }
    
    def _get_health_recommendations(self, stats) -> List[str]:
        """獲取健康建議"""
        recommendations = []
        
        if stats.hit_rate < 0.5:
            recommendations.append("考慮增加快取 TTL 或檢查快取策略")
        
        if stats.memory_usage_mb > self.config.max_memory_mb * 0.8:
            recommendations.append("考慮增加最大記憶體限制或清理不必要的快取")
        
        if stats.avg_access_time_ms > 5:
            recommendations.append("檢查快取項目大小，考慮優化資料結構")
        
        if stats.eviction_count > stats.expired_count * 2:
            recommendations.append("考慮增加快取大小限制或調整 TTL 策略")
        
        return recommendations
    
    # === 序列化器 ===
    
    def _json_serializer(self, data: Any) -> str:
        """JSON 序列化器"""
        return json.dumps(data, default=str, ensure_ascii=False)
    
    def _pickle_serializer(self, data: Any) -> bytes:
        """Pickle 序列化器"""
        return pickle.dumps(data)
    
    def _raw_serializer(self, data: Any) -> Any:
        """原始序列化器（不序列化）"""
        return data


# 全域快取管理器實例
_cache_manager_instance: Optional[DashboardCacheManager] = None


def get_cache_manager(config: Optional[CacheConfig] = None) -> DashboardCacheManager:
    """獲取快取管理器單例"""
    global _cache_manager_instance
    
    if _cache_manager_instance is None:
        _cache_manager_instance = DashboardCacheManager(config)
    
    return _cache_manager_instance


async def initialize_cache_manager(config: Optional[CacheConfig] = None) -> DashboardCacheManager:
    """初始化並啟動快取管理器"""
    cache_manager = get_cache_manager(config)
    await cache_manager.start()
    await cache_manager.warm_up_cache()
    return cache_manager


async def shutdown_cache_manager() -> None:
    """關閉快取管理器"""
    global _cache_manager_instance
    
    if _cache_manager_instance:
        await _cache_manager_instance.stop()
        _cache_manager_instance = None