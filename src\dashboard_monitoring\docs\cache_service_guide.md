# 統一監控儀表板快取服務指南

## 概述

統一監控儀表板快取服務是一個高效能的記憶體快取系統，專為支援監控系統的即時更新需求而設計。它提供了 TTL（Time To Live）過期機制、快取統計功能和記憶體使用最佳化，確保系統能在 5 秒內響應資料變更。

## 核心特性

### 🚀 高效能快取
- **記憶體快取**：所有資料儲存在記憶體中，提供毫秒級存取速度
- **TTL 過期機制**：自動清理過期資料，防止記憶體洩漏
- **LRU 淘汰策略**：當快取滿時自動移除最少使用的項目
- **並發安全**：使用執行緒鎖確保多執行緒環境下的資料一致性

### 📊 智能統計
- **命中率監控**：追蹤快取命中率，評估快取效果
- **記憶體使用監控**：即時監控記憶體使用情況
- **存取時間統計**：追蹤平均存取時間，識別效能瓶頸
- **按類型統計**：分別統計不同資料類型的快取使用情況

### 🔧 記憶體最佳化
- **記憶體壓力檢測**：自動檢測記憶體使用是否超過閾值
- **智能清理**：在記憶體壓力下自動清理過期和最少使用的項目
- **垃圾回收**：在必要時觸發 Python 垃圾回收
- **大小計算**：準確計算每個快取項目的記憶體使用量

## 架構設計

```
┌─────────────────────────────────────────────────────────────┐
│                    快取服務架構                              │
├─────────────────────────────────────────────────────────────┤
│  應用層                                                     │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  快取管理器      │  │  快取工具       │                  │
│  │  CacheManager   │  │  CacheUtils     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  核心層                                                     │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              快取服務 (CacheService)                    ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      ││
│  │  │ TTL 管理    │ │ LRU 淘汰    │ │ 統計收集    │      ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘      ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  儲存層                                                     │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              記憶體儲存 (Memory Store)                  ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      ││
│  │  │ 快取項目    │ │ 索引結構    │ │ 統計資料    │      ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘      ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 快速開始

### 基本使用

```python
from src.dashboard_monitoring.core.dashboard_cache_service import get_cache_service

# 獲取快取服務實例
cache_service = get_cache_service()

# 設定快取
cache_service.set("user:123", {"name": "張三", "role": "admin"}, ttl=300)

# 獲取快取
user_data = cache_service.get("user:123")
print(user_data)  # {'name': '張三', 'role': 'admin'}

# 檢查快取是否存在
if cache_service.exists("user:123"):
    print("快取存在")

# 刪除快取
cache_service.delete("user:123")
```

### 使用快取管理器

```python
from src.dashboard_monitoring.core.dashboard_cache_manager import get_cache_manager

# 獲取快取管理器
cache_manager = get_cache_manager()

# 快取監控指標
email_metrics = EmailMetrics(pending_count=10, processing_count=5, ...)
await cache_manager.cache_email_metrics(email_metrics)

# 獲取快取的指標
cached_metrics = await cache_manager.get_email_metrics()

# 快取業務資料
vendor_stats = {"GTK": {"count": 15}, "JCET": {"count": 8}}
await cache_manager.cache_vendor_stats(vendor_stats)
```

### 使用裝飾器

```python
from src.dashboard_monitoring.utils.dashboard_cache_utils import cache_result

@cache_result(ttl=300, namespace="business")
async def get_expensive_data(param1: str, param2: int):
    # 模擬耗時操作
    await asyncio.sleep(2)
    return f"結果: {param1}_{param2}"

# 首次調用會執行函數
result1 = await get_expensive_data("test", 123)  # 耗時 2 秒

# 第二次調用使用快取
result2 = await get_expensive_data("test", 123)  # 毫秒級回應
```

## 配置選項

### 基本配置

```python
from src.dashboard_monitoring.config.dashboard_cache_config import CacheConfig

config = CacheConfig(
    max_size=1000,              # 最大快取項目數量
    default_ttl=300,            # 預設 TTL (秒)
    cleanup_interval=60,        # 清理間隔 (秒)
    max_memory_mb=256,          # 最大記憶體使用 (MB)
    memory_pressure_threshold=0.8,  # 記憶體壓力閾值
    enable_statistics=True,     # 啟用統計功能
    enable_debug_logging=False  # 啟用除錯日誌
)
```

### 環境變數配置

```bash
# 快取配置
DASHBOARD_CACHE_MAX_SIZE=1000
DASHBOARD_CACHE_DEFAULT_TTL=300
DASHBOARD_CACHE_CLEANUP_INTERVAL=60
DASHBOARD_CACHE_MAX_MEMORY_MB=256
DASHBOARD_CACHE_MEMORY_THRESHOLD=0.8

# 功能開關
DASHBOARD_CACHE_ENABLE_STATS=true
DASHBOARD_CACHE_ENABLE_MEMORY_MONITORING=true
DASHBOARD_CACHE_DEBUG=false
DASHBOARD_CACHE_LOG_OPERATIONS=false
```

### 按類型 TTL 配置

```python
config = CacheConfig()
config.ttl_by_type = {
    # 監控指標 - 短期快取
    'email_metrics': 30,      # 30秒
    'celery_metrics': 30,     # 30秒
    'system_metrics': 60,     # 1分鐘
    
    # 業務資料 - 中期快取
    'business_metrics': 300,  # 5分鐘
    'vendor_stats': 300,      # 5分鐘
    
    # 配置資料 - 長期快取
    'alert_rules': 1800,      # 30分鐘
    'dashboard_config': 3600, # 1小時
}
```

## API 端點

### 快取狀態

```http
GET /api/monitoring/cache/status
```

回應：
```json
{
  "status": "success",
  "data": {
    "service_status": "running",
    "statistics": {
      "total_entries": 150,
      "total_size_mb": 12.5,
      "hit_rate": 85.2,
      "memory_usage_mb": 45.8
    },
    "health": {
      "status": "excellent",
      "health_score": 95
    }
  }
}
```

### 快取統計

```http
GET /api/monitoring/cache/statistics
```

### 快取健康狀態

```http
GET /api/monitoring/cache/health
```

### 設定快取

```http
POST /api/monitoring/cache/set
Content-Type: application/json

{
  "key": "test_key",
  "value": {"data": "test_value"},
  "ttl": 300
}
```

### 獲取快取項目

```http
GET /api/monitoring/cache/entry/test_key
```

### 刪除快取項目

```http
DELETE /api/monitoring/cache/entry/test_key
```

### 失效快取模式

```http
POST /api/monitoring/cache/invalidate
Content-Type: application/json

{
  "pattern": "metrics:*"
}
```

### 清空快取

```http
DELETE /api/monitoring/cache/clear?confirm=true
```

### 最佳化快取

```http
POST /api/monitoring/cache/optimize
```

## 監控指標

### 效能指標

- **命中率 (Hit Rate)**：快取命中的百分比
- **平均存取時間 (Avg Access Time)**：平均快取存取時間（毫秒）
- **記憶體使用率 (Memory Usage)**：當前記憶體使用量
- **淘汰率 (Eviction Rate)**：項目被淘汰的頻率

### 統計資料

- **總項目數 (Total Entries)**：當前快取項目總數
- **命中次數 (Hit Count)**：快取命中總次數
- **未命中次數 (Miss Count)**：快取未命中總次數
- **淘汰次數 (Eviction Count)**：項目被淘汰的總次數
- **過期次數 (Expired Count)**：項目自然過期的總次數

## 最佳實踐

### 1. 選擇合適的 TTL

```python
# 根據資料更新頻率設定 TTL
cache_manager.cache_email_metrics(metrics, ttl=30)    # 高頻更新
cache_manager.cache_vendor_stats(stats, ttl=300)     # 中頻更新
cache_manager.cache_system_info(info, ttl=3600)      # 低頻更新
```

### 2. 使用命名空間

```python
# 使用命名空間組織快取鍵
email_key = f"{CacheNamespace.EMAIL_METRICS}:current"
business_key = f"{CacheNamespace.BUSINESS}:vendor_stats"
```

### 3. 批量操作

```python
from src.dashboard_monitoring.utils.dashboard_cache_utils import CacheBatch

batch = CacheBatch()
batch.add_set("key1", "value1", ttl=300)
batch.add_set("key2", "value2", ttl=300)
batch.add_set("key3", "value3", ttl=300)

results = await batch.execute()
print(f"成功: {results['success']}, 失敗: {results['failed']}")
```

### 4. 錯誤處理

```python
@cache_result(ttl=300, ignore_errors=True)
async def safe_cached_function():
    # 即使快取失敗，函數仍會執行
    return expensive_computation()
```

### 5. 記憶體監控

```python
# 定期檢查快取健康狀態
health = cache_manager.get_cache_health()
if health['health_score'] < 70:
    logger.warning(f"快取健康分數較低: {health['health_score']}")
    for recommendation in health['recommendations']:
        logger.info(f"建議: {recommendation}")
```

## 故障排除

### 常見問題

#### 1. 記憶體使用過高

**症狀**：記憶體使用率超過 90%
**解決方案**：
- 減少 `max_memory_mb` 設定
- 降低 TTL 值
- 增加清理頻率

```python
# 手動觸發記憶體最佳化
await optimize_cache_memory()
```

#### 2. 命中率過低

**症狀**：命中率低於 50%
**解決方案**：
- 增加 TTL 值
- 檢查快取鍵是否正確
- 增加快取大小限制

#### 3. 存取時間過長

**症狀**：平均存取時間超過 10ms
**解決方案**：
- 檢查快取項目大小
- 最佳化資料結構
- 考慮使用序列化

### 除錯模式

```python
# 啟用除錯日誌
config = CacheConfig(
    enable_debug_logging=True,
    log_cache_operations=True
)
```

### 效能調優

```python
# 效能調優配置
config = CacheConfig(
    max_size=5000,              # 增加快取大小
    cleanup_interval=30,        # 更頻繁的清理
    memory_pressure_threshold=0.7,  # 更早觸發清理
    enable_statistics=True      # 啟用統計以監控效能
)
```

## 整合範例

### 與監控協調器整合

```python
class DashboardMonitoringCoordinator:
    def __init__(self):
        self.cache_manager = get_cache_manager()
    
    async def collect_all_metrics(self):
        # 嘗試從快取獲取
        cached_metrics = await self.cache_manager.get_dashboard_metrics()
        if cached_metrics:
            return cached_metrics
        
        # 收集新指標
        metrics = await self._collect_fresh_metrics()
        
        # 快取結果
        await self.cache_manager.cache_dashboard_metrics(metrics, ttl=30)
        
        return metrics
```

### 與 WebSocket 整合

```python
class WebSocketManager:
    def __init__(self):
        self.cache_manager = get_cache_manager()
    
    async def broadcast_metrics(self):
        # 從快取獲取最新指標
        metrics = await self.cache_manager.get_dashboard_metrics()
        if metrics:
            await self.broadcast_to_all_clients(metrics)
    
    async def broadcast_cache_statistics(self):
        # 廣播快取統計資料
        cache_stats = self.cache_manager.get_cache_statistics()
        message = {
            "type": "cache_statistics",
            "payload": cache_stats['statistics'],
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_all_clients(message)
```

### 與監控 API 整合

快取服務已完全整合到監控 API 中，提供以下端點：

```python
# 在 dashboard_monitoring_api.py 中自動包含快取路由
router.include_router(cache_router, prefix="", tags=["快取管理"])
```

**可用的快取 API 端點：**
- `GET /api/monitoring/cache/status` - 快取服務狀態
- `GET /api/monitoring/cache/statistics` - 詳細統計資料
- `GET /api/monitoring/cache/health` - 健康狀態檢查
- `POST /api/monitoring/cache/optimize` - 記憶體最佳化
- `DELETE /api/monitoring/cache/clear` - 清空快取

### 與依賴注入系統整合

```python
# 在 dashboard_dependencies.py 中註冊快取服務
from ..core.dashboard_cache_manager import get_cache_manager

async def get_cache_manager_dependency():
    """快取管理器依賴注入"""
    try:
        return get_cache_manager()
    except Exception as e:
        logger.error(f"無法獲取快取管理器: {e}")
        raise HTTPException(status_code=503, detail="快取服務不可用")

# 在 API 端點中使用
@router.get("/cached-data")
async def get_cached_data(
    cache_manager: DashboardCacheManager = Depends(get_cache_manager_dependency)
):
    return await cache_manager.get_dashboard_metrics()
```

## 測試

### 單元測試

```python
import pytest
from src.dashboard_monitoring.core.dashboard_cache_service import DashboardCacheService

@pytest.fixture
def cache_service():
    return DashboardCacheService(max_size=100, default_ttl=300)

def test_cache_set_get(cache_service):
    cache_service.set("test_key", "test_value")
    assert cache_service.get("test_key") == "test_value"

def test_cache_expiration(cache_service):
    cache_service.set("expire_key", "expire_value", ttl=0.1)
    time.sleep(0.2)
    assert cache_service.get("expire_key") is None
```

### 整合測試

```python
@pytest.mark.asyncio
async def test_cache_manager_integration():
    cache_manager = await initialize_cache_manager()
    
    # 測試指標快取
    metrics = EmailMetrics(pending_count=10, ...)
    await cache_manager.cache_email_metrics(metrics)
    
    cached_metrics = await cache_manager.get_email_metrics()
    assert cached_metrics.pending_count == 10
    
    await shutdown_cache_manager()
```

### 效能測試

```python
@pytest.mark.asyncio
async def test_cache_performance():
    cache_service = DashboardCacheService(max_size=10000)
    
    # 測試大量寫入
    start_time = time.time()
    for i in range(1000):
        cache_service.set(f"perf_key_{i}", f"value_{i}")
    write_time = time.time() - start_time
    
    assert write_time < 1.0  # 應該在 1 秒內完成
```

## 版本更新

### v1.0.0
- 初始版本
- 基本快取功能
- TTL 過期機制
- LRU 淘汰策略

### v1.1.0 (計劃中)
- Redis 後端支援
- 分散式快取
- 快取預熱策略
- 更多淘汰策略 (LFU, FIFO)

## 支援

如有問題或建議，請聯繫開發團隊或查看相關文檔：

- [API 文檔](./api_documentation.md)
- [架構設計](./architecture_design.md)
- [故障排除指南](./troubleshooting_guide.md)