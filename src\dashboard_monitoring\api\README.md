# API 層文檔

## 📋 概述

API 層提供統一監控儀表板的所有對外介面，包括 REST API 端點和 WebSocket 即時通訊。

## 📁 模組結構

### `dashboard_dependencies.py` - 依賴注入系統
統一的依賴注入管理，解決服務耦合問題。

**主要功能：**
- 必需依賴和可選依賴模式
- 複合依賴支援多服務組合
- 統一錯誤處理和服務健康檢查
- 類型安全的依賴注入

**使用範例：**
```python
from .dashboard_dependencies import MonitoringCoordinator, Config

@router.get("/dashboard")
async def get_dashboard_data(
    coordinator: MonitoringCoordinator,
    config: Config
) -> Dict[str, Any]:
    metrics = await coordinator.collect_all_metrics()
    return {"status": "success", "data": metrics.dict()}
```

### `dashboard_monitoring_api.py` - 監控 REST API
提供所有監控資料的 REST API 端點。

**主要端點：**
- `GET /api/monitoring/dashboard` - 獲取完整儀表板資料
- `GET /api/monitoring/health` - 系統健康檢查
- `GET /api/monitoring/email/queue` - 郵件佇列狀態
- `GET /api/monitoring/celery/tasks` - Celery 任務狀態
- `GET /api/monitoring/system/resources` - 系統資源狀態
- `GET /api/monitoring/alerts` - 活躍告警
- `GET /api/monitoring/trends/{metric_type}` - 趨勢資料

### `dashboard_websocket.py` - WebSocket 即時通訊 ✅
提供即時監控資料推送的 WebSocket 服務。

**主要功能：**
- **連接管理** - 支援最大 100 個並發連接
- **訂閱機制** - 客戶端可選擇訂閱特定類型的更新
- **廣播功能** - 即時推送指標更新、告警、系統狀態
- **異常處理** - 心跳檢測、超時處理、自動清理

**WebSocket 端點：**
```
ws://localhost:5555/ws/dashboard/{client_id}
```

**支援的訊息類型：**

**客戶端發送：**
- `subscribe` - 訂閱特定類型的更新
- `unsubscribe` - 取消訂閱
- `ping` - 心跳檢測

**伺服器推送：**
- `metrics_update` - 指標更新
- `alert` - 告警通知
- `system_status` - 系統狀態更新
- `pong` - 心跳回應
- `error` - 錯誤訊息
- `connection_info` - 連接資訊

**訂閱類型：**
- `all` - 訂閱所有更新
- `metrics` - 只訂閱指標更新
- `alerts` - 只訂閱告警更新
- `email_queue` - 只訂閱郵件佇列更新
- `celery_tasks` - 只訂閱 Celery 任務更新
- `system_health` - 只訂閱系統健康狀態更新
- `business_metrics` - 只訂閱業務指標更新

**使用範例：**

**JavaScript 客戶端：**
```javascript
const ws = new WebSocket('ws://localhost:5555/ws/dashboard/client-123');

// 連接建立後訂閱指標更新
ws.onopen = function() {
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            types: ['metrics', 'alerts']
        }
    }));
};

// 處理伺服器推送的資料
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'metrics_update':
            updateDashboard(data.payload);
            break;
        case 'alert':
            showAlert(data.payload);
            break;
        case 'connection_info':
            console.log('連接資訊:', data.payload);
            break;
    }
};

// 發送心跳
setInterval(() => {
    ws.send(JSON.stringify({
        type: 'ping',
        payload: {
            client_time: new Date().toISOString()
        }
    }));
}, 30000);
```

**Python 客戶端：**
```python
import asyncio
import websockets
import json

async def dashboard_client():
    uri = "ws://localhost:5555/ws/dashboard/python-client"
    
    async with websockets.connect(uri) as websocket:
        # 訂閱所有更新
        await websocket.send(json.dumps({
            "type": "subscribe",
            "payload": {"types": ["all"]}
        }))
        
        # 接收訊息
        async for message in websocket:
            data = json.loads(message)
            print(f"收到訊息: {data['type']}")
            
            if data['type'] == 'metrics_update':
                print(f"指標更新: {data['payload']}")
            elif data['type'] == 'alert':
                print(f"告警: {data['payload']}")

asyncio.run(dashboard_client())
```

## 🔧 配置參數

WebSocket 相關配置在 `dashboard_config.py` 中：

```python
@dataclass
class WebSocketConfig:
    max_connections: int = 100          # 最大連接數
    heartbeat_interval: int = 30        # 心跳間隔（秒）
    connection_timeout: int = 300       # 連接超時（秒）
    message_queue_size: int = 1000      # 訊息佇列大小
    enable_compression: bool = True     # 啟用壓縮
```

## 📊 效能指標

- **並發連接** - 支援至少 100 個並發 WebSocket 連接
- **即時更新** - 5 秒內反映系統變化
- **API 回應** - 95% 請求在 500ms 內回應
- **記憶體使用** - WebSocket 服務記憶體使用 < 128MB

## 🛡️ 安全考量

- **連接數限制** - 防止資源耗盡攻擊
- **訊息驗證** - 完整的 JSON 格式驗證
- **錯誤隔離** - 不洩露系統內部資訊
- **自動清理** - 定期清理無效連接

## 🧪 測試

WebSocket 功能的完整測試位於：
- `tests/unit/test_dashboard_websocket.py` - 單元測試
- `tests/integration/test_dashboard_websocket_integration.py` - 整合測試

執行測試：
```bash
pytest tests/unit/test_dashboard_websocket.py -v
```

## 📈 監控統計

WebSocket 管理器提供詳細的統計資訊：

```python
from src.dashboard_monitoring.api.dashboard_websocket import get_websocket_stats

stats = await get_websocket_stats()
print(f"當前連接數: {stats['current_connections']}")
print(f"總訊息數: {stats['total_messages_sent']}")
```

## 🔄 與核心服務整合

WebSocket 服務與監控協調器緊密整合：

```python
from src.dashboard_monitoring.core.dashboard_websocket_manager import get_websocket_service

# 獲取 WebSocket 服務
websocket_service = get_websocket_service()

# 廣播指標更新
await websocket_service.broadcast_metrics_update(metrics_data)

# 廣播告警
await websocket_service.broadcast_alert(alert_data)
```

## 🚀 部署注意事項

1. **反向代理配置** - 確保 WebSocket 升級請求正確處理
2. **防火牆設定** - 開放 WebSocket 端口
3. **負載均衡** - 配置 WebSocket 會話親和性
4. **監控告警** - 監控 WebSocket 連接數和訊息處理延遲

## 📞 故障排除

**常見問題：**

1. **連接失敗** - 檢查端口是否開放，防火牆設定
2. **訊息丟失** - 檢查訊息佇列大小，網路穩定性
3. **記憶體洩漏** - 檢查連接清理機制，監控記憶體使用
4. **效能問題** - 檢查並發連接數，調整心跳間隔

**除錯工具：**
```bash
# 檢查 WebSocket 連接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" \
     http://localhost:5555/ws/dashboard/debug-client
```