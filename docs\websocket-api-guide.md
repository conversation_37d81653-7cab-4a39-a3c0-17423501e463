# 統一監控儀表板 WebSocket API 指南

## 概述

統一監控儀表板提供 WebSocket API 用於即時資料推送，支援 5 秒內的資料更新，滿足即時監控需求。本指南詳細說明了 WebSocket 連接、訊息格式和使用方法。

**狀態**: ✅ **已實現並可用**

## 主儀表板頁面整合

統一監控儀表板已完成主頁面實現，包含完整的 WebSocket 整合：

### 前端實現特色
- ✅ **響應式設計**: 支援桌面、平板、手機等不同螢幕尺寸
- ✅ **即時資料更新**: 透過 WebSocket 實現 5 秒內資料更新
- ✅ **六大監控區域**: 
  - 📧 郵件處理監控（廠商統計、Code Comparison 任務）
  - 🔄 Celery 任務監控（任務類型統計、工作者狀態）
  - 💻 系統資源監控（CPU/記憶體/磁碟、服務健康）
  - 📁 檔案處理監控（檔案類型、儲存空間）
  - 📊 業務指標監控（MO/LOT 統計、品質分數）
  - 🚨 告警管理（告警統計、確認操作）
- ✅ **互動式 UI**: 刷新按鈕、面板展開/收縮、告警確認
- ✅ **深色模式支援**: 自動適應系統主題設定
- ✅ **自動重連機制**: 連接斷線時自動重新連接
- ✅ **心跳檢測**: 30 秒心跳間隔確保連接穩定

### 檔案結構
```
src/dashboard_monitoring/
├── templates/
│   └── dashboard_main.html          # 主儀表板頁面
├── static/
│   ├── css/
│   │   ├── dashboard_main.css       # 主要樣式
│   │   └── dashboard_components.css # 元件樣式
│   └── js/
│       ├── dashboard_websocket.js   # WebSocket 管理
│       ├── dashboard_charts.js      # 圖表處理
│       └── dashboard_main.js        # 主要功能
```

## 連接端點

### 主要監控 WebSocket
```
ws://localhost:8000/ws/dashboard/{client_id}
```

**參數說明：**
- `client_id`: 唯一的客戶端識別碼，用於管理連接

## 訊息格式

### 基本訊息結構
```json
{
  "type": "message_type",
  "payload": {},
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 訊息類型

#### 1. 訂閱請求 (subscribe)
```json
{
  "type": "subscribe",
  "payload": {
    "channels": ["metrics", "alerts", "system_status"],
    "filters": {
      "metric_types": ["email", "celery", "system"],
      "alert_levels": ["warning", "critical"]
    }
  }
}
```

#### 2. 取消訂閱 (unsubscribe)
```json
{
  "type": "unsubscribe",
  "payload": {
    "channels": ["alerts"]
  }
}
```

#### 3. 指標更新 (metrics_update)
```json
{
  "type": "metrics_update",
  "payload": {
    "email_metrics": {
      "pending_count": 10,
      "processing_count": 5,
      "completed_count": 100,
      "failed_count": 2,
      "vendor_queue_counts": {
        "GTK": 5,
        "JCET": 3,
        "ETD": 2
      }
    },
    "celery_metrics": {
      "total_active": 8,
      "total_pending": 15,
      "task_type_counts": {
        "code_comparison": {"active": 3, "pending": 5},
        "csv_to_summary": {"active": 2, "pending": 3},
        "compression": {"active": 1, "pending": 2}
      }
    },
    "system_metrics": {
      "cpu_percent": 45.2,
      "memory_percent": 68.5,
      "disk_percent": 32.1
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 4. 告警通知 (alert)
```json
{
  "type": "alert",
  "payload": {
    "id": "alert_123",
    "alert_type": "queue_overflow",
    "level": "warning",
    "title": "郵件佇列過多",
    "message": "待處理郵件數量超過 10 個",
    "source": "email_monitor",
    "triggered_at": "2024-01-01T12:00:00.000Z",
    "metadata": {
      "current_count": 15,
      "threshold": 10
    }
  }
}
```

#### 5. 系統狀態 (system_status)
```json
{
  "type": "system_status",
  "payload": {
    "overall_status": "healthy",
    "services": {
      "email_service": "healthy",
      "celery_service": "healthy",
      "database": "healthy",
      "cache_service": "healthy"
    },
    "cache_statistics": {
      "hit_rate": 85.2,
      "memory_usage_mb": 45.8,
      "total_entries": 150
    }
  }
}
```

#### 6. 心跳檢測 (heartbeat)
```json
{
  "type": "heartbeat",
  "payload": {
    "server_time": "2024-01-01T12:00:00.000Z",
    "connection_count": 25
  }
}
```

## 客戶端實現

### JavaScript 客戶端範例

```javascript
class DashboardWebSocket {
    constructor(clientId) {
        this.clientId = clientId;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5秒
        this.subscriptions = new Set();
        this.messageHandlers = new Map();
    }

    connect() {
        const wsUrl = `ws://localhost:8000/ws/dashboard/${this.clientId}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = (event) => {
            console.log('WebSocket 連接已建立');
            this.reconnectAttempts = 0;
            this.onConnected();
        };

        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('解析 WebSocket 訊息失敗:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket 連接已關閉');
            this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket 錯誤:', error);
        };
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket 未連接，無法發送訊息');
        }
    }

    subscribe(channels, filters = {}) {
        const message = {
            type: 'subscribe',
            payload: {
                channels: channels,
                filters: filters
            }
        };
        
        channels.forEach(channel => this.subscriptions.add(channel));
        this.send(message);
    }

    unsubscribe(channels) {
        const message = {
            type: 'unsubscribe',
            payload: {
                channels: channels
            }
        };
        
        channels.forEach(channel => this.subscriptions.delete(channel));
        this.send(message);
    }

    onMessage(type, handler) {
        this.messageHandlers.set(type, handler);
    }

    handleMessage(message) {
        const handler = this.messageHandlers.get(message.type);
        if (handler) {
            handler(message.payload, message.timestamp);
        } else {
            console.log('未處理的訊息類型:', message.type);
        }
    }

    onConnected() {
        // 重新訂閱之前的頻道
        if (this.subscriptions.size > 0) {
            this.subscribe(Array.from(this.subscriptions));
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`嘗試重新連接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectInterval);
        } else {
            console.error('達到最大重連次數，停止重連');
        }
    }
}

// 使用範例
const dashboard = new DashboardWebSocket('dashboard_client_001');

// 設定訊息處理器
dashboard.onMessage('metrics_update', (payload, timestamp) => {
    updateDashboardMetrics(payload);
});

dashboard.onMessage('alert', (payload, timestamp) => {
    showAlert(payload);
});

dashboard.onMessage('system_status', (payload, timestamp) => {
    updateSystemStatus(payload);
});

// 連接並訂閱
dashboard.connect();
dashboard.subscribe(['metrics', 'alerts', 'system_status'], {
    metric_types: ['email', 'celery', 'system'],
    alert_levels: ['warning', 'critical']
});

// 更新儀表板指標
function updateDashboardMetrics(metrics) {
    // 更新郵件指標
    if (metrics.email_metrics) {
        document.getElementById('email-pending').textContent = metrics.email_metrics.pending_count;
        document.getElementById('email-processing').textContent = metrics.email_metrics.processing_count;
        document.getElementById('email-completed').textContent = metrics.email_metrics.completed_count;
        document.getElementById('email-failed').textContent = metrics.email_metrics.failed_count;
    }

    // 更新 Celery 指標
    if (metrics.celery_metrics) {
        document.getElementById('celery-active').textContent = metrics.celery_metrics.total_active;
        document.getElementById('celery-pending').textContent = metrics.celery_metrics.total_pending;
    }

    // 更新系統指標
    if (metrics.system_metrics) {
        updateProgressBar('cpu-usage', metrics.system_metrics.cpu_percent);
        updateProgressBar('memory-usage', metrics.system_metrics.memory_percent);
        updateProgressBar('disk-usage', metrics.system_metrics.disk_percent);
    }
}

// 顯示告警
function showAlert(alert) {
    const alertContainer = document.getElementById('alerts-container');
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${alert.level}`;
    alertElement.innerHTML = `
        <div class="alert-header">
            <span class="alert-level">${alert.level.toUpperCase()}</span>
            <span class="alert-time">${new Date(alert.triggered_at).toLocaleTimeString()}</span>
        </div>
        <div class="alert-title">${alert.title}</div>
        <div class="alert-message">${alert.message}</div>
        <button onclick="acknowledgeAlert('${alert.id}')" class="alert-acknowledge">確認</button>
    `;
    
    alertContainer.insertBefore(alertElement, alertContainer.firstChild);
    
    // 自動移除舊告警
    const alerts = alertContainer.querySelectorAll('.alert');
    if (alerts.length > 10) {
        alertContainer.removeChild(alerts[alerts.length - 1]);
    }
}

// 更新進度條
function updateProgressBar(elementId, percentage) {
    const progressBar = document.getElementById(elementId);
    const progressFill = progressBar.querySelector('.progress-fill');
    const progressText = progressBar.querySelector('.progress-text');
    
    progressFill.style.width = `${percentage}%`;
    progressText.textContent = `${percentage.toFixed(1)}%`;
    
    // 根據使用率設定顏色
    if (percentage > 90) {
        progressFill.className = 'progress-fill critical';
    } else if (percentage > 80) {
        progressFill.className = 'progress-fill warning';
    } else {
        progressFill.className = 'progress-fill normal';
    }
}

// 確認告警
async function acknowledgeAlert(alertId) {
    try {
        const response = await fetch(`/api/monitoring/alerts/${alertId}/acknowledge`, {
            method: 'POST'
        });
        
        if (response.ok) {
            const alertElement = document.querySelector(`[data-alert-id="${alertId}"]`);
            if (alertElement) {
                alertElement.classList.add('acknowledged');
            }
        }
    } catch (error) {
        console.error('確認告警失敗:', error);
    }
}
```

### Python 客戶端範例

```python
import asyncio
import websockets
import json
from typing import Dict, Any, Callable, List

class DashboardWebSocketClient:
    def __init__(self, client_id: str, server_url: str = "ws://localhost:8000"):
        self.client_id = client_id
        self.server_url = server_url
        self.websocket = None
        self.message_handlers: Dict[str, Callable] = {}
        self.subscriptions: set = set()
        self.running = False

    async def connect(self):
        """連接到 WebSocket 服務器"""
        uri = f"{self.server_url}/ws/dashboard/{self.client_id}"
        
        try:
            self.websocket = await websockets.connect(uri)
            self.running = True
            print(f"已連接到 {uri}")
            
            # 開始監聽訊息
            await self.listen()
            
        except Exception as e:
            print(f"連接失敗: {e}")

    async def disconnect(self):
        """斷開連接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()

    async def send_message(self, message: Dict[str, Any]):
        """發送訊息"""
        if self.websocket:
            await self.websocket.send(json.dumps(message))

    async def subscribe(self, channels: List[str], filters: Dict[str, Any] = None):
        """訂閱頻道"""
        message = {
            "type": "subscribe",
            "payload": {
                "channels": channels,
                "filters": filters or {}
            }
        }
        
        self.subscriptions.update(channels)
        await self.send_message(message)

    async def unsubscribe(self, channels: List[str]):
        """取消訂閱"""
        message = {
            "type": "unsubscribe",
            "payload": {
                "channels": channels
            }
        }
        
        for channel in channels:
            self.subscriptions.discard(channel)
        
        await self.send_message(message)

    def on_message(self, message_type: str, handler: Callable):
        """註冊訊息處理器"""
        self.message_handlers[message_type] = handler

    async def listen(self):
        """監聽訊息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError as e:
                    print(f"解析訊息失敗: {e}")
                except Exception as e:
                    print(f"處理訊息失敗: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket 連接已關閉")
        except Exception as e:
            print(f"監聽訊息時發生錯誤: {e}")

    async def handle_message(self, message: Dict[str, Any]):
        """處理收到的訊息"""
        message_type = message.get("type")
        payload = message.get("payload", {})
        timestamp = message.get("timestamp")
        
        handler = self.message_handlers.get(message_type)
        if handler:
            if asyncio.iscoroutinefunction(handler):
                await handler(payload, timestamp)
            else:
                handler(payload, timestamp)
        else:
            print(f"未處理的訊息類型: {message_type}")

# 使用範例
async def main():
    client = DashboardWebSocketClient("python_client_001")
    
    # 註冊訊息處理器
    def handle_metrics_update(payload, timestamp):
        print(f"收到指標更新: {timestamp}")
        if "email_metrics" in payload:
            email_metrics = payload["email_metrics"]
            print(f"郵件佇列: 待處理={email_metrics['pending_count']}, "
                  f"處理中={email_metrics['processing_count']}")
    
    def handle_alert(payload, timestamp):
        print(f"收到告警: {payload['level']} - {payload['title']}")
        print(f"訊息: {payload['message']}")
    
    client.on_message("metrics_update", handle_metrics_update)
    client.on_message("alert", handle_alert)
    
    # 連接並訂閱
    await client.connect()
    await client.subscribe(["metrics", "alerts"], {
        "metric_types": ["email", "celery"],
        "alert_levels": ["warning", "critical"]
    })
    
    # 保持連接
    try:
        while client.running:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("正在關閉連接...")
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
```

## 快取整合

WebSocket API 與快取服務緊密整合，提供高效的即時資料推送：

### 快取支援的即時更新
- **指標快取**: 30秒 TTL，確保資料新鮮度
- **告警快取**: 60秒 TTL，支援告警狀態同步
- **系統狀態快取**: 60秒 TTL，提供系統健康資訊

### 快取統計 WebSocket 訊息
```json
{
  "type": "cache_statistics",
  "payload": {
    "hit_rate": 85.2,
    "memory_usage_mb": 45.8,
    "total_entries": 150,
    "avg_access_time_ms": 2.5,
    "health_score": 95
  }
}
```

## 錯誤處理

### 連接錯誤
- **503 Service Unavailable**: 服務暫時不可用
- **401 Unauthorized**: 認證失敗
- **429 Too Many Requests**: 連接數量超過限制

### 訊息錯誤
- **400 Bad Request**: 訊息格式錯誤
- **404 Not Found**: 請求的資源不存在
- **422 Unprocessable Entity**: 訊息內容無效

### 重連策略
1. 指數退避重連（1s, 2s, 4s, 8s, 16s）
2. 最大重連次數：5次
3. 連接成功後重新訂閱之前的頻道

## 效能考量

### 連接管理
- **最大並發連接**: 100個
- **心跳間隔**: 30秒
- **訊息佇列大小**: 1000條

### 資料壓縮
- 大型訊息自動使用 gzip 壓縮
- 支援訊息批量發送以減少網路開銷

### 頻率限制
- 每個客戶端每秒最多 10 條訊息
- 訂閱更新頻率：最快 5 秒一次

## 安全考量

### 認證授權
- 支援 JWT Token 認證
- 基於角色的訂閱權限控制

### 資料保護
- 敏感資料自動過濾
- 支援 WSS (WebSocket Secure) 加密傳輸

### 審計日誌
- 記錄所有連接和訂閱活動
- 監控異常連接模式

## 監控和除錯

### 連接監控
```http
GET /api/monitoring/websocket/connections
```

### 訊息統計
```http
GET /api/monitoring/websocket/statistics
```

### 除錯模式
設定環境變數 `WEBSOCKET_DEBUG=true` 啟用詳細日誌記錄。

## 最佳實踐

1. **合理訂閱**: 只訂閱需要的頻道和資料類型
2. **錯誤處理**: 實現完整的錯誤處理和重連邏輯
3. **資源清理**: 及時取消不需要的訂閱
4. **效能監控**: 監控連接狀態和訊息處理效能
5. **安全考量**: 使用 HTTPS/WSS 和適當的認證機制