# WebSocket API 文檔
> **📡 統一監控儀表板 WebSocket API**  
> 即時監控資料推送的完整 API 參考

## 📋 概述

統一監控儀表板的 WebSocket API 提供即時監控資料推送功能，支援多種訂閱類型和雙向通訊。

## 🔗 連接端點

```
ws://localhost:5555/ws/dashboard/{client_id}
```

**參數說明：**
- `client_id`: 唯一的客戶端識別碼，用於區分不同的連接

## 📨 訊息格式

所有 WebSocket 訊息都使用 JSON 格式：

```json
{
    "type": "message_type",
    "payload": {
        "data": "message_data"
    },
    "timestamp": "2025-01-31T10:30:00Z",
    "client_id": "client-123"
}
```

## 📤 客戶端發送的訊息類型

### 1. 訂閱 (subscribe)

訂閱特定類型的監控資料更新：

```json
{
    "type": "subscribe",
    "payload": {
        "types": ["metrics", "alerts", "email_queue"]
    }
}
```

**可用的訂閱類型：**
- `all` - 訂閱所有更新
- `metrics` - 指標更新
- `alerts` - 告警通知
- `email_queue` - 郵件佇列更新
- `celery_tasks` - Celery 任務更新
- `system_health` - 系統健康狀態更新
- `business_metrics` - 業務指標更新

### 2. 取消訂閱 (unsubscribe)

取消特定類型的訂閱：

```json
{
    "type": "unsubscribe",
    "payload": {
        "types": ["alerts"]
    }
}
```

### 3. 心跳 (ping)

發送心跳以保持連接活躍：

```json
{
    "type": "ping",
    "payload": {
        "client_time": "2025-01-31T10:30:00Z"
    }
}
```

## 📥 伺服器推送的訊息類型

### 1. 指標更新 (metrics_update)

當監控指標發生變化時推送：

```json
{
    "type": "metrics_update",
    "payload": {
        "email_metrics": {
            "pending_count": 15,
            "processing_count": 3,
            "completed_count": 142,
            "failed_count": 2
        },
        "celery_metrics": {
            "total_active": 5,
            "total_pending": 12,
            "task_type_counts": {
                "code_comparison": {"active": 2, "pending": 5},
                "csv_to_summary": {"active": 1, "pending": 3}
            }
        },
        "system_metrics": {
            "cpu_percent": 75.5,
            "memory_percent": 68.2,
            "disk_percent": 45.8
        }
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

### 2. 告警通知 (alert)

當系統產生告警時推送：

```json
{
    "type": "alert",
    "payload": {
        "id": "alert-001",
        "title": "CPU 使用率過高",
        "level": "warning",
        "message": "CPU 使用率已達到 85%，建議檢查系統負載",
        "source": "system_monitor",
        "triggered_at": "2025-01-31T10:30:00Z",
        "metadata": {
            "current_value": 85.2,
            "threshold_value": 80.0,
            "metric_type": "cpu_percent"
        }
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

### 3. 系統狀態 (system_status)

系統健康狀態變化時推送：

```json
{
    "type": "system_status",
    "payload": {
        "overall_status": "warning",
        "service_health": {
            "email_service": "healthy",
            "celery_service": "healthy",
            "database": "warning"
        },
        "active_alerts": 3,
        "critical_alerts": 0
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

### 4. 心跳回應 (pong)

對客戶端心跳的回應：

```json
{
    "type": "pong",
    "payload": {
        "server_time": "2025-01-31T10:30:00Z",
        "client_ping_time": "2025-01-31T10:30:00Z",
        "connection_alive": true
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

### 5. 連接資訊 (connection_info)

連接建立或狀態變化時推送：

```json
{
    "type": "connection_info",
    "payload": {
        "action": "connected",
        "client_id": "client-123",
        "connected_at": "2025-01-31T10:30:00Z",
        "server_time": "2025-01-31T10:30:00Z",
        "max_connections": 100,
        "heartbeat_interval": 30
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

### 6. 錯誤訊息 (error)

當發生錯誤時推送：

```json
{
    "type": "error",
    "payload": {
        "error": "無效的訊息類型",
        "error_code": "INVALID_MESSAGE_TYPE",
        "details": "收到的訊息類型 'invalid_type' 不被支援"
    },
    "timestamp": "2025-01-31T10:30:00Z"
}
```

## 🔧 配置參數

WebSocket 服務的配置參數：

```python
# 在 dashboard_config.py 中配置
websocket_config = WebSocketConfig(
    max_connections=100,        # 最大連接數
    heartbeat_interval=30,      # 心跳間隔（秒）
    connection_timeout=300,     # 連接超時（秒）
    message_queue_size=1000,    # 訊息佇列大小
    enable_compression=True     # 啟用壓縮
)
```

## 📊 效能指標

- **並發連接限制**: 預設支援 100 個並發連接
- **訊息佇列**: 1000 個訊息的緩衝佇列
- **心跳間隔**: 30 秒（可配置）
- **連接超時**: 300 秒（可配置）
- **自動重連**: 指數退避策略，最多重試 5 次

## 🛡️ 安全注意事項

1. **連接數限制**: 防止資源耗盡攻擊
2. **訊息驗證**: 所有訊息都經過 JSON 格式驗證
3. **錯誤隔離**: 單一連接錯誤不影響其他連接
4. **自動清理**: 定期清理無效和超時的連接

## 📈 監控統計

獲取 WebSocket 服務統計資訊：

```bash
curl http://localhost:5555/api/monitoring/websocket/stats
```

回應範例：

```json
{
    "current_connections": 15,
    "max_connections": 100,
    "total_connections": 142,
    "total_messages_sent": 5847,
    "total_messages_received": 1923,
    "uptime_seconds": 3600,
    "message_queue_size": 0
}
```

## 🚀 最佳實踐

1. **客戶端實現**
   - 實現自動重連機制
   - 使用指數退避策略
   - 正確處理所有訊息類型

2. **訂閱管理**
   - 只訂閱需要的資料類型
   - 適時取消不需要的訂閱
   - 避免重複訂閱

3. **錯誤處理**
   - 實現完整的錯誤處理邏輯
   - 記錄詳細的錯誤日誌
   - 提供使用者友好的錯誤提示

4. **效能最佳化**
   - 使用適當的心跳間隔
   - 避免發送過於頻繁的訊息
   - 實現客戶端快取機制

## 🐛 故障排除

### 常見問題

1. **連接失敗**
   - 檢查伺服器是否運行
   - 確認端口 5555 是否開放
   - 檢查防火牆設定

2. **訊息丟失**
   - 檢查網路連接穩定性
   - 確認訂閱類型正確
   - 檢查訊息佇列是否滿載

3. **頻繁斷線**
   - 檢查心跳設定
   - 確認網路穩定性
   - 調整連接超時時間

### 除錯工具

使用瀏覽器開發者工具監控 WebSocket 連接：

```javascript
// 在瀏覽器控制台中執行
const ws = new WebSocket('ws://localhost:5555/ws/dashboard/debug-client');
ws.onopen = () => console.log('連接已建立');
ws.onmessage = (event) => console.log('收到訊息:', JSON.parse(event.data));
ws.onerror = (error) => console.error('WebSocket 錯誤:', error);
```

## 📚 相關文檔

- [統一監控儀表板 README](../README.md)
- [API 層文檔](../api/README.md)
- [核心服務層文檔](../core/README.md)
- [WebSocket API 使用指南](../../docs/websocket-api-guide.md)