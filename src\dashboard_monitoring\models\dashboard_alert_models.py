"""
Dashboard Alert Models

This module defines all data models for alert management in the unified dashboard.
Supports multi-level alerts, notification channels, and alert history tracking.

Requirements covered: 4, 12
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json
import uuid
from pydantic import BaseModel, Field, validator


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status states"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class AlertType(Enum):
    """Types of alerts that can be generated"""
    QUEUE_OVERFLOW = "queue_overflow"
    TASK_FAILURE = "task_failure"
    RESOURCE_HIGH = "resource_high"
    SERVICE_DOWN = "service_down"
    DATA_QUALITY = "data_quality"
    PROCESSING_SLOW = "processing_slow"
    CONNECTION_FAILURE = "connection_failure"
    STORAGE_FULL = "storage_full"
    SYSTEM_ERROR = "system_error"


class NotificationChannel(Enum):
    """Available notification channels - Requirement 12.1"""
    EMAIL = "email"
    LINE = "line"
    SYSTEM_NOTIFICATION = "system_notification"
    WEBHOOK = "webhook"
    SMS = "sms"


@dataclass
class AlertRule:
    """Alert rule configuration"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    category: str = ""  # 'email', 'celery', 'system', 'file', 'business'
    metric_path: str = ""  # 'email.pending_count', 'system.cpu_percent'
    condition_type: str = "greater_than"  # 'greater_than', 'less_than', 'equals', 'not_equals'
    threshold_value: float = 0.0
    alert_level: AlertLevel = AlertLevel.WARNING
    alert_message: str = ""
    is_enabled: bool = True
    cooldown_minutes: int = 5  # Minimum time between alerts
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def evaluate(self, current_value: float) -> bool:
        """Evaluate if the rule should trigger an alert"""
        if not self.is_enabled:
            return False
            
        if self.condition_type == "greater_than":
            return current_value > self.threshold_value
        elif self.condition_type == "less_than":
            return current_value < self.threshold_value
        elif self.condition_type == "equals":
            return abs(current_value - self.threshold_value) < 0.001
        elif self.condition_type == "not_equals":
            return abs(current_value - self.threshold_value) >= 0.001
        
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "category": self.category,
            "metric_path": self.metric_path,
            "condition_type": self.condition_type,
            "threshold_value": self.threshold_value,
            "alert_level": self.alert_level.value,
            "alert_message": self.alert_message,
            "is_enabled": self.is_enabled,
            "cooldown_minutes": self.cooldown_minutes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


@dataclass
class DashboardAlert:
    """Main alert model - Requirements 4, 12"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    alert_type: AlertType = AlertType.SYSTEM_ERROR
    level: AlertLevel = AlertLevel.INFO
    title: str = ""
    message: str = ""
    source: str = ""  # "email_monitor", "celery_monitor", "system_monitor"
    
    # Time information
    triggered_at: datetime = field(default_factory=datetime.now)
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    
    # Status and counting
    status: AlertStatus = AlertStatus.ACTIVE
    occurrence_count: int = 1
    
    # Additional data
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Alert rule information
    rule_id: Optional[str] = None
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None
    
    # Notification tracking
    notifications_sent: List[str] = field(default_factory=list)  # List of channels notified
    last_notification_at: Optional[datetime] = None
    
    def is_active(self) -> bool:
        """Check if alert is currently active"""
        return self.status == AlertStatus.ACTIVE

    def is_critical(self) -> bool:
        """Check if alert is critical level"""
        return self.level == AlertLevel.CRITICAL

    def get_age_minutes(self) -> float:
        """Get alert age in minutes"""
        return (datetime.now() - self.triggered_at).total_seconds() / 60

    def acknowledge(self, acknowledged_by: str = "system") -> None:
        """Acknowledge the alert"""
        if self.status == AlertStatus.ACTIVE:
            self.status = AlertStatus.ACKNOWLEDGED
            self.acknowledged_at = datetime.now()
            self.metadata["acknowledged_by"] = acknowledged_by

    def resolve(self, resolved_by: str = "system") -> None:
        """Resolve the alert"""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.now()
        self.metadata["resolved_by"] = resolved_by

    def increment_occurrence(self) -> None:
        """Increment occurrence count for duplicate alerts"""
        self.occurrence_count += 1
        self.metadata["last_occurrence"] = datetime.now().isoformat()

    def add_notification(self, channel: NotificationChannel) -> None:
        """Track that notification was sent via channel"""
        if channel.value not in self.notifications_sent:
            self.notifications_sent.append(channel.value)
        self.last_notification_at = datetime.now()

    def should_escalate(self, escalation_minutes: int = 30) -> bool:
        """Check if alert should be escalated - Requirement 12.4"""
        if not self.is_active():
            return False
        return self.get_age_minutes() > escalation_minutes

    def get_priority_score(self) -> int:
        """Get priority score for sorting - Requirement 4.5"""
        base_score = {
            AlertLevel.INFO: 1,
            AlertLevel.WARNING: 2,
            AlertLevel.ERROR: 3,
            AlertLevel.CRITICAL: 4
        }.get(self.level, 1)
        
        # Increase priority for older alerts
        age_bonus = min(int(self.get_age_minutes() / 10), 5)
        
        # Increase priority for repeated occurrences
        occurrence_bonus = min(self.occurrence_count - 1, 3)
        
        return base_score * 10 + age_bonus + occurrence_bonus

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "alert_type": self.alert_type.value,
            "level": self.level.value,
            "title": self.title,
            "message": self.message,
            "source": self.source,
            "triggered_at": self.triggered_at.isoformat(),
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "status": self.status.value,
            "occurrence_count": self.occurrence_count,
            "metadata": self.metadata,
            "rule_id": self.rule_id,
            "threshold_value": self.threshold_value,
            "current_value": self.current_value,
            "notifications_sent": self.notifications_sent,
            "last_notification_at": self.last_notification_at.isoformat() if self.last_notification_at else None,
            "age_minutes": self.get_age_minutes(),
            "priority_score": self.get_priority_score()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DashboardAlert':
        """Create alert from dictionary"""
        alert = cls(
            id=data.get("id", str(uuid.uuid4())),
            alert_type=AlertType(data.get("alert_type", "system_error")),
            level=AlertLevel(data.get("level", "info")),
            title=data.get("title", ""),
            message=data.get("message", ""),
            source=data.get("source", ""),
            triggered_at=datetime.fromisoformat(data["triggered_at"]) if data.get("triggered_at") else datetime.now(),
            status=AlertStatus(data.get("status", "active")),
            occurrence_count=data.get("occurrence_count", 1),
            metadata=data.get("metadata", {}),
            rule_id=data.get("rule_id"),
            threshold_value=data.get("threshold_value"),
            current_value=data.get("current_value"),
            notifications_sent=data.get("notifications_sent", [])
        )
        
        if data.get("acknowledged_at"):
            alert.acknowledged_at = datetime.fromisoformat(data["acknowledged_at"])
        if data.get("resolved_at"):
            alert.resolved_at = datetime.fromisoformat(data["resolved_at"])
        if data.get("last_notification_at"):
            alert.last_notification_at = datetime.fromisoformat(data["last_notification_at"])
            
        return alert


@dataclass
class AlertSummary:
    """Summary of alerts for dashboard display"""
    total_active: int = 0
    total_acknowledged: int = 0
    total_resolved: int = 0
    critical_count: int = 0
    error_count: int = 0
    warning_count: int = 0
    info_count: int = 0
    
    # Recent activity
    new_alerts_last_hour: int = 0
    resolved_alerts_last_hour: int = 0
    
    # Top alert sources
    top_alert_sources: Dict[str, int] = field(default_factory=dict)
    
    # Alert trends
    alert_trend_24h: List[int] = field(default_factory=list)  # Hourly counts for last 24 hours
    
    timestamp: datetime = field(default_factory=datetime.now)

    def get_total_alerts(self) -> int:
        """Get total number of alerts"""
        return self.total_active + self.total_acknowledged + self.total_resolved

    def get_severity_distribution(self) -> Dict[str, int]:
        """Get distribution of alerts by severity"""
        return {
            "critical": self.critical_count,
            "error": self.error_count,
            "warning": self.warning_count,
            "info": self.info_count
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "total_active": self.total_active,
            "total_acknowledged": self.total_acknowledged,
            "total_resolved": self.total_resolved,
            "critical_count": self.critical_count,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "info_count": self.info_count,
            "new_alerts_last_hour": self.new_alerts_last_hour,
            "resolved_alerts_last_hour": self.resolved_alerts_last_hour,
            "top_alert_sources": self.top_alert_sources,
            "alert_trend_24h": self.alert_trend_24h,
            "timestamp": self.timestamp.isoformat(),
            "total_alerts": self.get_total_alerts(),
            "severity_distribution": self.get_severity_distribution()
        }


@dataclass
class NotificationConfig:
    """Configuration for notification channels"""
    channel: NotificationChannel
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)
    
    # Rate limiting
    max_notifications_per_hour: int = 10
    cooldown_minutes: int = 5
    
    # Filtering
    min_alert_level: AlertLevel = AlertLevel.WARNING
    alert_types: List[AlertType] = field(default_factory=list)  # Empty means all types
    
    # Retry configuration
    max_retries: int = 3
    retry_delay_seconds: int = 60
    
    def should_notify(self, alert: DashboardAlert) -> bool:
        """Check if this channel should notify for the given alert"""
        if not self.enabled:
            return False
            
        # Check minimum alert level
        level_priority = {
            AlertLevel.INFO: 1,
            AlertLevel.WARNING: 2,
            AlertLevel.ERROR: 3,
            AlertLevel.CRITICAL: 4
        }
        
        if level_priority.get(alert.level, 1) < level_priority.get(self.min_alert_level, 2):
            return False
            
        # Check alert types filter
        if self.alert_types and alert.alert_type not in self.alert_types:
            return False
            
        return True

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "channel": self.channel.value,
            "enabled": self.enabled,
            "config": self.config,
            "max_notifications_per_hour": self.max_notifications_per_hour,
            "cooldown_minutes": self.cooldown_minutes,
            "min_alert_level": self.min_alert_level.value,
            "alert_types": [at.value for at in self.alert_types],
            "max_retries": self.max_retries,
            "retry_delay_seconds": self.retry_delay_seconds
        }


@dataclass
class AlertHistory:
    """Historical alert data for trend analysis"""
    date: datetime
    total_alerts: int = 0
    critical_alerts: int = 0
    error_alerts: int = 0
    warning_alerts: int = 0
    info_alerts: int = 0
    avg_resolution_time_minutes: float = 0.0
    top_alert_types: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "date": self.date.isoformat(),
            "total_alerts": self.total_alerts,
            "critical_alerts": self.critical_alerts,
            "error_alerts": self.error_alerts,
            "warning_alerts": self.warning_alerts,
            "info_alerts": self.info_alerts,
            "avg_resolution_time_minutes": self.avg_resolution_time_minutes,
            "top_alert_types": self.top_alert_types
        }


# Utility functions for alert management
def merge_duplicate_alerts(alerts: List[DashboardAlert]) -> List[DashboardAlert]:
    """Merge duplicate alerts to avoid notification flooding - Requirement 12.5"""
    merged_alerts = {}
    
    for alert in alerts:
        # Create a key based on alert type, source, and message
        key = f"{alert.alert_type.value}_{alert.source}_{hash(alert.message)}"
        
        if key in merged_alerts:
            # Merge with existing alert
            existing = merged_alerts[key]
            existing.increment_occurrence()
            # Update with latest values if current alert is more recent
            if alert.triggered_at > existing.triggered_at:
                existing.current_value = alert.current_value
                existing.metadata.update(alert.metadata)
        else:
            merged_alerts[key] = alert
    
    return list(merged_alerts.values())


def prioritize_alerts(alerts: List[DashboardAlert]) -> List[DashboardAlert]:
    """Sort alerts by priority - Requirement 4.5"""
    return sorted(alerts, key=lambda a: a.get_priority_score(), reverse=True)


def filter_alerts_by_time(alerts: List[DashboardAlert], hours: int = 24) -> List[DashboardAlert]:
    """Filter alerts by time range"""
    cutoff_time = datetime.now() - timedelta(hours=hours)
    return [alert for alert in alerts if alert.triggered_at >= cutoff_time]


def get_alert_statistics(alerts: List[DashboardAlert]) -> Dict[str, Any]:
    """Get statistical summary of alerts"""
    if not alerts:
        return {
            "total": 0,
            "by_level": {},
            "by_type": {},
            "by_source": {},
            "avg_age_minutes": 0,
            "resolution_rate": 0
        }
    
    by_level = {}
    by_type = {}
    by_source = {}
    total_age = 0
    resolved_count = 0
    
    for alert in alerts:
        # Count by level
        level_key = alert.level.value
        by_level[level_key] = by_level.get(level_key, 0) + 1
        
        # Count by type
        type_key = alert.alert_type.value
        by_type[type_key] = by_type.get(type_key, 0) + 1
        
        # Count by source
        by_source[alert.source] = by_source.get(alert.source, 0) + 1
        
        # Calculate age
        total_age += alert.get_age_minutes()
        
        # Count resolved
        if alert.status == AlertStatus.RESOLVED:
            resolved_count += 1
    
    return {
        "total": len(alerts),
        "by_level": by_level,
        "by_type": by_type,
        "by_source": by_source,
        "avg_age_minutes": total_age / len(alerts),
        "resolution_rate": resolved_count / len(alerts) if alerts else 0
    }


# Pydantic models for API validation
class AlertRuleModel(BaseModel):
    """Pydantic model for alert rule validation"""
    name: str = Field(..., min_length=1, max_length=100, description="Rule name")
    category: str = Field(..., description="Alert category")
    metric_path: str = Field(..., description="Metric path to monitor")
    condition_type: str = Field(..., pattern="^(greater_than|less_than|equals|not_equals)$")
    threshold_value: float = Field(..., description="Threshold value for alert")
    alert_level: str = Field(..., pattern="^(info|warning|error|critical)$")
    alert_message: str = Field(..., min_length=1, max_length=500, description="Alert message template")
    is_enabled: bool = Field(default=True, description="Whether rule is enabled")
    cooldown_minutes: int = Field(default=5, ge=1, le=1440, description="Cooldown period in minutes")


class DashboardAlertModel(BaseModel):
    """Pydantic model for dashboard alert validation"""
    alert_type: str = Field(..., description="Type of alert")
    level: str = Field(..., pattern="^(info|warning|error|critical)$")
    title: str = Field(..., min_length=1, max_length=200, description="Alert title")
    message: str = Field(..., min_length=1, max_length=1000, description="Alert message")
    source: str = Field(..., min_length=1, max_length=50, description="Alert source system")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional alert metadata")
    threshold_value: Optional[float] = Field(None, description="Threshold value that triggered alert")
    current_value: Optional[float] = Field(None, description="Current metric value")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class NotificationConfigModel(BaseModel):
    """Pydantic model for notification configuration validation"""
    channel: str = Field(..., pattern="^(email|line|system_notification|webhook|sms)$")
    enabled: bool = Field(default=True, description="Whether channel is enabled")
    config: Dict[str, Any] = Field(default_factory=dict, description="Channel-specific configuration")
    max_notifications_per_hour: int = Field(default=10, ge=1, le=100, description="Rate limit")
    cooldown_minutes: int = Field(default=5, ge=1, le=60, description="Cooldown between notifications")
    min_alert_level: str = Field(default="warning", pattern="^(info|warning|error|critical)$")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    retry_delay_seconds: int = Field(default=60, ge=1, le=3600, description="Delay between retries")