"""
統一監控儀表板快取 API
提供快取管理和監控的 REST API 端點
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
import logging

from ..core.dashboard_cache_manager import get_cache_manager
from ..utils.dashboard_cache_utils import (
    get_cache_statistics, get_cache_health, optimize_cache_memory,
    invalidate_cache_by_pattern, CacheMetrics
)
from ..config.dashboard_cache_config import CacheNamespace


# 建立路由器
router = APIRouter(
    prefix="/cache",
    tags=["快取管理"],
    responses={404: {"description": "Not found"}}
)

logger = logging.getLogger(__name__)


# Pydantic 模型
class CacheSetRequest(BaseModel):
    """快取設定請求"""
    key: str
    value: Any
    ttl: Optional[int] = None


class CacheInvalidateRequest(BaseModel):
    """快取失效請求"""
    pattern: str


class CacheHealthResponse(BaseModel):
    """快取健康回應"""
    status: str
    health_score: int
    statistics: Dict[str, Any]
    recommendations: List[str]


# 依賴注入
def get_cache_manager_dependency():
    """獲取快取管理器依賴"""
    try:
        return get_cache_manager()
    except Exception as e:
        logger.error(f"無法獲取快取管理器: {e}")
        raise HTTPException(status_code=503, detail="快取服務不可用")


@router.get("/status")
async def get_cache_status(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取快取服務狀態"""
    try:
        statistics = get_cache_statistics()
        health = get_cache_health()
        
        return {
            "status": "success",
            "data": {
                "service_status": "running" if cache_manager.cache_service._running else "stopped",
                "statistics": statistics,
                "health": health,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"獲取快取狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_cache_statistics_endpoint(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取快取統計資料"""
    try:
        statistics = get_cache_statistics()
        
        return {
            "status": "success",
            "data": statistics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取快取統計失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def get_cache_health_endpoint(
    cache_manager = Depends(get_cache_manager_dependency)
) -> CacheHealthResponse:
    """獲取快取健康狀態"""
    try:
        health = get_cache_health()
        
        return CacheHealthResponse(
            status=health["status"],
            health_score=health["health_score"],
            statistics=health["statistics"],
            recommendations=health["recommendations"]
        )
        
    except Exception as e:
        logger.error(f"獲取快取健康狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_cache_metrics(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取快取效能指標"""
    try:
        metrics_collector = CacheMetrics()
        metrics = metrics_collector.collect_metrics()
        performance_metrics = metrics_collector.get_performance_metrics()
        
        return {
            "status": "success",
            "data": {
                "metrics": metrics,
                "performance": performance_metrics
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取快取指標失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/keys")
async def get_cache_keys(
    pattern: Optional[str] = Query(None, description="鍵模式過濾"),
    limit: int = Query(100, description="返回數量限制"),
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取快取鍵列表"""
    try:
        keys = cache_manager.cache_service.get_keys(pattern)
        
        # 限制返回數量
        if len(keys) > limit:
            keys = keys[:limit]
            truncated = True
        else:
            truncated = False
        
        return {
            "status": "success",
            "data": {
                "keys": keys,
                "total_count": len(keys),
                "truncated": truncated,
                "pattern": pattern
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取快取鍵失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/entry/{key}")
async def get_cache_entry(
    key: str,
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取特定快取項目"""
    try:
        value = cache_manager.cache_service.get(key)
        exists = cache_manager.cache_service.exists(key)
        
        if not exists:
            raise HTTPException(status_code=404, detail="快取項目不存在")
        
        # 獲取項目詳細資訊
        entry_info = None
        if key in cache_manager.cache_service._cache:
            entry = cache_manager.cache_service._cache[key]
            entry_info = {
                "key": entry.key,
                "size_bytes": entry.size_bytes,
                "access_count": entry.access_count,
                "created_at": datetime.fromtimestamp(entry.created_at).isoformat(),
                "last_accessed": datetime.fromtimestamp(entry.last_accessed).isoformat(),
                "expires_at": datetime.fromtimestamp(entry.expires_at).isoformat() if entry.expires_at else None,
                "is_expired": entry.is_expired()
            }
        
        return {
            "status": "success",
            "data": {
                "key": key,
                "value": value,
                "exists": exists,
                "entry_info": entry_info
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取快取項目失敗 {key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/set")
async def set_cache_entry(
    request: CacheSetRequest,
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """設定快取項目"""
    try:
        success = cache_manager.cache_service.set(
            request.key, 
            request.value, 
            request.ttl
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="快取設定失敗")
        
        return {
            "status": "success",
            "message": "快取項目已設定",
            "data": {
                "key": request.key,
                "ttl": request.ttl,
                "set_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"設定快取項目失敗 {request.key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/entry/{key}")
async def delete_cache_entry(
    key: str,
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """刪除快取項目"""
    try:
        success = cache_manager.cache_service.delete(key)
        
        if not success:
            raise HTTPException(status_code=404, detail="快取項目不存在")
        
        return {
            "status": "success",
            "message": "快取項目已刪除",
            "data": {
                "key": key,
                "deleted_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刪除快取項目失敗 {key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/invalidate")
async def invalidate_cache(
    request: CacheInvalidateRequest,
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """根據模式失效快取"""
    try:
        invalidated_count = await invalidate_cache_by_pattern(request.pattern)
        
        return {
            "status": "success",
            "message": f"已失效 {invalidated_count} 個快取項目",
            "data": {
                "pattern": request.pattern,
                "invalidated_count": invalidated_count,
                "invalidated_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"失效快取失敗 {request.pattern}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/invalidate/namespace/{namespace}")
async def invalidate_namespace(
    namespace: str,
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """失效整個命名空間的快取"""
    try:
        # 驗證命名空間
        valid_namespaces = [
            CacheNamespace.METRICS,
            CacheNamespace.EMAIL_METRICS,
            CacheNamespace.CELERY_METRICS,
            CacheNamespace.SYSTEM_METRICS,
            CacheNamespace.FILE_METRICS,
            CacheNamespace.BUSINESS_METRICS,
            CacheNamespace.ALERTS,
            CacheNamespace.TRENDS,
            CacheNamespace.BUSINESS,
            CacheNamespace.SYSTEM_INFO,
            CacheNamespace.CONFIG
        ]
        
        if namespace not in valid_namespaces:
            raise HTTPException(
                status_code=400, 
                detail=f"無效的命名空間。有效選項: {', '.join(valid_namespaces)}"
            )
        
        invalidated_count = await cache_manager.invalidate_namespace(namespace)
        
        return {
            "status": "success",
            "message": f"已失效命名空間 '{namespace}' 的 {invalidated_count} 個快取項目",
            "data": {
                "namespace": namespace,
                "invalidated_count": invalidated_count,
                "invalidated_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"失效命名空間快取失敗 {namespace}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/clear")
async def clear_cache(
    confirm: bool = Query(False, description="確認清空所有快取"),
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """清空所有快取"""
    if not confirm:
        raise HTTPException(
            status_code=400, 
            detail="請設定 confirm=true 參數以確認清空所有快取"
        )
    
    try:
        # 獲取清空前的統計
        stats_before = cache_manager.cache_service.get_statistics()
        entries_before = stats_before.total_entries
        
        # 清空快取
        cache_manager.cache_service.clear()
        
        return {
            "status": "success",
            "message": f"已清空所有快取，移除 {entries_before} 個項目",
            "data": {
                "entries_cleared": entries_before,
                "cleared_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"清空快取失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimize")
async def optimize_cache(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """最佳化快取記憶體使用"""
    try:
        # 獲取最佳化前的統計
        stats_before = cache_manager.cache_service.get_statistics()
        
        # 執行最佳化
        await optimize_cache_memory()
        
        # 獲取最佳化後的統計
        stats_after = cache_manager.cache_service.get_statistics()
        
        return {
            "status": "success",
            "message": "快取記憶體最佳化完成",
            "data": {
                "before": {
                    "total_entries": stats_before.total_entries,
                    "memory_usage_mb": round(stats_before.memory_usage_mb, 2)
                },
                "after": {
                    "total_entries": stats_after.total_entries,
                    "memory_usage_mb": round(stats_after.memory_usage_mb, 2)
                },
                "optimized_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"快取最佳化失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/warmup")
async def warmup_cache(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """預熱快取"""
    try:
        await cache_manager.warm_up_cache()
        
        return {
            "status": "success",
            "message": "快取預熱完成",
            "data": {
                "warmed_up_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"快取預熱失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_cache_config(
    cache_manager = Depends(get_cache_manager_dependency)
) -> Dict[str, Any]:
    """獲取快取配置"""
    try:
        config_dict = cache_manager.config.to_dict()
        
        return {
            "status": "success",
            "data": {
                "config": config_dict,
                "service_info": {
                    "max_size": cache_manager.cache_service.max_size,
                    "default_ttl": cache_manager.cache_service.default_ttl,
                    "cleanup_interval": cache_manager.cache_service.cleanup_interval,
                    "max_memory_bytes": cache_manager.cache_service.max_memory_bytes,
                    "running": cache_manager.cache_service._running
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取快取配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 生命週期管理
def initialize_cache_api():
    """初始化快取 API"""
    try:
        cache_manager = get_cache_manager()
        logger.info("快取 API 已初始化")
    except Exception as e:
        logger.error(f"快取 API 初始化失敗: {e}")


def shutdown_cache_api():
    """關閉快取 API"""
    try:
        logger.info("快取 API 已關閉")
    except Exception as e:
        logger.error(f"快取 API 關閉失敗: {e}")