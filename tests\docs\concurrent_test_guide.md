# FT Summary 並發處理測試指南

## 測試環境準備

### 1. 資料庫狀態
- 目前資料庫中有 17 封郵件
- 其中 16 封是 FT Hold Lot Report 郵件
- 15 封處於 pending 狀態，可用於測試

### 2. 測試腳本說明

#### `add_test_emails_to_db.py`
- 用於添加測試郵件到資料庫
- 可以指定要添加的郵件數量
- 自動生成隨機的 PD、LOT、良率等資料

```bash
# 添加 10 封測試郵件
echo 10 | python add_test_emails_to_db.py
```

#### `reset_email_parse_status.py`
- 重置郵件的解析狀態，便於重複測試
- 顯示郵件統計資訊

```bash
# 顯示郵件狀態
python reset_email_parse_status.py show

# 重置所有 FT 郵件為 pending 狀態
echo y | python reset_email_parse_status.py
```

#### `test_concurrent_processing.py`
- 主要的並發測試腳本
- 模擬多個工程師同時處理郵件
- 包含三種測試場景：2、3、5 個用戶並發

```bash
# 執行並發測試
python test_concurrent_processing.py
```

## 測試步驟

### 1. 啟動服務
```bash
# 確保虛擬環境已啟動
venv_win_3_11_12\Scripts\activate

# 啟動整合服務
python start_integrated_services.py
```

### 2. 準備測試資料
```bash
# 檢查當前郵件狀態
python reset_email_parse_status.py show

# 如需要，添加更多測試郵件
echo 20 | python add_test_emails_to_db.py

# 重置郵件狀態以便測試
echo y | python reset_email_parse_status.py
```

### 3. 執行並發測試
```bash
# 執行完整的並發測試
python test_concurrent_processing.py
```

## 測試場景

### 場景 1: 小規模測試
- 2 個用戶同時處理郵件
- 測試基本的並發能力

### 場景 2: 中規模測試  
- 3 個用戶同時處理郵件
- 測試一般工作負載

### 場景 3: 大規模測試
- 5 個用戶同時處理郵件
- 測試高負載情況

## 預期結果

1. **郵件解析**
   - 每個用戶能夠獨立解析郵件
   - 解析時間通常在 0.5-2 秒之間
   - 不會出現資料衝突

2. **FT Summary 生成**
   - 用戶可以選擇多封郵件生成摘要
   - 生成時間根據郵件數量而定

3. **並發處理**
   - 系統能夠處理多個並發請求
   - 資料庫事務正確處理
   - API 響應時間合理

## 監控要點

1. **性能指標**
   - 平均解析時間
   - 總處理時間
   - 成功率

2. **錯誤處理**
   - 解析失敗的原因
   - API 錯誤響應
   - 資料庫鎖定問題

3. **用戶體驗**
   - 響應時間是否可接受
   - 並發時是否有明顯延遲
   - 錯誤訊息是否清晰

## 故障排[EXCEPT_CHAR]

### 常見問題

1. **API 服務未啟動**
   ```
   ✗ 無法連接到 API 服務
   ```
   解決方案：執行 `python start_integrated_services.py`

2. **資料庫鎖定**
   ```
   database is locked
   ```
   解決方案：減少並發數量或優化資料庫查詢

3. **解析失敗**
   - 檢查郵件格式是否正確
   - 查看 parse_error 欄位的錯誤訊息

## 測試報告

測試完成後，檢查以下內容：

1. **成功率統計**
   - 郵件解析成功率
   - FT Summary 生成成功率

2. **性能統計**
   - 平均處理時間
   - 最大/最小處理時間
   - 並發處理能力

3. **用戶活動統計**
   - 每個用戶的操作次數
   - 成功率和平均時間

## 進階測試

如需要更複雜的測試場景，可以修改 `test_concurrent_processing.py`：

1. 增加用戶數量
2. 調整處理延遲
3. 模擬錯誤情況
4. 測試長時間運行

## 清理測試資料

測試完成後，可以清理測試資料：

```bash
# 備份當前資料庫
copy email_inbox.db email_inbox.db.backup

# 或刪[EXCEPT_CHAR]測試郵件（謹慎操作）
# sqlite3 email_inbox.db "DELETE FROM emails WHERE sender='<EMAIL>'"
```