#!/usr/bin/env python3
"""
生產環境 Redis + Celery 啟動腳本
僅在需要真正分散式任務處理時使用

📋 使用場景：
  ✅ 生產環境部署 - 需要真正的異步處理
  ✅ 高負載場景 - 大量並發任務
  ✅ 多服務器部署 - Worker 分佈在不同機器
  ❌ 開發和測試 - 當前的內存模式已足夠

🔧 功能說明：
  1. 🔴 啟動 Redis 服務器 (Port 6379)
  2. 👷 啟動 Celery Worker (多進程)
  3. 🌸 啟動 Flower 監控 (Port 5566)
  4. 🚀 啟動主應用服務
  5. 🛑 統一停止所有服務

⚠️  注意事項：
  - 會覆蓋當前的內存模式配置
  - 需要安裝 Redis 服務器
  - 適合 Windows/Linux/macOS 環境
  - 停止後需重新啟動 start_integrated_services.py 恢復內存模式

🎯 替代方案：
  - 開發模式：python start_integrated_services.py
  - Windows 快速啟動：start_production_mode.bat
"""

import os
import sys
import time
import subprocess
import signal
import psutil
from pathlib import Path

def print_banner():
    """顯示啟動橫幅"""
    print("=" * 70)
    print("🔴 生產環境 Redis + Celery 系統啟動器")
    print("=" * 70)
    print("📋 將啟動以下服務:")
    print("  1. 🔴 Redis 服務器 (Port 6379)")
    print("  2. 👷 Celery Worker (多進程)")
    print("  3. 🌸 Flower 監控 (Port 5566)")
    print("  4. 🚀 主應用服務")
    print("=" * 70)
    print("⚠️  注意：這會覆蓋當前的內存模式配置")
    print("=" * 70)

def check_redis():
    """檢查 Redis 是否可用"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 連接成功")
        return True
    except Exception as e:
        print(f"❌ Redis 連接失敗: {e}")
        return False

def check_port_available(port):
    """檢查端口是否可用"""
    for conn in psutil.net_connections():
        if conn.laddr.port == port:
            return False
    return True

def set_production_mode():
    """設置生產模式環境變數"""
    os.environ['USE_MEMORY_BROKER'] = 'false'
    os.environ['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
    os.environ['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'
    print("🔧 已設置生產模式環境變數")

def start_redis():
    """啟動 Redis 服務"""
    if not check_port_available(6379):
        print("⚠️  端口 6379 已被使用，假設 Redis 已在運行")
        return None
    
    print("🔴 正在啟動 Redis 服務器...")
    try:
        process = subprocess.Popen(
            ["redis-server", "--port", "6379", "--daemonize", "no"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        time.sleep(3)
        
        if check_redis():
            print("✅ Redis 啟動成功")
            return process
        else:
            print("❌ Redis 啟動失敗")
            return None
    except FileNotFoundError:
        print("❌ 找不到 redis-server 命令，請確保 Redis 已安裝")
        return None

def start_celery_worker():
    """啟動 Celery Worker"""
    print("👷 正在啟動 Celery Worker...")
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "celery", 
            "-A", "src.tasks.celery_app", 
            "worker", 
            "--loglevel=info", 
            "--concurrency=4",
            "--pool=prefork"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(5)
        print("✅ Celery Worker 啟動成功")
        return process
    except Exception as e:
        print(f"❌ Celery Worker 啟動失敗: {e}")
        return None

def start_flower():
    """啟動 Flower 監控"""
    if not check_port_available(5566):
        print("⚠️  端口 5566 已被使用，跳過 Flower 啟動")
        return None
    
    print("🌸 正在啟動 Flower 監控...")
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "celery", 
            "-A", "src.tasks.celery_app", 
            "flower", 
            "--port=5566"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(3)
        print("✅ Flower 監控啟動成功")
        return process
    except Exception as e:
        print(f"❌ Flower 啟動失敗: {e}")
        return None

def start_main_app():
    """啟動主應用"""
    print("🚀 正在啟動主應用...")
    try:
        process = subprocess.Popen([
            sys.executable, "start_integrated_services.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(5)
        print("✅ 主應用啟動成功")
        return process
    except Exception as e:
        print(f"❌ 主應用啟動失敗: {e}")
        return None

def main():
    """主函數"""
    print_banner()
    
    # 確認用戶意圖
    response = input("是否要切換到生產模式？(y/N): ")
    if response.lower() != 'y':
        print("❌ 取消啟動")
        return
    
    processes = []
    
    try:
        # 設置生產模式
        set_production_mode()
        
        # 啟動 Redis
        redis_process = start_redis()
        if redis_process:
            processes.append(("Redis", redis_process))
        elif not check_redis():
            print("❌ Redis 服務不可用，無法繼續")
            return
        
        # 啟動 Celery Worker
        worker_process = start_celery_worker()
        if worker_process:
            processes.append(("Celery Worker", worker_process))
        
        # 啟動 Flower
        flower_process = start_flower()
        if flower_process:
            processes.append(("Flower", flower_process))
        
        # 啟動主應用
        app_process = start_main_app()
        if app_process:
            processes.append(("Main App", app_process))
        
        print("\n🎉 生產環境啟動完成！")
        print("📊 服務狀態:")
        print("  🔴 Redis:        localhost:6379")
        print("  🌸 Flower 監控:  http://localhost:5566")
        print("  🚀 主應用:       http://localhost:5555")
        print("  📱 網路瀏覽器:    http://localhost:5555/network/ui")
        print("\n按 Ctrl+C 停止所有服務...")
        
        # 等待用戶中斷
        try:
            while True:
                time.sleep(1)
                # 檢查進程狀態
                for name, process in processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name} 進程已停止")
        except KeyboardInterrupt:
            print("\n🛑 正在停止所有服務...")
            
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
    
    finally:
        # 停止所有進程
        for name, process in processes:
            try:
                print(f"🛑 停止 {name}...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"🔪 強制終止 {name}...")
                process.kill()
            except Exception as e:
                print(f"❌ 停止 {name} 時發生錯誤: {e}")
        
        print("✅ 所有服務已停止")
        print("💡 如要恢復內存模式，請重新啟動 start_integrated_services.py")

if __name__ == "__main__":
    main()
