/**
 * EQC 處理器模組 v2.1 (2025-06-23 20:20)
 * 處理完整的 EQC 工作流程，包括區間檢測、雙重搜尋和報告生成
 * 修復: 添加 executeStep4 方法和資料驗證邏輯
 */

class EQCProcessor {
    constructor() {
        this.isProcessing = false;
        this.currentResults = null;
        this.lastAutoExpandId = null; // 記錄上次自動展開的處理ID，避免重複自動展開
        this.version = "2.1"; // 版本標識
        
        // 🔥 新增進度監控和取消功能
        this.processingStartTime = null;
        this.estimatedDuration = 1200; // 預估處理時間(秒)
        this.maxTimeout = 300; // 最大超時時間(秒)
        this.processingInterval = null;
        this.cancelRequested = false;
        
        this.processingSteps = [
            { name: 'Step 1/4', description: '🔥 執行繁中資料夾處理 - EQCBin1FinalProcessor.process_complete_eqc_integration()', percentage: 25 },
            { name: 'Step 2/4', description: '📊 程式碼區間檢測與雙重搜尋 - StandardEQCProcessor.process_from_stage2_only()', percentage: 50 },
            { name: 'Step 3/4', description: '🔍 分析真實 EQCTOTALDATA.xlsx 資料...', percentage: 75 },
            { name: 'Step 4/4', description: '🔧 前端整合顯示結果完成...', percentage: 100 }
        ];
    }
    
    /**
     * 執行完整的 EQC 工作流程
     * @param {string} folderPath - 資料夾路徑
     * @param {Object} options - 處理選項
     */
    async processCompleteWorkflow(folderPath, options = {}) {
        if (this.isProcessing) {
            StatusManager.showToast('已有處理程序在執行中，請等待完成', 'warning');
            return;
        }
        
        // 🔥 初始化進度監控
        this.isProcessing = true;
        this.cancelRequested = false;
        this.processingStartTime = Date.now();
        
        console.log('🔥 開始執行完整 EQC 工作流程 (防無限迴圈模式)...');
        console.log('📁 處理路徑:', folderPath);
        
        // 啟動進度監控
        this.startProgressMonitoring();
        
        try {
            // 驗證路徑
            if (!folderPath || folderPath.trim() === '') {
                throw new Error('請輸入有效的資料夾路徑');
            }
            
            // 開始處理流程
            progressDisplay.startProcessing();
            
            // Step 1: /api/process_online_eqc (mode='1') - 繁中資料夾處理
            await this.executeStep1(folderPath, options);

            // Step 2: /api/process_eqc_advanced - 程式碼區間檢測與雙重搜尋
            await this.executeStep2(folderPath, options);

            // Step 3: /api/analyze_eqc_real_data - 分析真實數據
            await this.executeStep3(folderPath);

            // Step 4: 前端整合顯示結果
            console.log('🔧 檢查 executeStep4 方法:', typeof this.executeStep4);
            if (typeof this.executeStep4 !== 'function') {
                throw new Error('executeStep4 方法不存在，請清除瀏覽器快取後重新載入頁面');
            }
            await this.executeStep4();

            // 顯示結果
            await this.displayResults();
            
            console.log('✅ EQC 工作流程完成');
            
        } catch (error) {
            console.error('❌ EQC 處理失敗:', error);
            this.handleProcessingError(error);
        } finally {
            this.isProcessing = false;
            this.stopProgressMonitoring();
        }
    }
    
    /**
     * 執行步驟 1: /api/process_online_eqc (mode='1') - 繁中資料夾處理
     * EQCBin1FinalProcessor.process_complete_eqc_integration()
     * @param {string} folderPath - 資料夾路徑
     * @param {Object} options - 處理選項
     */
    async executeStep1(folderPath, options) {
        console.log('🔥 Step 1: 執行繁中資料夾處理 - EQCBin1FinalProcessor.process_complete_eqc_integration()');
        
        // 更新進度
        const step = this.processingSteps[0];
        progressDisplay.updateProgress(step.name, step.description, step.percentage, 'processing');
        
        try {
            // 🔥 檢查是否已被取消
            if (this.cancelRequested) {
                throw new Error('處理已被取消');
            }
            
            console.log('🔄 呼叫 /api/process_online_eqc (mode=1)...');
            const eqcResult = await ApiClient.processOnlineEqc(folderPath, 1); // mode=1: EQC BIN1 整合統計處理

            if (eqcResult.status !== 'success') {
                throw new Error(eqcResult.message || 'Step 1 - 繁中資料夾處理失敗');
            }

            // 保存 Step 1 結果
            this.currentResults = {
                step1_online_eqc: eqcResult,
                processing_mode: 'four_step_workflow',
                folder_path: folderPath  // 🔧 保存原始資料夾路徑供後續使用
            };
            
            console.log('✅ Step 1 完成 - 繁中資料夾處理');
            console.log('📋 Step 1 結果:', eqcResult);

        } catch (error) {
            console.error('❌ Step 1 失敗:', error);
            throw new Error(`Step 1 失敗: ${error.message}`);
        }
        
        // 短暫延遲以顯示進度
        await Utils.sleep(500);
    }
    
    /**
     * 執行步驟 2: /api/process_eqc_advanced - 程式碼區間檢測與雙重搜尋
     * StandardEQCProcessor.process_from_stage2_only()
     * @param {string} folderPath - 資料夾路徑
     * @param {Object} options - 處理選項
     */
    async executeStep2(folderPath, options) {
        console.log('📊 Step 2: 程式碼區間檢測與雙重搜尋 - StandardEQCProcessor.process_from_stage2_only()');

        // 更新進度
        const step = this.processingSteps[1];
        progressDisplay.updateProgress(step.name, step.description, step.percentage, 'processing');

        try {
            // 🔥 檢查是否已被取消
            if (this.cancelRequested) {
                throw new Error('處理已被取消');
            }

            // 準備請求參數
            const requestData = {
                folder_path: folderPath,
                include_step123: options.includeStep123 !== false,
                ...this.getCodeRegionSettings()
            };

            console.log('🔄 呼叫 /api/process_eqc_advanced...');
            const advancedResult = await ApiClient.processEqcAdvanced(folderPath, requestData);

            if (advancedResult.status === 'success') {
                // 將進階處理結果合併到當前結果中
                this.currentResults.step2_advanced = advancedResult;
                console.log('✅ Step 2 完成 - 程式碼區間檢測與雙重搜尋');
                console.log('📊 Step 2 結果:', advancedResult);
            } else {
                throw new Error(advancedResult.message || 'Step 2 - 程式碼區間檢測失敗');
            }

        } catch (error) {
            console.error('❌ Step 2 失敗:', error);
            throw new Error(`Step 2 失敗: ${error.message}`);
        }
        
        // 短暫延遲以顯示進度
        await Utils.sleep(500);
    }
    
    /**
     * 執行步驟 3: /api/analyze_eqc_real_data - 分析真實數據
     */
    async executeStep3(folderPath) {
        console.log('📊 執行步驟 3: 分析真實數據 [VERSION 2.0]');

        // 更新進度
        const step = this.processingSteps[2];
        progressDisplay.updateProgress(step.name, step.description, step.percentage, 'processing');

        try {
            // 🔥 檢查是否已被取消
            if (this.cancelRequested) {
                throw new Error('處理已被取消');
            }

            // 調用 analyze_eqc_real_data API
            console.log('🔧 調用 analyze_eqc_real_data API...');

            const analyzeResult = await apiClient.analyzeEqcRealData(folderPath);

            if (analyzeResult.status === 'success') {
                // 將結果存儲到 currentResults
                this.currentResults.step3_real_data = analyzeResult;

                console.log('✅ Step 3 分析真實數據成功');
                console.log('📊 Site 統計數據:', analyzeResult.summary_data?.site_stats);

                // 更新進度為完成
                progressDisplay.updateProgress(step.name, '真實數據分析完成', step.percentage, 'success');
            } else {
                throw new Error(`真實數據分析失敗: ${analyzeResult.message || '未知錯誤'}`);
            }

        } catch (error) {
            console.error('❌ Step 3 失敗:', error);
            const step = this.processingSteps[2];
            progressDisplay.updateProgress(step.name, `失敗: ${error.message}`, step.percentage, 'error');
            throw new Error(`Step 3 失敗: ${error.message}`);
        }
    }

            // 檢查是否有必要的檔案
            const step1Data = this.currentResults.step1_online_eqc?.data;
            if (step1Data?.eqc_total_file) {
                console.log('✅ EQCTOTALDATA.csv 已生成:', step1Data.eqc_total_file);
            }
            if (step1Data?.eqc_raw_file) {
                console.log('✅ EQCTOTALDATA_RAW.csv 已生成:', step1Data.eqc_raw_file);
            }

            // 短暫延遲以顯示整合過程
            await Utils.sleep(500);

            console.log('✅ 步驟 3 完成 - 資料驗證與整合');
            console.log('📋 驗證後的處理結果:', this.currentResults);

        } catch (error) {
            console.error('❌ 步驟 3 失敗:', error);
            throw new Error(`最終整合失敗: ${error.message}`);
        }
    }
    
    /**
     * 執行步驟 4: 前端整合顯示結果
     */
    async executeStep4() {
        console.log('🎯 執行步驟 4: 前端整合顯示結果');

        // 更新進度（如果有第4步的配置）
        if (this.processingSteps[3]) {
            const step = this.processingSteps[3];
            progressDisplay.updateProgress(step.name, step.description, step.percentage, 'processing');
        }

        try {
            // 🔥 檢查是否已被取消
            if (this.cancelRequested) {
                throw new Error('處理已被取消');
            }

            // 整理結果摘要
            const summary = this.generateResultSummary();
            
            // 更新結果摘要到 currentResults
            this.currentResults.summary = summary;
            
            console.log('✅ 步驟 4 完成 - 前端整合完成');
            console.log('📊 結果摘要:', summary);

        } catch (error) {
            console.error('❌ 步驟 4 失敗:', error);
            throw new Error(`前端整合失敗: ${error.message}`);
        }
    }
    
    /**
     * 生成結果摘要
     */
    generateResultSummary() {
        const step1 = this.currentResults.step1_online_eqc?.data;
        const step2 = this.currentResults.step2_advanced?.data;
        
        return {
            processing_mode: this.currentResults.processing_mode || 'four_step_workflow',
            files_generated: {
                eqc_total: step1?.eqc_total_file || null,
                eqc_raw: step1?.eqc_raw_file || null,
                excel_download: step1?.eqctotaldata_download_path || null
            },
            hyperlink_count: step1?.hyperlink_count || 0,
            processing_time: {
                step1: this.currentResults.step1_online_eqc?.processing_time || 0,
                step2: this.currentResults.step2_advanced?.processing_time || 0,
                total: (this.currentResults.step1_online_eqc?.processing_time || 0) + 
                       (this.currentResults.step2_advanced?.processing_time || 0)
            },
            code_regions: {
                main_start: step2?.main_region_start || null,
                main_end: step2?.main_region_end || null,
                backup_start: step2?.backup_region_start || null,
                backup_end: step2?.backup_region_end || null,
                search_method: step2?.search_method || null,
                match_rate: step2?.match_rate || null
            },
            success: true
        };
    }
    
    /**
     * 獲取 CODE 區間設定
     * @returns {Object} CODE 區間設定
     */
    getCodeRegionSettings() {
        const mainStart = DOMManager.getValue('mainStart');
        const mainEnd = DOMManager.getValue('mainEnd');
        const backupStart = DOMManager.getValue('backupStart');
        const backupEnd = DOMManager.getValue('backupEnd');
        
        const settings = {};
        
        // 主要區間
        if (mainStart && mainEnd) {
            const start = parseInt(mainStart);
            const end = parseInt(mainEnd);
            if (start > 0 && end > 0 && end >= start) {
                settings.main_region = {
                    start_column: start,
                    end_column: end
                };
            }
        }
        
        // 備用區間
        if (backupStart && backupEnd) {
            const start = parseInt(backupStart);
            const end = parseInt(backupEnd);
            if (start > 0 && end > 0 && end >= start) {
                settings.backup_region = {
                    start_column: start,
                    end_column: end
                };
            }
        }
        
        console.log('🔧 CODE 區間設定:', settings);
        return settings;
    }
    
    /**
     * 顯示處理結果
     */
    async displayResults() {
        if (!this.currentResults) {
            console.warn('⚠️ 沒有處理結果可顯示');
            return;
        }

        // 完成進度顯示
        progressDisplay.completeProcessing('EQC 處理完成！點擊查看詳細結果');

        console.log('📋 顯示處理結果');
        console.log('📊 處理結果數據:', this.currentResults);

        // 渲染詳細面板 (包含自動更新 CODE 區間和讀取真實數據)
        await detailPanel.renderDetailContent(this.currentResults);

        // 🔧 只在處理完成後的第一次自動展開，避免覆蓋用戶的收合操作
        // 使用一個標記來確保每次處理只自動展開一次
        const currentProcessingId = `${this.currentResults.folder_path}_${this.currentResults.processing_time}`;

        if (!this.lastAutoExpandId || this.lastAutoExpandId !== currentProcessingId) {
            // 這是新的處理結果，可以自動展開
            setTimeout(() => {
                if (!detailPanel.isExpanded) {
                    // 🔧 使用 toggle() 確保狀態同步，而不是直接調用 expand()
                    detailPanel.isExpanded = false; // 確保狀態正確
                    detailPanel.toggle(); // 統一的狀態管理
                    console.log('📖 自動展開詳細面板顯示新的處理結果');
                }
            }, 500);

            // 記錄這次的自動展開ID，防止重複展開
            this.lastAutoExpandId = currentProcessingId;
            console.log('📖 設置自動展開ID:', currentProcessingId);
        } else {
            console.log('📖 保持詳細面板當前狀態（避免覆蓋用戶操作）');
        }

        // 更新今日處理記錄
        this.updateTodayRecords();

        // 顯示成功提示
        StatusManager.showToast('EQC 處理完成！CODE 區間已自動設定', 'success');

        // 顯示下載對話框（恢復舊版下載方式）
        this.showDownloadModal();
    }
    
    /**
     * 顯示下載對話框
     */
    showDownloadModal() {
        if (!this.currentResults) {
            console.warn('⚠️ 沒有處理結果，無法顯示下載對話框');
            return;
        }

        try {
            // 🔧 修復：從 Step 1 結果中獲取正確的路徑資訊
            const step1Data = this.currentResults.step1_online_eqc?.data;
            if (!step1Data) {
                console.error('❌ 沒有 Step 1 數據，無法顯示下載對話框');
                return;
            }

            // 從 Step 1 數據中直接獲取完整路徑
            let eqctotaldataPath = step1Data.eqctotaldata_download_path;
            let eqctotaldataRawPath = step1Data.eqctotaldata_raw_download_path;
            
            // 提取資料夾路徑（用於備用邏輯）
            let folderPath = null;
            if (eqctotaldataPath) {
                // 從完整路徑中提取資料夾路徑
                folderPath = eqctotaldataPath.replace(/[\/\\][^\/\\]*$/, '');
            }

            // 備用邏輯：如果沒有完整路徑，嘗試構建路徑
            if (!eqctotaldataPath && folderPath) {
                eqctotaldataPath = `${folderPath}/EQCTOTALDATA.xlsx`;
            }
            if (!eqctotaldataRawPath && folderPath) {
                eqctotaldataRawPath = `${folderPath}/EQCTOTALDATA_RAW.csv`;
            }

            console.log('📥 顯示下載對話框');
            console.log('📁 資料夾路徑:', folderPath);
            console.log('📊 EQCTOTALDATA.xlsx 路徑:', eqctotaldataPath);
            console.log('📄 EQCTOTALDATA_RAW.csv 路徑:', eqctotaldataRawPath);

            // 調用下載組件的函數
            if (typeof showDownloadButtons === 'function') {
                showDownloadButtons(folderPath, eqctotaldataPath, eqctotaldataRawPath);
            } else {
                console.error('❌ showDownloadButtons 函數未載入');
                StatusManager.showToast('下載功能載入失敗', 'error');
            }

        } catch (error) {
            console.error('❌ 顯示下載對話框失敗:', error);
            StatusManager.showToast(`顯示下載對話框失敗: ${error.message}`, 'error');
        }
    }

    /**
     * 更新今日處理記錄
     */
    async updateTodayRecords() {
        try {
            console.log('📅 正在載入今日處理記錄...');

            // 顯示載入狀態
            const emptyElement = DOMManager.get('todayRecordsEmpty');
            const listElement = DOMManager.get('todayRecordsList');

            if (emptyElement) {
                emptyElement.innerHTML = '<div style="text-align: center; color: #6c757d;"><i class="fas fa-spinner fa-spin"></i> 載入中...</div>';
                DOMManager.show('todayRecordsEmpty');
                DOMManager.hide('todayRecordsList');
            }

            const records = await ApiClient.getTodayProcessedFiles();
            this.renderTodayRecords(records);

            console.log('✅ 今日處理記錄載入完成');
        } catch (error) {
            console.warn('⚠️ 無法載入今日處理記錄:', error);

            // 顯示錯誤狀態
            const emptyElement = DOMManager.get('todayRecordsEmpty');
            if (emptyElement) {
                emptyElement.innerHTML = '<div style="text-align: center; color: #e74c3c;"><i class="fas fa-exclamation-triangle"></i> 載入失敗</div>';
                DOMManager.show('todayRecordsEmpty');
            }
        }
    }
    
    /**
     * 渲染今日處理記錄
     * @param {Object} records - 處理記錄
     */
    renderTodayRecords(records) {
        console.log('📋 渲染今日處理記錄，收到數據:', records);

        const emptyElement = DOMManager.get('todayRecordsEmpty');
        const listElement = DOMManager.get('todayRecordsList');

        // 檢查數據格式 - 支援多種格式
        let processedFiles = [];

        if (records && records.data && records.data.processed_files) {
            // 後端 API 格式: {status: "success", data: {processed_files: [...]}}
            processedFiles = records.data.processed_files;
            console.log('✅ 使用後端 API 格式，檔案數量:', processedFiles.length);
        } else if (records && records.files) {
            // 舊格式: {files: [...]}
            processedFiles = records.files;
            console.log('✅ 使用舊格式，檔案數量:', processedFiles.length);
        } else if (Array.isArray(records)) {
            // 直接陣列格式
            processedFiles = records;
            console.log('✅ 使用陣列格式，檔案數量:', processedFiles.length);
        }

        if (!processedFiles || processedFiles.length === 0) {
            console.log('⚠️ 沒有今日處理記錄');
            if (emptyElement) {
                emptyElement.innerHTML = '<div style="text-align: center; color: #6c757d;">今日尚無處理記錄</div>';
                DOMManager.show('todayRecordsEmpty');
            }
            DOMManager.hide('todayRecordsList');
            return;
        }

        // 隱藏空狀態，顯示記錄列表
        DOMManager.hide('todayRecordsEmpty');
        DOMManager.show('todayRecordsList');

        // 生成記錄 HTML
        const recordsHTML = processedFiles.map(record => {
            console.log('📋 處理檔案記錄:', record);

            // 使用原始資料夾名稱或 display_name + EQCTOTALDATA
            const baseName = record.display_name || record.original_folder_name || record.extract_id || '未知檔案';
            const displayName = record.original_folder_name ? 
                `${record.original_folder_name}_EQCTOTALDATA` : 
                baseName;

            // 獲取處理時間
            const processTime = record.process_time || record.processed_time;
            const formattedTime = processTime ? Utils.formatDateTime(processTime) : '時間未知';

            // 計算總檔案大小
            const totalSize = record.result_files ? 
                record.result_files.reduce((sum, file) => sum + (file.size || 0), 0) : 0;
            const totalSizeText = totalSize > 0 ? Utils.formatFileSize(totalSize) : '';

            // 檔案數量資訊
            const fileCount = record.total_files || (record.result_files ? record.result_files.length : 0);
            const fileInfo = fileCount > 0 ? `${fileCount}個檔案${totalSizeText ? ` (${totalSizeText})` : ''}` : '';

            console.log(`📋 顯示: ${displayName}, 時間: ${formattedTime}, 檔案: ${fileInfo}`);

            // 生成下載按鈕
            let downloadButtons = '';
            if (record.result_files && record.result_files.length > 0) {
                downloadButtons = record.result_files.map(file => {
                    let iconClass = 'fa-file';
                    let buttonColor = '#6c757d';
                    let shortName = file.filename;
                    
                    if (file.filename.endsWith('.xlsx')) {
                        iconClass = 'fa-file-excel';
                        buttonColor = '#28a745';
                        shortName = 'Excel';
                    } else if (file.filename.endsWith('.csv')) {
                        iconClass = 'fa-file-csv';
                        buttonColor = '#17a2b8';
                        shortName = file.filename.includes('RAW') ? 'RAW' : 'CSV';
                    }
                    
                    // 生成唯一的按鈕 ID 並使用 data 屬性避免路徑跳脫問題
                    const buttonId = `download-btn-${record.extract_id}-${file.filename.replace(/[^a-zA-Z0-9]/g, '')}`;
                    
                    return `
                        <button id="${buttonId}"
                                data-file-path="${file.path.replace(/\\/g, '/')}"
                                data-file-name="${file.filename}"
                                onclick="eqcProcessor.downloadFileByDataAttr('${buttonId}')"
                                style="padding: 2px 6px; font-size: 9px; background: ${buttonColor}; color: white; 
                                       border: none; border-radius: 3px; cursor: pointer; display: flex; 
                                       align-items: center; gap: 2px;"
                                title="${file.filename} (${file.size_mb}MB)">
                            <i class="fas ${iconClass}"></i>
                            <span>${shortName}</span>
                        </button>
                    `;
                }).join('');
            }

            return `
                <div style="border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; margin-bottom: 8px; background: #f8f9fa;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 6px;">
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <i class="fas fa-clock" style="color: #667eea; font-size: 12px;"></i>
                            <span style="font-weight: bold; font-size: 13px;">${record.process_time_display || formattedTime.split(' ')[1]}</span>
                            <span style="font-size: 11px; color: #6c757d;">${displayName}</span>
                        </div>
                        <span style="font-size: 11px; color: #28a745;">${fileInfo}</span>
                    </div>
                    
                    <div style="font-size: 10px; color: #6c757d; margin-bottom: 8px; word-break: break-all;">
                        📁 ${record.directory_path || ''}
                    </div>
                    
                    <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                        ${downloadButtons}
                    </div>
                </div>
            `;
        }).join('');
        
        DOMManager.setHTML('todayRecordsList', recordsHTML);
    }
    
    /**
     * 顯示報告
     * @param {string} reportPath - 報告路徑
     */
    async showReport(reportPath) {
        try {
            await modal.showReportPreview(reportPath);
        } catch (error) {
            console.error('❌ 顯示報告失敗:', error);
            StatusManager.showToast('顯示報告失敗', 'error');
        }
    }
    
    /**
     * 下載報告
     * @param {string} reportPath - 報告路徑
     */
    downloadReport(reportPath) {
        const downloadUrl = ApiClient.createReportDownloadUrl(reportPath);
        const fileName = reportPath.split('/').pop() || 'eqc_report.txt';
        Utils.downloadFile(downloadUrl, fileName);
        StatusManager.showToast('報告下載已開始', 'success');
    }
    
    /**
     * 透過 data 屬性下載檔案 (避免路徑跳脫問題)
     * @param {string} buttonId - 按鈕 ID
     */
    downloadFileByDataAttr(buttonId) {
        try {
            const button = document.getElementById(buttonId);
            if (!button) {
                throw new Error('找不到下載按鈕');
            }

            const filePath = button.getAttribute('data-file-path');
            const fileName = button.getAttribute('data-file-name');

            console.log(`🔽 下載今日記錄檔案: ${fileName}`);
            console.log(`📁 原始路徑: ${filePath}`);

            // 路徑轉換：Windows -> Linux
            let linuxPath = filePath; // 已經在 HTML 生成時轉換過斜線

            if (linuxPath.startsWith('D:/')) {
                // 處理 D:/path/to/file 格式
                linuxPath = linuxPath.replace('D:/', '/mnt/d/');
            } else if (linuxPath.startsWith('D:')) {
                // 處理 D:path/to/file 格式（缺少斜線）
                linuxPath = '/mnt/d/' + linuxPath.substring(2);
            }

            console.log(`🔄 路徑轉換: ${filePath} -> ${linuxPath}`);

            // 使用後端的檔案下載 API
            const downloadUrl = ApiClient.createDownloadUrl(linuxPath);
            Utils.downloadFile(downloadUrl, fileName);
            StatusManager.showToast(`${fileName} 下載已開始`, 'success');

        } catch (error) {
            console.error('❌ 檔案下載失敗:', error);
            StatusManager.showToast('檔案下載失敗', 'error');
        }
    }

    /**
     * 下載檔案 (舊版本，保留作為備用)
     * @param {string} filePath - 檔案路徑
     * @param {string} fileName - 檔案名稱
     */
    downloadFile(filePath, fileName) {
        try {
            // 使用後端的檔案下載 API
            const downloadUrl = ApiClient.createDownloadUrl(filePath);
            Utils.downloadFile(downloadUrl, fileName);
            StatusManager.showToast(`${fileName} 下載已開始`, 'success');
            console.log(`📥 開始下載檔案: ${fileName} from ${filePath}`);
        } catch (error) {
            console.error('❌ 檔案下載失敗:', error);
            StatusManager.showToast('檔案下載失敗', 'error');
        }
    }
    
    /**
     * 處理錯誤
     * @param {Error} error - 錯誤對象
     */
    handleProcessingError(error) {
        progressDisplay.failProcessing(error.message);
        StatusManager.showToast(`處理失敗: ${error.message}`, 'error');
        
        // 重置狀態
        this.currentResults = null;
    }
    
    /**
     * 重置處理器狀態
     */
    reset() {
        this.isProcessing = false;
        this.currentResults = null;
        progressDisplay.reset();
        detailPanel.reset();
    }
    
    /**
     * 獲取當前狀態
     * @returns {Object} 當前狀態
     */
    getCurrentStatus() {
        return {
            isProcessing: this.isProcessing,
            hasResults: !!this.currentResults,
            currentStep: progressDisplay.getCurrentStatus().currentStep
        };
    }
    
    /**
     * 🔥 啟動進度監控和倒數計時
     */
    startProgressMonitoring() {
        console.log('🔥 啟動進度監控...');
        
        this.processingInterval = setInterval(() => {
            if (!this.isProcessing) {
                this.stopProgressMonitoring();
                return;
            }
            
            const elapsed = (Date.now() - this.processingStartTime) / 1000;
            const remaining = Math.max(0, this.estimatedDuration - elapsed);
            const progress = Math.min(100, (elapsed / this.estimatedDuration) * 100);
            
            // 更新進度顯示
            const progressText = `處理中... (剩餘時間: ${Math.ceil(remaining)}秒)`;
            console.log(`🔄 ${progressText} - 進度: ${progress.toFixed(1)}%`);
            
            // 超時檢測
            if (elapsed > this.maxTimeout) {
                console.warn('⏰ 處理超時，將自動取消...');
                this.cancelProcessing('處理超時');
            }
            
        }, 1000); // 每秒更新
    }
    
    /**
     * 🔥 停止進度監控
     */
    stopProgressMonitoring() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
            console.log('🔄 進度監控已停止');
        }
    }
    
    /**
     * 🔥 取消處理
     * @param {string} reason - 取消原因
     */
    cancelProcessing(reason = '使用者取消') {
        console.warn(`❌ 取消處理: ${reason}`);
        
        this.cancelRequested = true;
        this.isProcessing = false;
        this.stopProgressMonitoring();
        
        // 更新UI
        progressDisplay.failProcessing(`處理已取消: ${reason}`);
        StatusManager.showToast(`處理已取消: ${reason}`, 'warning');
        
        // 重設狀態
        this.currentResults = null;
    }
    
    /**
     * 🔥 手動取消處理
     */
    manualCancel() {
        if (this.isProcessing) {
            this.cancelProcessing('使用者手動取消');
        } else {
            StatusManager.showToast('沒有進行中的處理', 'info');
        }
    }
}

// 創建全局實例
const eqcProcessor = new EQCProcessor();

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EQCProcessor;
} else if (typeof window !== 'undefined') {
    window.EQCProcessor = EQCProcessor;
    window.eqcProcessor = eqcProcessor;
}
