# Outlook Summary System - Python Migration

## [BOARD] 專案概述

這是一個將現有 VBA Excel 郵件處理系統遷移至 Python 的專案。系統自動化處理來自多個半導體測試廠商（GTK, ETD, XAHT, JCET, LINGSEN 等）的郵件，包含解析、檔案管理和報表生成功能。

## [ROCKET] **最新更新 (2025-08-02)**

## 專案統計
| 項目 | 數量 | 備註 |
|------|------|------|
| 總檔案數 | 0 | 專案所有檔案 |
| Python 檔案 | 0 | 源碼檔案數 |
| 程式碼行數 | N/A | Python 程式碼行數 |
| 測試檔案 | 0 | 單元/整合測試 |
| 文檔檔案 | 0 | Markdown 文檔 |
| 支援廠商 | 6 | 半導體測試廠商 |
| 函數數量 | 0 | Python 函數總數 |
| 類別數量 | 0 | Python 類別總數 |
| Git 提交 | N/A | 版本控制歷史 |
| 貢獻者 | N/A | 開發團隊成員 |

### [OK] **CTA 處理系統架構完成**
- **CLI 入口**: `cta_processor.py` - 用戶操作介面
- **處理引擎**: `src/infrastructure/adapters/excel/cta/cta_integrated_processor.py` - 核心邏輯
- **架構模組**: 完整的 CTA 適配器系統
- **功能**: CSV 到 Excel 轉換，完全符合 VBA convertOtherDatalog 邏輯
- **特色**: 動態欄位檢測、智慧數字轉換、無警告三角形
- **架構**: 遵循 CLAUDE.md 功能替換原則，消[EXCEPT_CHAR]重複程式碼

### [CHART] **CTA 處理能力**
- **輸入**: CTA CSV 檔案 (`doc/` 目錄)
- **輸出**: Excel 檔案 (`logs/` 目錄) 
- **結構**: Data11 (52行 x 1884列) + sum (原檔資料)
- **效能**: ~8.5秒處理時間，632.3 KB 輸出檔案

### [TARGET] **完全規範符合**
- [OK] CTA_CORE_ANALYSIS.md 動態欄位規範
- [OK] CSV_TO_EXCEL_FINAL_BACKUP.md 智慧數字轉換
- [OK] CLAUDE.md 功能替換原則與極簡程式碼
- [OK] VBA 邏輯完整對應

## [BUILDING_CONSTRUCTION] 系統架構

採用**六角架構 (Hexagonal Architecture)** 設計，包含：

- **核心領域層**: 業務邏輯與規則
- **應用層**: 使用案例與工作流程
- **基礎設施層**: 外部整合（Outlook、檔案系統、資料庫）
- **展示層**: API、CLI、Web UI

## [BOOKS] 文件結構

### [TARGET] 核心文件
- **[CLAUDE.md](./CLAUDE.md)** - AI 程式設計指導規則與開發規範
- **[PYTHON_MIGRATION_PLAN.md](./PYTHON_MIGRATION_PLAN.md)** - 完整的 Python 遷移計畫
- **[VBA_TO_PYTHON_MAPPING.md](./VBA_TO_PYTHON_MAPPING.md)** - VBA 到 Python 架構對照表
- **[UPDATED_ARCHITECTURE_WITH_DATABASE.md](./UPDATED_ARCHITECTURE_WITH_DATABASE.md)** - 包含資料庫與統計分析的完整架構
- **[DOMAIN_MODELS_DESIGN.md](./DOMAIN_MODELS_DESIGN.md)** - 核心領域模型設計
- **[PROJECT_STATUS_TEMPLATE.md](./PROJECT_STATUS_TEMPLATE.md)** - 專案狀態追蹤模板

### [FILE_FOLDER] 原始 VBA 檔案
- `autodownloadLY.xlsm` - 原始 VBA Excel 檔案
- `prg.txt` - VBA 主程式碼
- `module1.txt` - VBA 模組1程式碼
- `module2.txt` - VBA 模組2程式碼

## [ROCKET] 快速開始

### 1. 環境設置

```bash
# 克隆專案
git clone <repository_url>
cd outlook_summary

# 建立虛擬環境 ([WARNING] 必要步驟)
python -m venv venv

# 啟動虛擬環境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安裝系統壓縮工具 (必要)
sudo apt install -y p7zip-full p7zip-rar unrar zip unzip

# 安裝 Python 依賴 (包含所有必要套件)
pip install -r requirements.txt
```

### 2. UV 虛擬環境啟動 (推薦)

```bash
# 使用 UV 建立的 Python >=3.9 虛擬環境
venv_win_3_11_12\Scripts\activate    # Windows

# 或直接執行腳本
.\venv_win_3_11_12\Scripts\python.exe start_integrated_services.py

# 使用批處理檔案 (推薦)
activate_venv_3_11_12.bat        # 啟動虛擬環境
run_with_venv_3_11_12.bat start_integrated_services.py  # 直接執行
```

### 3. 服務啟動

```bash
# 方法1: 使用UTF-8批處理檔案 (推薦)
start_services_utf8.bat

# 方法2: 手動設置編碼
chcp 65001
python start_integrated_services.py

# 方法3: 使用UV虛擬環境
venv_win_3_11_12\Scripts\python.exe start_integrated_services.py
```

### 4. 服務端點

**整合服務啟動後可訪問以下端點：**

```bash
# 郵件收件夾系統
http://localhost:5000              # 主要郵件管理界面
http://localhost:5000/inbox        # 收件箱管理

# FT-EQC 處理系統
http://localhost:8010/ui           # FT-EQC 處理界面
http://localhost:8010/docs         # API 文檔
http://localhost:8010/api/status   # 系統狀態
```

### 5. 開發指令

```bash
# [WARNING] 每次開發前必須先啟動虛擬環境
source venv/bin/activate    # Linux/Mac
# 或
venv_win_3_11_12\Scripts\activate    # Windows UV環境

# 執行測試
pytest --cov=src --cov-report=html

# 程式碼品質檢查
black src/ tests/
flake8 src/ tests/
mypy src/

# 啟動開發伺服器
python -m src.main --dev
```

### 6. 故障排除

**Unicode 編碼問題：**
```bash
# 如果出現 cp950 編碼錯誤，使用 UTF-8 啟動
start_services_utf8.bat

# 或手動設置編碼
chcp 65001
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
```

**服務進程管理：**
```bash
# 查看運行中的服務
tasklist | findstr python
netstat -ano | findstr ":5000\|:8010"

# 強制終止服務
taskkill /F /IM python.exe
```

**虛擬環境問題：**
```bash
# 確認虛擬環境路徑
which python    # Linux/Mac
where python    # Windows

# 重新激活虛擬環境
deactivate
venv_win_3_11_12\Scripts\activate
```

## [TEST_TUBE] 測試策略

採用**測試驅動開發 (TDD)** 方法：

1. **[RED_CIRCLE] Red**: 先寫失敗的測試
2. **[GREEN_CIRCLE] Green**: 寫最少程式碼讓測試通過
3. **[BLUE_CIRCLE] Refactor**: 重構程式碼
4. **[TEST_TUBE] Program Test**: 實際執行驗證

### 測試覆蓋率目標
- **整體目標**: 90%+
- **核心業務邏輯**: 95%+
- **API 端點**: 100%

## [FACTORY] 支援的廠商

| 廠商 | 識別條件 | 解析器 |
|------|----------|--------|
| GTK | `ft hold`, `ft lot` | GTKParser |
| ETD | `anf` | ETDParser |
| XAHT | `tianshui`, `西安` | XAHTParser |
| JCET | `jcet` | JCETParser |
| LINGSEN | `lingsen` | LINGSENParser |

## [CHART] 核心功能

### [E_MAIL] 郵件處理流程
1. **郵件監控** - 即時監控 Outlook 收件箱
2. **廠商識別** - 自動識別郵件來源廠商
3. **資料解析** - 提取 MO、LOT、良率等關鍵資訊
4. **檔案處理** - 下載、解壓、轉換附件
5. **報表生成** - 自動產生 Excel 摘要報表
6. **郵件發送** - 發送處理結果通知

### [UP] 統計分析功能
- **廠商效能分析** - 處理成功率、平均良率
- **趨勢分析** - 良率趨勢、處理時間趨勢
- **異常檢測** - 自動偵測異常數值
- **即時監控** - 系統健康狀態儀表板

### [CHART_WITH_UPWARDS_TREND] **統一監控儀表板 (NEW)**
全方位即時監控系統，專為半導體郵件處理基礎設施設計：

#### 核心監控功能
- **📧 郵件處理監控** - code_comparison.py 任務、廠商分組統計
- **🔄 Celery任務監控** - 長時間任務、工作者狀態、佇列管理 ✅
- **💻 系統資源監控** - CPU/記憶體/磁碟、服務健康狀態
- **📊 業務指標監控** - MO/LOT統計、資料品質、報告生成
- **🚨 智能告警系統** - 多級告警、自動通知、告警合併

#### 技術特性
- **即時更新** - 5秒內反映系統變化 (WebSocket)
- **最小侵入** - 不影響現有系統運行
- **歷史分析** - 7天/30天趨勢分析
- **高效能快取** - 記憶體快取支援毫秒級回應
- **錯誤隔離** - 監控故障不影響主業務功能

#### 已實現元件
- ✅ **Celery 監控收集器** - 完整的任務佇列和工作者監控
- ✅ **快取服務系統** - 高效能記憶體快取管理
- ✅ **WebSocket 管理器** - 即時資料推送服務
- ✅ **資料模型系統** - 完整的監控指標資料結構

#### 存取方式
- **主儀表板**: `http://localhost:5555/dashboard`
- **API 文檔**: [監控 API 文檔](src/dashboard_monitoring/docs/api_documentation.md)
- **使用指南**: [Celery 收集器指南](src/dashboard_monitoring/docs/celery_collector_guide.md)

## [FILE_CABINET] 資料庫設計

支援 PostgreSQL 和 SQLite，包含：

- `emails` - 郵件記錄表
- `vendors` - 廠商資訊表
- `parsing_results` - 解析結果表
- `daily_statistics` - 每日統計表
- `performance_metrics` - 效能指標表

## [TOOL] 開發規範

### 強制要求
- [OK] **虛擬環境**: 每次開發前必須啟動
- [OK] **TDD 開發**: 後端程式碼必須先寫測試
- [OK] **API 測試**: 所有端點必須實際測試
- [OK] **前端測試**: 使用 Playwright 進行 E2E 測試
- [OK] **程式測試**: 實際執行驗證功能

### 品質標準
- **測試覆蓋率**: > 90%
- **型別檢查**: MyPy 100% 通過
- **程式碼格式**: Black + Flake8
- **安全掃描**: Bandit 無高風險項目

## [PACKAGE] 技術棧

### 後端
- **Python >=3.9**: 主要開發語言
- **FastAPI**: Web 框架
- **Pydantic**: 資料驗證
- **SQLAlchemy**: ORM
- **Pandas**: 資料處理
- **Pytest**: 測試框架

### 前端
- **HTML/CSS/JavaScript**: 基礎前端
- **Playwright**: E2E 測試

### 資料庫
- **PostgreSQL**: 生產環境
- **SQLite**: 開發/測試環境

### 監控
- **Prometheus**: 指標收集
- **Grafana**: 視覺化儀表板

## [ROCKET] 部署

```bash
# Docker 部署
docker-compose up -d

# 手動部署
pip install -r requirements.txt
python -m src.main --production
```

## [BOARD] 專案狀態

**輔助文件說明**: 本文件為專案概述與快速開始輔助文件。

**主要狀態文件**: 請參考 **[PROJECT_STATUS_TEMPLATE.md](./PROJECT_STATUS_TEMPLATE.md)** 來獲取最新、最完整的專案進度和狀態資訊。

- **當前階段**: PHASE_2 已完成 [OK]，準備進入 PHASE_3
- **完成任務**: TASK_001~005 全部完成
- **完成進度**: 設計階段 100%，實作階段 60% (PHASE_2 數據模型層建設完成)
- **測試狀態**: 72 個測試，100% 通過率
- **品質指標**: A級 (TDD 開發、六角架構、現代 Python 最佳實踐)

## 🤝 貢獻指南

1. Fork 專案
2. 建立功能分支
3. 遵循 TDD 開發
4. 確保測試通過
5. 提交 Pull Request

## [PAGE_FACING_UP] 授權

本專案採用 MIT 授權條款。

---

**🤖 本專案使用 AI 輔助開發，遵循最佳實踐和現代軟體工程原則。**