"""
統一監控儀表板模組

提供全方位即時監控系統，整合郵件處理、Celery任務、系統資源和業務指標監控。

主要功能：
- 郵件處理監控
- Celery任務監控  
- 系統資源監控
- 業務指標監控
- 智能告警系統
"""

__version__ = "1.0.0"
__author__ = "Dashboard Monitoring Team"

# 匯出配置系統和API模組
from .config import (
    DashboardConfig,
    MonitoringRulesManager,
    get_dashboard_config,
    get_monitoring_rules_manager
)

# 匯出API模組（WebSocket API已實現）
try:
    from . import api
except ImportError:
    # API模組可能有依賴問題，但不影響其他功能
    api = None

__all__ = [
    "DashboardConfig",
    "MonitoringRulesManager",
    "get_dashboard_config",
    "get_monitoring_rules_manager",
    "api"
]