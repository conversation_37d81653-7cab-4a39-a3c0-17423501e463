"""
統一監控儀表板 - 監控 API 端點
使用依賴注入模式，避免緊耦合和重複代碼
"""

from fastapi import APIRouter, HTTPException, status, Query, Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

from .dashboard_dependencies import (
    MonitoringCoordinator,
    AlertService, 
    TrendAnalyzer,
    MonitoringRepository,
    AlertRepository,
    Config,
    AllServices,
    OptionalMonitoringCoordinator,
    check_service_health
)
from .dashboard_cache_api import router as cache_router
from ..models.dashboard_metrics_models import (
    DashboardMetrics,
    EmailMetrics,
    CeleryMetrics,
    SystemMetrics
)
from ..models.dashboard_alert_models import (
    DashboardAlert,
    AlertLevel
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/monitoring", tags=["監控"])

# 包含快取管理路由
router.include_router(cache_router, prefix="", tags=["快取管理"])

# ================================
# 儀表板主要端點
# ================================

@router.get("/dashboard", summary="獲取儀表板資料")
async def get_dashboard_data(
    coordinator: MonitoringCoordinator,
    config: Config
) -> Dict[str, Any]:
    """
    獲取完整的儀表板資料
    
    ✅ 優勢：
    - 無需手動檢查服務可用性
    - 統一的錯誤處理
    - 易於測試和維護
    """
    try:
        # 直接使用注入的服務，無需 None 檢查
        metrics = await coordinator.collect_all_metrics()
        
        return {
            "status": "success",
            "data": {
                "metrics": metrics.dict(),
                "timestamp": datetime.now().isoformat(),
                "update_interval": config.metrics_update_interval
            }
        }
    except Exception as e:
        logger.error(f"獲取儀表板資料失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="獲取儀表板資料失敗"
        )

@router.get("/health", summary="系統健康檢查")
async def get_system_health() -> Dict[str, Any]:
    """
    系統健康檢查 - 不依賴具體服務，獨立檢查
    """
    health_status = await check_service_health()
    
    status_code = status.HTTP_200_OK if health_status["overall"] else status.HTTP_503_SERVICE_UNAVAILABLE
    
    return {
        "status": "healthy" if health_status["overall"] else "unhealthy",
        "services": health_status,
        "timestamp": datetime.now().isoformat()
    }

# ================================
# 郵件監控端點
# ================================

@router.get("/email/queue", summary="獲取郵件佇列狀態")
async def get_email_queue_status(
    coordinator: MonitoringCoordinator
) -> Dict[str, Any]:
    """
    獲取郵件佇列狀態
    
    ❌ 重構前的問題：
    coordinator = get_monitoring_coordinator()
    if not coordinator:
        raise HTTPException(503, "服務不可用")
    
    ✅ 重構後：直接使用注入的服務
    """
    metrics = await coordinator.collect_email_metrics()
    
    return {
        "status": "success",
        "data": {
            "pending_count": metrics.pending_count,
            "processing_count": metrics.processing_count,
            "completed_count": metrics.completed_count,
            "failed_count": metrics.failed_count,
            "vendor_counts": metrics.vendor_queue_counts,
            "code_comparison_active": metrics.code_comparison_active
        },
        "timestamp": datetime.now().isoformat()
    }

@router.get("/email/performance", summary="獲取郵件處理效能")
async def get_email_performance(
    coordinator: MonitoringCoordinator,
    time_range: str = Query("1h", description="時間範圍: 1h, 6h, 24h, 7d")
) -> Dict[str, Any]:
    """獲取郵件處理效能指標"""
    metrics = await coordinator.collect_email_metrics()
    
    return {
        "status": "success",
        "data": {
            "avg_processing_time": metrics.avg_processing_time_seconds,
            "throughput_per_hour": metrics.throughput_per_hour,
            "vendor_success_rates": metrics.vendor_success_rates,
            "time_range": time_range
        }
    }

# ================================
# Celery 任務監控端點
# ================================

@router.get("/celery/tasks", summary="獲取 Celery 任務狀態")
async def get_celery_task_status(
    coordinator: MonitoringCoordinator
) -> Dict[str, Any]:
    """獲取 Celery 任務狀態"""
    metrics = await coordinator.collect_celery_metrics()
    
    return {
        "status": "success",
        "data": {
            "total_active": metrics.total_active,
            "total_pending": metrics.total_pending,
            "total_completed": metrics.total_completed,
            "total_failed": metrics.total_failed,
            "task_type_counts": metrics.task_type_counts,
            "worker_status": metrics.worker_status,
            "avg_task_duration": metrics.avg_task_duration
        }
    }

@router.get("/celery/workers", summary="獲取工作者狀態")
async def get_worker_status(
    coordinator: MonitoringCoordinator
) -> Dict[str, Any]:
    """獲取 Celery 工作者狀態"""
    metrics = await coordinator.collect_celery_metrics()
    
    return {
        "status": "success",
        "data": {
            "worker_status": metrics.worker_status,
            "worker_load": metrics.worker_load,
            "total_workers": len(metrics.worker_status)
        }
    }

# ================================
# 系統監控端點
# ================================

@router.get("/system/resources", summary="獲取系統資源狀態")
async def get_system_resources(
    coordinator: MonitoringCoordinator
) -> Dict[str, Any]:
    """獲取系統資源狀態"""
    metrics = await coordinator.collect_system_metrics()
    
    return {
        "status": "success",
        "data": {
            "cpu_percent": metrics.cpu_percent,
            "memory_percent": metrics.memory_percent,
            "disk_percent": metrics.disk_percent,
            "active_connections": metrics.active_connections,
            "service_health": metrics.service_health
        }
    }

# ================================
# 告警管理端點
# ================================

@router.get("/alerts", summary="獲取活躍告警")
async def get_active_alerts(
    alert_service: AlertService,
    level: Optional[str] = Query(None, description="告警級別篩選")
) -> Dict[str, Any]:
    """
    獲取活躍告警
    
    ✅ 依賴注入優勢：
    - alert_service 保證可用，無需檢查
    - 統一的錯誤處理
    - 易於單元測試
    """
    alerts = await alert_service.get_active_alerts(level=level)
    
    return {
        "status": "success",
        "data": {
            "alerts": [alert.dict() for alert in alerts],
            "total_count": len(alerts),
            "critical_count": len([a for a in alerts if a.level == "critical"]),
            "warning_count": len([a for a in alerts if a.level == "warning"])
        }
    }

@router.post("/alerts/{alert_id}/acknowledge", summary="確認告警")
async def acknowledge_alert(
    alert_id: str = Path(..., description="告警 ID"),
    alert_service: AlertService = None
) -> Dict[str, Any]:
    """確認告警"""
    success = await alert_service.acknowledge_alert(alert_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="告警不存在或已處理"
        )
    
    return {
        "status": "success",
        "message": f"告警 {alert_id} 已確認"
    }

# ================================
# 趨勢分析端點
# ================================

@router.get("/trends/{metric_type}", summary="獲取趨勢資料")
async def get_trend_data(
    metric_type: str = Path(..., description="指標類型"),
    time_range: str = Query("24h", description="時間範圍"),
    trend_analyzer: TrendAnalyzer = None
) -> Dict[str, Any]:
    """
    獲取趨勢資料
    
    ✅ 依賴注入讓代碼更簡潔：
    - 無需重複的服務獲取邏輯
    - 自動處理服務不可用情況
    """
    trend_data = await trend_analyzer.analyze_trends(metric_type, time_range)
    
    return {
        "status": "success",
        "data": trend_data
    }

@router.get("/predictions/load", summary="獲取負載預測")
async def get_load_prediction(
    hours_ahead: int = Query(24, description="預測小時數"),
    trend_analyzer: TrendAnalyzer = None
) -> Dict[str, Any]:
    """獲取負載預測"""
    prediction = await trend_analyzer.predict_load(hours_ahead)
    
    return {
        "status": "success",
        "data": prediction
    }

# ================================
# 歷史資料端點
# ================================

@router.get("/history/metrics", summary="獲取歷史指標")
async def get_metrics_history(
    metric_type: str = Query(..., description="指標類型"),
    start_time: datetime = Query(..., description="開始時間"),
    end_time: datetime = Query(..., description="結束時間"),
    monitoring_repo: MonitoringRepository = None
) -> Dict[str, Any]:
    """
    獲取歷史指標資料
    
    ✅ 依賴注入的測試優勢：
    - 可以輕易替換 monitoring_repo 進行測試
    - 不需要複雜的 Mock 設定
    """
    history_data = await monitoring_repo.get_metrics_history(
        metric_type=metric_type,
        start_time=start_time,
        end_time=end_time
    )
    
    return {
        "status": "success",
        "data": {
            "metrics": history_data,
            "count": len(history_data),
            "time_range": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat()
            }
        }
    }

# ================================
# 統計資料端點
# ================================

@router.get("/statistics/summary", summary="獲取統計摘要")
async def get_statistics_summary(
    services: AllServices  # 使用複合依賴
) -> Dict[str, Any]:
    """
    獲取統計摘要
    
    ✅ 複合依賴的優勢：
    - 一次注入所有需要的服務
    - 保證服務間的一致性
    - 簡化複雜端點的依賴管理
    """
    # 收集各種統計資料
    email_metrics = await services.coordinator.collect_email_metrics()
    celery_metrics = await services.coordinator.collect_celery_metrics()
    system_metrics = await services.coordinator.collect_system_metrics()
    active_alerts = await services.alert_service.get_active_alerts()
    
    return {
        "status": "success",
        "data": {
            "email_summary": {
                "total_pending": email_metrics.pending_count,
                "processing_rate": email_metrics.throughput_per_hour
            },
            "celery_summary": {
                "total_active": celery_metrics.total_active,
                "total_pending": celery_metrics.total_pending
            },
            "system_summary": {
                "cpu_usage": system_metrics.cpu_percent,
                "memory_usage": system_metrics.memory_percent
            },
            "alerts_summary": {
                "total_alerts": len(active_alerts),
                "critical_alerts": len([a for a in active_alerts if a.level == "critical"])
            }
        }
    }

# ================================
# 降級處理端點示例
# ================================

@router.get("/status/optional", summary="可選服務狀態")
async def get_optional_service_status(
    coordinator: OptionalMonitoringCoordinator  # 可選依賴
) -> Dict[str, Any]:
    """
    展示可選依賴的使用
    
    ✅ 降級處理：
    - 服務不可用時返回 None，不會拋出異常
    - 可以提供降級功能
    """
    if coordinator is None:
        return {
            "status": "degraded",
            "message": "監控服務暫時不可用，使用快取資料",
            "data": {
                "cached_metrics": "從快取獲取的資料"
            }
        }
    
    metrics = await coordinator.collect_all_metrics()
    return {
        "status": "success",
        "data": metrics.dict()
    }