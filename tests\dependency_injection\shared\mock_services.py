"""共用的 Mock 服務類別
提供所有測試階段使用的 Mock 服務實現
"""

from unittest.mock import Mock
from typing import Dict, Any, List, Optional


class MockFileStagingService:
    """Mock 檔案暫存服務
    
    模擬 FileStagingService 的行為，用於測試依賴注入
    """
    
    def __init__(self, service_id: str = "mock_staging_service"):
        self.service_id = service_id
        self.is_healthy = True
        self.tasks = {}
        self._task_counter = 0
    
    def health_check(self) -> Dict[str, Any]:
        """健康檢查"""
        return {
            "status": "healthy" if self.is_healthy else "unhealthy",
            "service_id": self.service_id,
            "task_count": len(self.tasks)
        }
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """獲取服務統計"""
        completed = sum(1 for task in self.tasks.values() if task.get("status") == "completed")
        active = sum(1 for task in self.tasks.values() if task.get("status") == "running")
        failed = sum(1 for task in self.tasks.values() if task.get("status") == "failed")
        
        return {
            "total_tasks": len(self.tasks),
            "active_tasks": active,
            "completed_tasks": completed,
            "failed_tasks": failed,
            "pending_tasks": len(self.tasks) - active - completed - failed
        }
    
    def create_staging_task(self, product_name: str, source_files: List[str], 
                          preserve_structure: bool = True, use_unique_name: bool = True) -> str:
        """創建暫存任務"""
        self._task_counter += 1
        task_id = f"staging-task-{self._task_counter}"
        
        self.tasks[task_id] = {
            "id": task_id,
            "product_name": product_name,
            "source_files": source_files,
            "preserve_structure": preserve_structure,
            "use_unique_name": use_unique_name,
            "status": "created",
            "created_at": "2025-08-02T00:00:00Z"
        }
        
        return task_id
    
    def execute_staging_task(self, task_id: str) -> Mock:
        """執行暫存任務"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
        
        self.tasks[task_id]["status"] = "completed"
        
        result = Mock()
        result.success = True
        result.task_id = task_id
        result.output_path = f"/tmp/staging/{task_id}"
        return result
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        return self.tasks.get(task_id)
    
    def cancel_task(self, task_id: str, force: bool = False) -> bool:
        """取消任務"""
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "cancelled"
            return True
        return False


class MockFileProcessingService:
    """Mock 檔案處理服務
    
    模擬 FileProcessingService 的行為，用於測試依賴注入
    """
    
    def __init__(self, service_id: str = "mock_processing_service"):
        self.service_id = service_id
        self.is_healthy = True
        self.tasks = {}
        self._task_counter = 0
    
    def health_check(self) -> Dict[str, Any]:
        """健康檢查"""
        return {
            "status": "healthy" if self.is_healthy else "unhealthy",
            "service_id": self.service_id,
            "task_count": len(self.tasks)
        }
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """獲取服務統計"""
        completed = sum(1 for task in self.tasks.values() if task.get("status") == "completed")
        active = sum(1 for task in self.tasks.values() if task.get("status") == "running")
        failed = sum(1 for task in self.tasks.values() if task.get("status") == "failed")
        
        return {
            "total_tasks": len(self.tasks),
            "active_tasks": active,
            "completed_tasks": completed,
            "failed_tasks": failed,
            "pending_tasks": len(self.tasks) - active - completed - failed
        }
    
    def create_task_with_staging(self, tool: str, source_files: List[str], 
                               product_name: str, preserve_structure: bool = True,
                               use_unique_name: bool = True) -> str:
        """創建帶暫存的處理任務"""
        self._task_counter += 1
        task_id = f"processing-task-{self._task_counter}"
        
        self.tasks[task_id] = {
            "id": task_id,
            "tool": tool,
            "product_name": product_name,
            "source_files": source_files,
            "preserve_structure": preserve_structure,
            "use_unique_name": use_unique_name,
            "status": "created",
            "created_at": "2025-08-02T00:00:00Z"
        }
        
        return task_id
    
    def execute_task(self, task_id: str) -> Mock:
        """執行處理任務"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
        
        self.tasks[task_id]["status"] = "completed"
        
        result = Mock()
        result.success = True
        result.task_id = task_id
        result.output_files = [f"/tmp/output/{task_id}/result.xlsx"]
        result.processing_time = 5.2
        result.tool_used = self.tasks[task_id]["tool"]
        result.retries_used = 0
        return result
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        return self.tasks.get(task_id)
    
    def cancel_task(self, task_id: str, force: bool = False) -> bool:
        """取消任務"""
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "cancelled"
            return True
        return False


class MockServiceContainer:
    """Mock 服務容器
    
    模擬 ServiceContainer 的行為，用於測試依賴注入
    """
    
    def __init__(self):
        self._staging_service = None
        self._processing_service = None
        self._initialization_errors = {}
    
    def get_staging_service(self) -> Optional[MockFileStagingService]:
        """獲取暫存服務"""
        return self._staging_service
    
    def get_processing_service(self) -> Optional[MockFileProcessingService]:
        """獲取處理服務"""
        return self._processing_service
    
    def get_initialization_errors(self) -> Dict[str, str]:
        """獲取初始化錯誤"""
        return self._initialization_errors.copy()
    
    def set_staging_service(self, service: Optional[MockFileStagingService]):
        """設置暫存服務（測試用）"""
        self._staging_service = service
    
    def set_processing_service(self, service: Optional[MockFileProcessingService]):
        """設置處理服務（測試用）"""
        self._processing_service = service
    
    def set_initialization_error(self, service_name: str, error_message: str):
        """設置初始化錯誤（測試用）"""
        self._initialization_errors[service_name] = error_message
    
    def clear_initialization_error(self, service_name: str):
        """清除初始化錯誤（測試用）"""
        self._initialization_errors.pop(service_name, None)
    
    def get_service_status(self) -> Dict[str, Any]:
        """獲取服務狀態"""
        return {
            'staging_service': {
                'available': self._staging_service is not None,
                'error': self._initialization_errors.get('staging')
            },
            'processing_service': {
                'available': self._processing_service is not None,
                'error': self._initialization_errors.get('processing')
            }
        }


class MockAPIState:
    """Mock API 狀態管理器
    
    模擬 APIState 的行為，用於測試依賴注入
    """
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.active_connections = {}
        self.task_cache = {}
    
    def increment_request_count(self):
        """增加請求計數"""
        self.request_count += 1
    
    def increment_error_count(self):
        """增加錯誤計數"""
        self.error_count += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計信息"""
        return {
            "request_count": self.request_count,
            "error_count": self.error_count,
            "active_connections_count": len(self.active_connections),
            "cached_tasks_count": len(self.task_cache),
            "uptime_seconds": 3600  # 模擬 1 小時運行時間
        }
    
    def add_connection(self, connection_key: str, connection_info: Dict[str, Any]):
        """添加連接"""
        self.active_connections[connection_key] = connection_info
    
    def remove_connection(self, connection_key: str) -> bool:
        """移除連接"""
        if connection_key in self.active_connections:
            del self.active_connections[connection_key]
            return True
        return False
