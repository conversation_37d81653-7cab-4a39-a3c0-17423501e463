"""依賴注入配置管理
提供不同環境下的依賴注入配置和管理策略
"""

import os
from typing import Dict, Any, Optional, Type, Callable
from enum import Enum
from dataclasses import dataclass
from loguru import logger

from .dependencies import ServiceContainer, APIState


class EnvironmentType(Enum):
    """環境類型"""
    DEVELOPMENT = "development"
    TESTING = "testing" 
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class ServiceConfig:
    """服務配置"""
    enabled: bool = True
    max_retries: int = 3
    timeout_seconds: int = 30
    cache_enabled: bool = True
    health_check_interval: int = 60
    
    # 特定服務配置
    staging_config: Optional[Dict[str, Any]] = None
    processing_config: Optional[Dict[str, Any]] = None
    search_config: Optional[Dict[str, Any]] = None


class DependencyConfig:
    """依賴注入配置管理器"""
    
    def __init__(self, environment: EnvironmentType = EnvironmentType.DEVELOPMENT):
        self.environment = environment
        self.service_configs: Dict[str, ServiceConfig] = {}
        self.service_overrides: Dict[str, Callable] = {}
        self.middleware_configs: Dict[str, Dict[str, Any]] = {}
        
        # 載入環境特定配置
        self._load_environment_config()
        
        logger.info(f"依賴注入配置已初始化 - 環境: {environment.value}")
    
    def _load_environment_config(self):
        """載入環境特定配置"""
        
        if self.environment == EnvironmentType.DEVELOPMENT:
            self._setup_development_config()
        elif self.environment == EnvironmentType.TESTING:
            self._setup_testing_config()
        elif self.environment == EnvironmentType.STAGING:
            self._setup_staging_config()
        elif self.environment == EnvironmentType.PRODUCTION:
            self._setup_production_config()
    
    def _setup_development_config(self):
        """開發環境配置"""
        self.service_configs = {
            "staging": ServiceConfig(
                enabled=True,
                max_retries=2,
                timeout_seconds=60,
                cache_enabled=True,
                health_check_interval=30,
                staging_config={
                    "staging_root": "./staging",
                    "max_staging_size_gb": 10,
                    "cleanup_interval_hours": 6
                }
            ),
            "processing": ServiceConfig(
                enabled=True,
                max_retries=3,
                timeout_seconds=120,
                cache_enabled=True,
                health_check_interval=45,
                processing_config={
                    "max_concurrent_tasks": 2,
                    "task_timeout_minutes": 30,
                    "temp_dir": "./temp"
                }
            ),
            "search": ServiceConfig(
                enabled=True,
                max_retries=2,
                timeout_seconds=30,
                cache_enabled=True,
                search_config={
                    "max_search_results": 100,
                    "search_timeout_seconds": 15
                }
            )
        }
        
        # 開發環境中介軟體配置
        self.middleware_configs = {
            "logging": {"level": "DEBUG", "detailed": True},
            "cors": {"allow_origins": ["*"], "allow_credentials": True},
            "rate_limiting": {"enabled": False}
        }
    
    def _setup_testing_config(self):
        """測試環境配置"""
        self.service_configs = {
            "staging": ServiceConfig(
                enabled=True,
                max_retries=1,
                timeout_seconds=10,
                cache_enabled=False,  # 測試時關閉快取
                health_check_interval=10,
                staging_config={
                    "staging_root": "./test_staging",
                    "max_staging_size_gb": 1,
                    "cleanup_interval_hours": 1
                }
            ),
            "processing": ServiceConfig(
                enabled=True,
                max_retries=1,
                timeout_seconds=30,
                cache_enabled=False,
                health_check_interval=10,
                processing_config={
                    "max_concurrent_tasks": 1,
                    "task_timeout_minutes": 5,
                    "temp_dir": "./test_temp"
                }
            ),
            "search": ServiceConfig(
                enabled=False,  # 測試時可能關閉搜尋服務
                max_retries=1,
                timeout_seconds=5
            )
        }
        
        self.middleware_configs = {
            "logging": {"level": "INFO", "detailed": False},
            "cors": {"allow_origins": ["http://localhost:3000"]},
            "rate_limiting": {"enabled": False}
        }
    
    def _setup_staging_config(self):
        """預備環境配置"""
        self.service_configs = {
            "staging": ServiceConfig(
                enabled=True,
                max_retries=3,
                timeout_seconds=90,
                cache_enabled=True,
                health_check_interval=60,
                staging_config={
                    "staging_root": "/app/staging",
                    "max_staging_size_gb": 50,
                    "cleanup_interval_hours": 12
                }
            ),
            "processing": ServiceConfig(
                enabled=True,
                max_retries=3,
                timeout_seconds=300,
                cache_enabled=True,
                health_check_interval=60,
                processing_config={
                    "max_concurrent_tasks": 4,
                    "task_timeout_minutes": 60,
                    "temp_dir": "/app/temp"
                }
            ),
            "search": ServiceConfig(
                enabled=True,
                max_retries=3,
                timeout_seconds=45,
                cache_enabled=True,
                search_config={
                    "max_search_results": 500,
                    "search_timeout_seconds": 30
                }
            )
        }
        
        self.middleware_configs = {
            "logging": {"level": "INFO", "detailed": True},
            "cors": {"allow_origins": ["https://staging.example.com"]},
            "rate_limiting": {"enabled": True, "requests_per_minute": 100}
        }
    
    def _setup_production_config(self):
        """生產環境配置"""
        self.service_configs = {
            "staging": ServiceConfig(
                enabled=True,
                max_retries=5,
                timeout_seconds=120,
                cache_enabled=True,
                health_check_interval=120,
                staging_config={
                    "staging_root": "/app/staging",
                    "max_staging_size_gb": 200,
                    "cleanup_interval_hours": 24
                }
            ),
            "processing": ServiceConfig(
                enabled=True,
                max_retries=5,
                timeout_seconds=600,
                cache_enabled=True,
                health_check_interval=120,
                processing_config={
                    "max_concurrent_tasks": 8,
                    "task_timeout_minutes": 120,
                    "temp_dir": "/app/temp"
                }
            ),
            "search": ServiceConfig(
                enabled=True,
                max_retries=3,
                timeout_seconds=60,
                cache_enabled=True,
                search_config={
                    "max_search_results": 1000,
                    "search_timeout_seconds": 45
                }
            )
        }
        
        self.middleware_configs = {
            "logging": {"level": "WARNING", "detailed": False},
            "cors": {"allow_origins": ["https://app.example.com"]},
            "rate_limiting": {"enabled": True, "requests_per_minute": 60}
        }
    
    def get_service_config(self, service_name: str) -> Optional[ServiceConfig]:
        """獲取服務配置"""
        return self.service_configs.get(service_name)
    
    def is_service_enabled(self, service_name: str) -> bool:
        """檢查服務是否啟用"""
        config = self.get_service_config(service_name)
        return config.enabled if config else False
    
    def add_service_override(self, service_name: str, override_func: Callable):
        """添加服務覆蓋（主要用於測試）"""
        self.service_overrides[service_name] = override_func
        logger.info(f"添加服務覆蓋: {service_name}")
    
    def remove_service_override(self, service_name: str):
        """移除服務覆蓋"""
        if service_name in self.service_overrides:
            del self.service_overrides[service_name]
            logger.info(f"移除服務覆蓋: {service_name}")
    
    def get_service_override(self, service_name: str) -> Optional[Callable]:
        """獲取服務覆蓋"""
        return self.service_overrides.get(service_name)
    
    def get_middleware_config(self, middleware_name: str) -> Dict[str, Any]:
        """獲取中介軟體配置"""
        return self.middleware_configs.get(middleware_name, {})


class EnhancedServiceContainer(ServiceContainer):
    """增強的服務容器，支援配置管理"""
    
    def __init__(self, config: DependencyConfig):
        super().__init__()
        self.config = config
        self._service_health_status: Dict[str, Dict[str, Any]] = {}
        
        logger.info("增強服務容器已初始化")
    
    def get_staging_service(self):
        """獲取檔案暫存服務（增強版）"""
        # 檢查配置是否啟用
        if not self.config.is_service_enabled("staging"):
            logger.warning("暫存服務已在配置中禁用")
            return None
        
        # 檢查是否有覆蓋
        override = self.config.get_service_override("staging")
        if override:
            logger.info("使用暫存服務覆蓋")
            return override()
        
        # 使用父類實現，但加入配置
        service = super().get_staging_service()
        if service:
            self._apply_service_config(service, "staging")
        
        return service
    
    def get_processing_service(self):
        """獲取檔案處理服務（增強版）"""
        if not self.config.is_service_enabled("processing"):
            logger.warning("處理服務已在配置中禁用")
            return None
        
        override = self.config.get_service_override("processing")
        if override:
            logger.info("使用處理服務覆蓋")
            return override()
        
        service = super().get_processing_service()
        if service:
            self._apply_service_config(service, "processing")
        
        return service
    
    def get_product_search_service(self):
        """獲取產品搜尋服務（增強版）"""
        if not self.config.is_service_enabled("search"):
            logger.warning("搜尋服務已在配置中禁用")
            return None
        
        override = self.config.get_service_override("search")
        if override:
            logger.info("使用搜尋服務覆蓋")
            return override()
        
        service = super().get_product_search_service()
        if service:
            self._apply_service_config(service, "search")
        
        return service
    
    def _apply_service_config(self, service, service_name: str):
        """將配置應用到服務實例"""
        config = self.config.get_service_config(service_name)
        if not config:
            return
        
        # 應用通用配置
        if hasattr(service, 'set_timeout'):
            service.set_timeout(config.timeout_seconds)
        
        if hasattr(service, 'set_max_retries'):
            service.set_max_retries(config.max_retries)
        
        if hasattr(service, 'enable_cache'):
            service.enable_cache(config.cache_enabled)
        
        # 應用特定配置
        specific_config = getattr(config, f"{service_name}_config", None)
        if specific_config and hasattr(service, 'apply_config'):
            service.apply_config(specific_config)
        
        logger.info(f"已將配置應用到 {service_name} 服務")
    
    def health_check_all_services(self) -> Dict[str, Dict[str, Any]]:
        """健康檢查所有服務"""
        results = {}
        
        # 檢查暫存服務
        staging = self.get_staging_service()
        results["staging"] = self._health_check_service(staging, "staging")
        
        # 檢查處理服務
        processing = self.get_processing_service()
        results["processing"] = self._health_check_service(processing, "processing")
        
        # 檢查搜尋服務
        search = self.get_product_search_service()
        results["search"] = self._health_check_service(search, "search")
        
        return results
    
    def _health_check_service(self, service, service_name: str) -> Dict[str, Any]:
        """健康檢查單個服務"""
        if service is None:
            return {
                "status": "unavailable",
                "enabled": self.config.is_service_enabled(service_name),
                "error": "服務實例為空"
            }
        
        try:
            # 嘗試調用服務的健康檢查方法
            if hasattr(service, 'health_check'):
                health_result = service.health_check()
                return {
                    "status": "healthy",
                    "enabled": True,
                    "details": health_result
                }
            else:
                return {
                    "status": "healthy",
                    "enabled": True,
                    "message": "服務可用但沒有健康檢查方法"
                }
        
        except Exception as e:
            logger.error(f"{service_name} 服務健康檢查失敗: {e}")
            return {
                "status": "unhealthy", 
                "enabled": True,
                "error": str(e)
            }


# 全域配置實例
_dependency_config = None
_enhanced_service_container = None


def get_dependency_config() -> DependencyConfig:
    """獲取依賴注入配置"""
    global _dependency_config
    if _dependency_config is None:
        # 從環境變數決定環境類型
        env_name = os.getenv("APP_ENVIRONMENT", "development").lower()
        try:
            environment = EnvironmentType(env_name)
        except ValueError:
            logger.warning(f"未知環境類型: {env_name}，使用 development")
            environment = EnvironmentType.DEVELOPMENT
        
        _dependency_config = DependencyConfig(environment)
    
    return _dependency_config


def get_enhanced_service_container() -> EnhancedServiceContainer:
    """獲取增強服務容器"""
    global _enhanced_service_container
    if _enhanced_service_container is None:
        config = get_dependency_config()
        _enhanced_service_container = EnhancedServiceContainer(config)
    
    return _enhanced_service_container


def reset_dependency_config():
    """重置依賴注入配置（主要用於測試）"""
    global _dependency_config, _enhanced_service_container
    _dependency_config = None
    _enhanced_service_container = None
    logger.info("依賴注入配置已重置")


# 配置特定的依賴注入函數
def get_configured_staging_service():
    """獲取配置後的暫存服務"""
    container = get_enhanced_service_container()
    return container.get_staging_service()


def get_configured_processing_service():
    """獲取配置後的處理服務"""
    container = get_enhanced_service_container()
    return container.get_processing_service()


def get_configured_search_service():
    """獲取配置後的搜尋服務"""
    container = get_enhanced_service_container()
    return container.get_product_search_service()


# 環境特定的依賴注入設置
def setup_testing_dependencies(app):
    """設置測試環境的依賴注入覆蓋"""
    from .dependencies import get_staging_service, get_processing_service
    
    # 重置配置為測試環境
    reset_dependency_config()
    os.environ["APP_ENVIRONMENT"] = "testing"
    
    # 獲取測試配置
    config = get_dependency_config()
    
    # 設置測試覆蓋
    app.dependency_overrides[get_staging_service] = get_configured_staging_service
    app.dependency_overrides[get_processing_service] = get_configured_processing_service
    
    logger.info("測試環境依賴注入已設置")


def setup_production_dependencies(app):
    """設置生產環境的依賴注入"""
    # 確保環境變數設置正確
    os.environ["APP_ENVIRONMENT"] = "production"
    
    # 獲取生產配置
    config = get_dependency_config()
    
    # 驗證所有必要服務都已啟用
    required_services = ["staging", "processing"]
    for service_name in required_services:
        if not config.is_service_enabled(service_name):
            raise RuntimeError(f"生產環境必需服務未啟用: {service_name}")
    
    logger.info("生產環境依賴注入已設置")


if __name__ == "__main__":
    # 測試配置管理
    config = DependencyConfig(EnvironmentType.DEVELOPMENT)
    print(f"暫存服務啟用: {config.is_service_enabled('staging')}")
    print(f"暫存配置: {config.get_service_config('staging')}")
    
    # 測試增強服務容器
    container = EnhancedServiceContainer(config)
    health_results = container.health_check_all_services()
    print(f"服務健康狀態: {health_results}")