"""檔案暫存服務 - 檔案操作
包含檔案複製、驗證和清理等核心操作
"""

import hashlib
import shutil
import asyncio
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

from loguru import logger
from .models import StagingTask, StagingFileInfo, FileIntegrityError
from .utils import file_lock


class FileOperations:
    """檔案操作類別，包含所有檔案相關的操作方法"""
    
    def __init__(
        self,
        chunk_size: int = 2 * 1024 * 1024,
        max_workers: int = 4,
        enable_file_locking: bool = True,
        enable_progress_batching: bool = True,
        progress_batcher=None,
        lock=None
    ):
        self.chunk_size = chunk_size
        self.max_workers = max_workers
        self.enable_file_locking = enable_file_locking
        self.enable_progress_batching = enable_progress_batching
        self.progress_batcher = progress_batcher
        self._lock = lock or threading.RLock()
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="FileOperations"
        )
    
    async def copy_files(self, task: StagingTask):
        """複製檔案"""
        loop = asyncio.get_event_loop()
        
        # 使用信號量控制並發數
        semaphore = asyncio.Semaphore(self.max_workers)
        
        async def copy_with_semaphore(file_info):
            async with semaphore:
                if task.cancelled:
                    return
                return await loop.run_in_executor(
                    self.executor,
                    self.copy_single_file,
                    file_info,
                    task
                )
        
        # 並行複製檔案
        copy_tasks = [copy_with_semaphore(file_info) for file_info in task.file_infos if not file_info.copied]
        
        if copy_tasks:
            await asyncio.gather(*copy_tasks, return_exceptions=True)
        
        # 檢查是否有複製失敗的檔案
        failed_files = [info.source_path for info in task.file_infos if not info.copied]
        if failed_files:
            logger.warning(f"部分檔案複製失敗: {len(failed_files)} 個檔案")
    
    def copy_single_file(self, file_info: StagingFileInfo, task: StagingTask):
        """複製單個檔案（支援檔案鎖定和進度批次更新）"""
        if task.cancelled:
            return
        
        try:
            logger.debug(f"開始複製檔案: {file_info.source_path} -> {file_info.target_path}")
            
            # 使用檔案鎖定機制
            if self.enable_file_locking:
                with file_lock(file_info.target_path):
                    self.perform_file_copy(file_info, task)
            else:
                self.perform_file_copy(file_info, task)
            
            # 驗證檔案大小
            if file_info.target_path.stat().st_size != file_info.size:
                raise FileIntegrityError(f"檔案大小不匹配: {file_info.target_path}")
            
            file_info.copied = True
            logger.debug(f"檔案複製完成: {file_info.target_path}")
        
        except Exception as e:
            file_info.error_message = str(e)
            logger.error(f"檔案複製失敗: {file_info.source_path} -> {file_info.target_path}, 錯誤: {e}")
    
    def perform_file_copy(self, file_info: StagingFileInfo, task: StagingTask):
        """執行實際的檔案複製"""
        with open(file_info.source_path, 'rb') as src, open(file_info.target_path, 'wb') as dst:
            copied_bytes = 0
            chunk_count = 0
            
            while True:
                if task.cancelled:
                    break
                
                chunk = src.read(self.chunk_size)
                if not chunk:
                    break
                
                dst.write(chunk)
                copied_bytes += len(chunk)
                chunk_count += 1
                
                # 批次更新進度
                if self.enable_progress_batching and self.progress_batcher:
                    self.progress_batcher.add_progress(task.task_id, len(chunk))
                    
                    if self.progress_batcher.should_flush():
                        self.flush_progress_updates(task)
                else:
                    # 直接更新進度
                    with self._lock:
                        task.copied_size += len(chunk)
                        task.progress = min(90.0, (task.copied_size / task.total_size) * 90.0)
                
                # 記錄 I/O 操作數
                task.io_operations += 1
    
    def flush_progress_updates(self, task: StagingTask):
        """刷新進度更新"""
        if not self.progress_batcher:
            return
            
        updates = self.progress_batcher.flush_updates()
        
        if task.task_id in updates:
            bytes_updated = updates[task.task_id]
            with self._lock:
                task.copied_size += bytes_updated
                task.progress = min(90.0, (task.copied_size / task.total_size) * 90.0)
    
    async def verify_file_integrity(self, task: StagingTask):
        """驗證檔案完整性"""
        logger.info(f"開始驗證檔案完整性: {len(task.file_infos)} 個檔案")
        
        loop = asyncio.get_event_loop()
        
        # 使用信號量控制驗證並發數
        semaphore = asyncio.Semaphore(self.max_workers)
        
        async def verify_with_semaphore(file_info):
            async with semaphore:
                if task.cancelled:
                    return
                return await loop.run_in_executor(
                    self.executor,
                    self.verify_single_file,
                    file_info
                )
        
        # 並行驗證檔案
        verify_tasks = [verify_with_semaphore(file_info) for file_info in task.file_infos if file_info.copied]
        
        if verify_tasks:
            await asyncio.gather(*verify_tasks, return_exceptions=True)
        
        # 檢查驗證結果
        failed_verifications = [info for info in task.file_infos if info.copied and not info.verified]
        if failed_verifications:
            raise FileIntegrityError(f"檔案完整性驗證失敗: {len(failed_verifications)} 個檔案")
        
        task.progress = 100.0
        logger.info("檔案完整性驗證完成")
    
    def verify_single_file(self, file_info: StagingFileInfo):
        """驗證單個檔案的完整性"""
        try:
            # 計算來源檔案的 MD5 校驗和
            source_checksum = self.calculate_file_checksum(file_info.source_path)
            
            # 計算目標檔案的 MD5 校驗和
            target_checksum = self.calculate_file_checksum(file_info.target_path)
            
            if source_checksum == target_checksum:
                file_info.checksum = source_checksum
                file_info.verified = True
                logger.debug(f"檔案完整性驗證通過: {file_info.target_path}")
            else:
                file_info.error_message = "校驗和不匹配"
                logger.error(f"檔案完整性驗證失敗: {file_info.target_path}")
        
        except Exception as e:
            file_info.error_message = f"驗證錯誤: {e}"
            logger.error(f"檔案完整性驗證異常: {file_info.target_path}, 錯誤: {e}")
    
    def calculate_file_checksum(self, file_path: Path) -> str:
        """計算檔案的 MD5 校驗和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(self.chunk_size), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def cleanup_failed_staging(self, task: StagingTask):
        """清理失敗的暫存檔案"""
        try:
            if task.staging_directory.exists():
                logger.info(f"清理失敗的暫存目錄: {task.staging_directory}")
                shutil.rmtree(task.staging_directory)
        except Exception as e:
            logger.error(f"清理失敗的暫存目錄時發生錯誤: {e}")
    
    async def cleanup_staging_directory(self, staging_directory: Path) -> bool:
        """清理暫存目錄"""
        try:
            if staging_directory.exists():
                logger.info(f"清理暫存目錄: {staging_directory}")
                shutil.rmtree(staging_directory)
                return True
            return True
        except Exception as e:
            logger.error(f"清理暫存目錄失敗: {e}")
            return False
    
    def __del__(self):
        """清理資源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
