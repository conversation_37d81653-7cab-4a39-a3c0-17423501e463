# 設計文件

## 概述

智慧型網路檔案瀏覽系統是一個基於現有網路瀏覽器的增強型檔案管理解決方案。系統採用六角架構模式，整合 LLM 智慧搜尋、自動化檔案處理，以及與現有工具的無縫整合。

## 架構

### 系統架構圖

```mermaid
graph TB
    subgraph "展示層 (Presentation Layer)"
        UI[網路瀏覽器 UI<br/>http://localhost:5555/network/ui]
        API[REST API 端點]
        WS[WebSocket 端點]
    end
    
    subgraph "應用層 (Application Layer)"
        PSS[ProductSearchService<br/>產品搜尋服務]
        LSS[LLMSearchService<br/>LLM 智慧搜尋服務]
        FPS[FileProcessingService<br/>檔案處理服務]
        AS[驗證服務]
    end
    
    subgraph "任務佇列層 (Task Queue Layer)"
        CELERY[Celery Worker]
        REDIS[Redis Broker]
        BG_TASKS[背景任務<br/>search_product_task<br/>run_csv_summary_task<br/>run_code_comparison_task]
    end
    
    subgraph "領域層 (Domain Layer)"
        FE[FileInfo<br/>檔案實體]
        PSR[ProductSearchResult<br/>搜尋結果實體]
        PT[ProcessingTask<br/>處理任務實體]
        SF[SearchFilters<br/>搜尋篩選實體]
        TR[TimeRange<br/>時間範圍實體]
    end
    
    subgraph "基礎設施層 (Infrastructure Layer)"
        ULC[UnifiedLLMClient<br/>統一 LLM 客戶端]
        NFS[網路檔案系統適配器]
        AUTH[網路驗證適配器]
    end
    
    subgraph "外部系統"
        NET[\\************\test_log]
        TEMP[d:\temp\]
        UPLOAD[\\************\temp_7days\]
        ENV[.env 憑證]
        CSVTOOL[csv_to_summary.py]
        CCTOOL[code_comparison.py]
        OLLAMA[Ollama LLM 服務]
    end
    
    UI --> API
    UI --> WS
    API --> PSS
    API --> LSS
    API --> FPS
    API --> AS
    
    PSS --> CELERY
    FPS --> CELERY
    LSS --> PSS
    
    CELERY --> BG_TASKS
    CELERY --> REDIS
    
    PSS --> FE
    PSS --> PSR
    PSS --> SF
    PSS --> TR
    FPS --> PT
    LSS --> ULC
    
    PSS --> NFS
    FPS --> NFS
    AS --> AUTH
    ULC --> OLLAMA
    
    NFS --> NET
    NFS --> UPLOAD
    BG_TASKS --> TEMP
    BG_TASKS --> CSVTOOL
    BG_TASKS --> CCTOOL
    AUTH --> ENV
    
    WS -.-> CELERY
    WS -.-> "即時狀態更新"
```

### 技術堆疊

- **後端框架**: FastAPI (擴展現有的 8010 埠服務)
- **前端**: HTML/JavaScript (擴展現有 UI)
- **檔案系統**: Python pathlib + os.walk()
- **LLM 整合**: Ollama 本地 LLM
- **網路存取**: SMB/CIFS 協定
- **並行處理**: asyncio + concurrent.futures
- **驗證**: Windows 網路驗證

## 元件和介面

### 1. 產品搜尋服務 (ProductSearchService)

```python
class ProductSearchService:
    async def search_product_folder(
        self, 
        product_name: str, 
        base_path: Path,
        time_range: TimeRange,
        filters: Optional[SearchFilters] = None
    ) -> ProductSearchResult
    
    async def create_search_task(
        self,
        product_name: str,
        base_path: Path,
        filters: SearchFilters
    ) -> str
    
    async def execute_search_task(self, task_id: str) -> Optional[ProductSearchResult]
    
    def create_time_range(self, time_range_type: TimeRangeType) -> TimeRange
```

**職責:**
- 產品資料夾定位和搜尋
- 並行檔案系統掃描
- 時間範圍篩選
- 搜尋任務管理
- 支援多種搜尋目錄選擇（自動、全部、特定廠商）

### 2. LLM 智慧搜尋服務 (LLMSearchService)

```python
class LLMSearchService:
    async def interpret_natural_query(self, query: str) -> Dict[str, Any]
    
    async def smart_search(
        self, 
        query: str, 
        base_path: Path,
        max_results: int = 100
    ) -> Dict[str, Any]
    
    async def analyze_search_results(
        self, 
        results: List[ProductSearchResult], 
        original_query: str
    ) -> Dict[str, Any]
    
    async def generate_action_suggestions(
        self, 
        results: List[ProductSearchResult],
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]
```

**職責:**
- 自然語言查詢解析
- 智慧搜尋執行
- 搜尋結果分析
- 行動建議生成
- LLM 後備機制（規則式解析）

### 3. 檔案處理服務 (FileProcessingService)

```python
class FileProcessingService:
    def create_task(self, tool: ProcessingTool, input_path: str) -> str
    
    async def execute_csv_summary(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult
    
    async def execute_code_comparison(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult
    
    async def get_task_progress(self, task_id: str) -> Dict[str, Any]
    
    def list_tasks(self) -> List[Dict[str, Any]]
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24)
```

**職責:**
- 整合 csv_to_summary.py 和 code_comparison.py 工具
- 異步任務執行和進度追蹤
- 處理結果管理
- 輸出檔案自動偵測
- 任務生命週期管理

### 4. 背景任務系統 (Celery Tasks)

```python
# 在 tasks.py 中定義的 Celery 任務
@celery_app.task(bind=True)
def search_product_task(self, product_name: str, base_path: str, filters: dict)

@celery_app.task(bind=True)  
def run_csv_summary_task(self, input_path: str)

@celery_app.task(bind=True)
def run_code_comparison_task(self, input_path: str)

@celery_app.task
def health_check_task()
```

**職責:**
- 長時間執行任務的背景處理
- 任務進度追蹤和狀態更新
- 任務結果持久化
- 系統健康檢查

### 5. WebSocket 端點 (WebSocketEndpoints)

```python
# WebSocket 連線管理
@websocket_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket)

# 即時狀態更新
async def broadcast_task_update(task_id: str, status: dict)
async def broadcast_system_status(status: dict)
```

**職責:**
- 即時任務狀態推送
- 多客戶端連線管理
- 系統狀態廣播
- 連線恢復機制

### 4. 網路檔案系統適配器 (NetworkFileSystemAdapter)

```python
class NetworkFileSystemAdapter:
    async def authenticate(
        self, 
        credentials: NetworkCredentials
    ) -> AuthSession
    
    async def list_directory(
        self, 
        path: Path, 
        recursive: bool = False
    ) -> List[FileInfo]
    
    async def copy_files(
        self, 
        source_files: List[Path], 
        destination: Path
    ) -> CopyResult
    
    async def upload_files(
        self, 
        local_files: List[Path], 
        remote_destination: Path
    ) -> UploadResult
```

**職責:**
- 網路檔案系統存取
- 檔案複製和上傳
- 網路驗證管理

## 資料模型

### 核心實體

```python
@dataclass
class FileInfo:
    path: Path
    name: str
    size: int
    modified_time: datetime
    file_type: str
    is_directory: bool

@dataclass
class SearchResult:
    product_folder: Path
    matched_files: List[FileInfo]
    total_files: int
    search_duration: float
    filters_applied: SearchFilters

@dataclass
class ProcessingResult:
    success: bool
    output_files: List[Path]
    processing_time: float
    tool_used: str
    error_message: Optional[str]

@dataclass
class TimeRange:
    start_date: datetime
    end_date: datetime
    
    @classmethod
    def last_months(cls, months: int) -> 'TimeRange':
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        return cls(start_date, end_date)
```

### 配置模型

```python
@dataclass
class NetworkConfig:
    base_path: str = "\\\\************\\test_log"
    upload_path: str = "\\\\************\\temp_7days"
    staging_path: str = "d:\\temp"
    username: str
    password: str
    
    @classmethod
    def from_env(cls) -> 'NetworkConfig':
        return cls(
            username=os.getenv('EMAIL_ADDRESS'),
            password=os.getenv('EMAIL_PASSWORD')
        )
```

## 錯誤處理

### 錯誤類型層次

```python
class NetworkFileSystemError(Exception):
    """網路檔案系統相關錯誤基類"""
    pass

class AuthenticationError(NetworkFileSystemError):
    """網路驗證失敗"""
    pass

class FileAccessError(NetworkFileSystemError):
    """檔案存取權限錯誤"""
    pass

class SearchTimeoutError(NetworkFileSystemError):
    """搜尋操作逾時"""
    pass

class ProcessingError(Exception):
    """檔案處理錯誤基類"""
    pass

class StagingError(ProcessingError):
    """檔案暫存錯誤"""
    pass

class UploadError(ProcessingError):
    """檔案上傳錯誤"""
    pass
```

### 錯誤處理策略

1. **網路連線錯誤**: 自動重試機制（最多 3 次）
2. **驗證失敗**: 記錄錯誤並提示使用者檢查憑證
3. **檔案存取錯誤**: 提供詳細的權限錯誤訊息
4. **處理逾時**: 允許使用者取消長時間執行的操作
5. **部分失敗**: 提供詳細的成功/失敗報告

## 測試策略

### 測試層級

1. **單元測試**
   - 各服務類別的獨立功能測試
   - 資料模型驗證測試
   - 錯誤處理邏輯測試

2. **整合測試**
   - 網路檔案系統存取測試
   - LLM 服務整合測試
   - 工具執行整合測試

3. **端對端測試**
   - 完整搜尋到處理工作流程測試
   - UI 互動測試
   - 效能測試

### 測試資料

```python
# 測試用的模擬網路結構
TEST_NETWORK_STRUCTURE = {
    "test_log": {
        "AAA": {
            "2024-01": ["file1.csv", "file2.xlsx"],
            "2024-02": ["file3.csv", "file4.xlsx"],
        },
        "BBB": {
            "2024-01": ["file5.csv"],
        }
    }
}
```

### 效能測試目標

- **搜尋回應時間**: < 5 秒（1000 個檔案）
- **檔案複製速度**: > 10 MB/s
- **並行搜尋**: 支援最多 10 個同時搜尋請求
- **記憶體使用**: < 500 MB（正常操作）

## 安全性考量

### 憑證管理

1. **環境變數儲存**: 敏感憑證儲存在 .env 檔案
2. **記憶體保護**: 憑證不在日誌中記錄
3. **會話管理**: 自動處理網路會話過期
4. **存取控制**: 僅允許授權的網路路徑存取

### 檔案安全

1. **路徑驗證**: 防止路徑遍歷攻擊
2. **檔案類型檢查**: 限制允許的檔案類型
3. **大小限制**: 防止過大檔案的處理
4. **暫存清理**: 自動清理暫存檔案

## 效能最佳化

### 搜尋最佳化

1. **並行掃描**: 使用 asyncio 並行處理多個目錄
2. **快取機制**: 快取最近搜尋的目錄結構
3. **索引建立**: 為常用產品建立檔案索引
4. **分頁載入**: 大量結果分頁顯示

### 網路最佳化

1. **連線池**: 重用網路連線
2. **批次操作**: 批次處理檔案操作
3. **壓縮傳輸**: 大檔案傳輸時使用壓縮
4. **斷點續傳**: 支援大檔案的斷點續傳

## 部署考量

### 系統需求

- **作業系統**: Windows 10/11 或 Windows Server
- **Python 版本**: >= 3.9
- **記憶體**: 最少 4GB RAM
- **網路**: 穩定的區域網路連線
- **磁碟空間**: 最少 10GB 可用空間（暫存用）

### 配置檔案

```env
# .env 檔案範例
EMAIL_ADDRESS=telowyield1
EMAIL_PASSWORD=GMTgmt88TE
NETWORK_BASE_PATH=\\************\test_log
NETWORK_UPLOAD_PATH=\\************\temp_7days
LOCAL_STAGING_PATH=d:\temp
LLM_MODEL=llama2
SEARCH_TIMEOUT=300
MAX_CONCURRENT_SEARCHES=10
```

### 監控和日誌

1. **操作日誌**: 記錄所有檔案操作
2. **效能監控**: 追蹤搜尋和處理時間
3. **錯誤追蹤**: 詳細的錯誤堆疊追蹤
4. **使用統計**: 追蹤功能使用頻率