"""錯誤恢復機制

提供重試和熔斷器等錯誤恢復策略
"""

import time
from typing import Callable, Any
from loguru import logger


def with_retry(
    func: Callable,
    max_retries: int = 3,
    backoff_factor: float = 1.5,
    exceptions: tuple = (Exception,)
) -> Any:
    """帶重試機制的函數執行器"""
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return func()
        except exceptions as e:
            last_exception = e
            if attempt < max_retries:
                wait_time = backoff_factor ** attempt
                time.sleep(wait_time)
                logger.warning(f"重試第 {attempt + 1} 次，等待 {wait_time:.2f} 秒: {e}")
                continue
            else:
                logger.error(f"重試 {max_retries} 次後仍然失敗: {e}")
                raise last_exception


class CircuitBreaker:
    """熔斷器模式實現"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """通過熔斷器調用函數"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time < self.recovery_timeout:
                raise Exception("熔斷器開啟，服務暫時不可用")
            else:
                self.state = "HALF_OPEN"
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _on_success(self):
        """成功時重置熔斷器"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """失敗時更新熔斷器狀態"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"熔斷器開啟，失敗次數：{self.failure_count}")
