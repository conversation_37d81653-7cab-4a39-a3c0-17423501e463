"""錯誤處理工具函數

提供錯誤響應格式化和日誌記錄功能
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Set
from loguru import logger


def create_unified_error_response(
    error_code: str,
    message: str,
    status_code: int,
    details: Optional[str] = None,
    path: Optional[str] = None,
    trace_id: Optional[str] = None
) -> Dict[str, Any]:
    """創建統一的錯誤響應格式"""
    return {
        "success": False,
        "error": {
            "code": error_code,
            "message": message,
            "details": details or "",
            "timestamp": datetime.now().isoformat() + "Z",
            "trace_id": trace_id or str(uuid.uuid4()),
            "path": path or ""
        },
        "status_code": status_code
    }


class ErrorLogger:
    """統一錯誤日誌記錄器"""
    
    SENSITIVE_FIELDS: Set[str] = {
        "password", "passwd", "pwd", "secret", "token", "key", "api_key",
        "access_token", "refresh_token", "auth", "authorization",
        "credit_card", "card_number", "cvv", "ssn", "social_security"
    }
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """記錄錯誤日誌"""
        filtered_context = self.filter_sensitive_data(context or {})
        
        logger.error(
            f"API 錯誤: {str(error)}",
            extra={
                "error_type": type(error).__name__,
                "context": filtered_context,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """過濾敏感數據"""
        filtered = {}
        
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in self.SENSITIVE_FIELDS):
                filtered[key] = "***"
            else:
                filtered[key] = value
        
        return filtered
