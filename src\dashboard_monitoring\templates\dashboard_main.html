<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>統一監控儀表板 - 半導體郵件處理系統</title>
    <link rel="stylesheet" href="/dashboard/static/css/dashboard_main.css">
    <link rel="stylesheet" href="/dashboard/static/css/dashboard_components.css">
    <link rel="icon" type="image/x-icon" href="/dashboard/static/images/dashboard_icons/favicon.ico">
</head>
<body>
    <!-- 頂部導航欄 -->
    <header class="dashboard-header">
        <div class="header-container">
            <div class="header-left">
                <h1 class="dashboard-title">
                    <span class="title-icon">📊</span>
                    統一監控儀表板
                </h1>
                <div class="system-status" id="systemStatus">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">連接中...</span>
                </div>
            </div>
            <div class="header-right">
                <div class="last-update" id="lastUpdate">
                    最後更新: --
                </div>
                <div class="connection-status" id="connectionStatus">
                    <span class="connection-indicator" id="connectionIndicator"></span>
                    <span class="connection-text">WebSocket</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 告警通知區域 -->
    <div class="alert-banner" id="alertBanner" style="display: none;">
        <div class="alert-container">
            <div class="alert-content" id="alertContent"></div>
            <button class="alert-close" id="alertClose">&times;</button>
        </div>
    </div>

    <!-- 主要內容區域 -->
    <main class="dashboard-main">
        <div class="dashboard-container">
            
            <!-- 概覽統計卡片 -->
            <section class="overview-section">
                <div class="overview-cards">
                    <div class="overview-card email-overview">
                        <div class="card-header">
                            <h3>📧 郵件處理</h3>
                            <div class="card-status" id="emailStatus">正常</div>
                        </div>
                        <div class="card-content">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <span class="metric-label">待處理</span>
                                    <span class="metric-value" id="emailPending">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">處理中</span>
                                    <span class="metric-value" id="emailProcessing">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">已完成</span>
                                    <span class="metric-value" id="emailCompleted">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">失敗</span>
                                    <span class="metric-value error" id="emailFailed">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overview-card celery-overview">
                        <div class="card-header">
                            <h3>🔄 Celery 任務</h3>
                            <div class="card-status" id="celeryStatus">正常</div>
                        </div>
                        <div class="card-content">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <span class="metric-label">活躍</span>
                                    <span class="metric-value" id="celeryActive">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">待處理</span>
                                    <span class="metric-value" id="celeryPending">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">工作者</span>
                                    <span class="metric-value" id="celeryWorkers">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">成功率</span>
                                    <span class="metric-value" id="celerySuccessRate">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overview-card system-overview">
                        <div class="card-header">
                            <h3>💻 系統資源</h3>
                            <div class="card-status" id="systemResourceStatus">正常</div>
                        </div>
                        <div class="card-content">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <span class="metric-label">CPU</span>
                                    <span class="metric-value" id="systemCpu">0%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">記憶體</span>
                                    <span class="metric-value" id="systemMemory">0%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">磁碟</span>
                                    <span class="metric-value" id="systemDisk">0%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">連接數</span>
                                    <span class="metric-value" id="systemConnections">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overview-card business-overview">
                        <div class="card-header">
                            <h3>📊 業務指標</h3>
                            <div class="card-status" id="businessStatus">正常</div>
                        </div>
                        <div class="card-content">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <span class="metric-label">今日 MO</span>
                                    <span class="metric-value" id="businessMO">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">今日 LOT</span>
                                    <span class="metric-value" id="businessLOT">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">品質分數</span>
                                    <span class="metric-value" id="businessQuality">0</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">報告生成</span>
                                    <span class="metric-value" id="businessReports">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 詳細監控區域 -->
            <section class="monitoring-section">
                <div class="monitoring-grid">
                    
                    <!-- 郵件處理詳細監控 -->
                    <div class="monitoring-panel email-panel">
                        <div class="panel-header">
                            <h3>📧 郵件處理監控</h3>
                            <div class="panel-controls">
                                <button class="refresh-btn" onclick="refreshEmailData()">🔄</button>
                                <button class="expand-btn" onclick="expandPanel('email')">⛶</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="vendor-stats" id="vendorStats">
                                <h4>廠商分組統計</h4>
                                <div class="vendor-grid" id="vendorGrid">
                                    <!-- 動態生成廠商統計 -->
                                </div>
                            </div>
                            <div class="code-comparison-stats" id="codeComparisonStats">
                                <h4>Code Comparison 任務</h4>
                                <div class="comparison-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">活躍任務</span>
                                        <span class="metric-value" id="codeComparisonActive">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">待處理</span>
                                        <span class="metric-value" id="codeComparisonPending">0</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">平均時間</span>
                                        <span class="metric-value" id="codeComparisonAvgTime">0s</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Celery 任務詳細監控 -->
                    <div class="monitoring-panel celery-panel">
                        <div class="panel-header">
                            <h3>🔄 Celery 任務監控</h3>
                            <div class="panel-controls">
                                <button class="refresh-btn" onclick="refreshCeleryData()">🔄</button>
                                <button class="expand-btn" onclick="expandPanel('celery')">⛶</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="task-type-stats" id="taskTypeStats">
                                <h4>任務類型統計</h4>
                                <div class="task-type-grid" id="taskTypeGrid">
                                    <!-- 動態生成任務類型統計 -->
                                </div>
                            </div>
                            <div class="worker-stats" id="workerStats">
                                <h4>工作者狀態</h4>
                                <div class="worker-grid" id="workerGrid">
                                    <!-- 動態生成工作者狀態 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系統資源詳細監控 -->
                    <div class="monitoring-panel system-panel">
                        <div class="panel-header">
                            <h3>💻 系統資源監控</h3>
                            <div class="panel-controls">
                                <button class="refresh-btn" onclick="refreshSystemData()">🔄</button>
                                <button class="expand-btn" onclick="expandPanel('system')">⛶</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="resource-charts" id="resourceCharts">
                                <div class="chart-container">
                                    <h4>CPU 使用率</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="cpuProgress"></div>
                                        <span class="progress-text" id="cpuText">0%</span>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <h4>記憶體使用率</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="memoryProgress"></div>
                                        <span class="progress-text" id="memoryText">0%</span>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <h4>磁碟使用率</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="diskProgress"></div>
                                        <span class="progress-text" id="diskText">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="service-health" id="serviceHealth">
                                <h4>服務健康狀態</h4>
                                <div class="service-grid" id="serviceGrid">
                                    <!-- 動態生成服務狀態 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 檔案處理監控 -->
                    <div class="monitoring-panel file-panel">
                        <div class="panel-header">
                            <h3>📁 檔案處理監控</h3>
                            <div class="panel-controls">
                                <button class="refresh-btn" onclick="refreshFileData()">🔄</button>
                                <button class="expand-btn" onclick="expandPanel('file')">⛶</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="file-stats" id="fileStats">
                                <div class="file-type-stats">
                                    <h4>檔案類型統計</h4>
                                    <div class="file-type-grid" id="fileTypeGrid">
                                        <!-- 動態生成檔案類型統計 -->
                                    </div>
                                </div>
                                <div class="storage-stats">
                                    <h4>儲存空間使用</h4>
                                    <div class="storage-grid" id="storageGrid">
                                        <!-- 動態生成儲存空間統計 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 告警管理面板 -->
                    <div class="monitoring-panel alert-panel">
                        <div class="panel-header">
                            <h3>🚨 告警管理</h3>
                            <div class="panel-controls">
                                <button class="refresh-btn" onclick="refreshAlertData()">🔄</button>
                                <button class="clear-all-btn" onclick="clearAllAlerts()">清除全部</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div class="alert-summary" id="alertSummary">
                                <div class="alert-count-grid">
                                    <div class="alert-count-item critical">
                                        <span class="count" id="criticalAlertCount">0</span>
                                        <span class="label">嚴重</span>
                                    </div>
                                    <div class="alert-count-item error">
                                        <span class="count" id="errorAlertCount">0</span>
                                        <span class="label">錯誤</span>
                                    </div>
                                    <div class="alert-count-item warning">
                                        <span class="count" id="warningAlertCount">0</span>
                                        <span class="label">警告</span>
                                    </div>
                                    <div class="alert-count-item info">
                                        <span class="count" id="infoAlertCount">0</span>
                                        <span class="label">資訊</span>
                                    </div>
                                </div>
                            </div>
                            <div class="alert-list" id="alertList">
                                <!-- 動態生成告警列表 -->
                            </div>
                        </div>
                    </div>

                </div>
            </section>
        </div>
    </main>

    <!-- 載入中遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">載入監控資料中...</div>
    </div>

    <!-- JavaScript 檔案 -->
    <script src="/dashboard/static/js/dashboard_websocket.js"></script>
    <script src="/dashboard/static/js/dashboard_charts.js"></script>
    <script src="/dashboard/static/js/dashboard_main.js"></script>
</body>
</html>