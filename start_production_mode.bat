@echo off
REM ========================================================================
REM 生產環境 Celery 啟動器 (Windows 批次檔)
REM 僅在需要真正分散式任務處理時使用
REM ========================================================================
REM
REM 📋 使用場景：
REM   ✅ Windows 環境快速啟動生產模式
REM   ✅ 需要真正的異步處理和任務持久化
REM   ✅ 高負載場景 - 大量並發任務
REM   ❌ 開發和測試 - 當前的內存模式已足夠
REM
REM 🔧 功能說明：
REM   1. 🔴 檢查 Redis 服務是否運行
REM   2. 👷 在新視窗啟動 Celery Worker
REM   3. 🌸 在新視窗啟動 Flower 監控
REM   4. 🚀 啟動主應用服務
REM
REM ⚠️  前置需求：
REM   - 需要先安裝並啟動 Redis 服務器
REM   - 確保 Python 環境已正確配置
REM   - 確保所有依賴套件已安裝
REM
REM 🎯 替代方案：
REM   - 開發模式：python start_integrated_services.py
REM   - 跨平台啟動：python start_production_celery.py
REM
REM ========================================================================

echo ========================================
echo 生產環境 Celery 啟動器
echo ========================================
echo.

REM 設置生產模式環境變數
set USE_MEMORY_BROKER=false
set CELERY_BROKER_URL=redis://localhost:6379/0
set CELERY_RESULT_BACKEND=redis://localhost:6379/0

echo 🔧 已設置生產模式環境變數
echo.

REM 檢查 Redis 是否運行
echo 🔍 檢查 Redis 服務...
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Redis 未運行，請先啟動 Redis 服務
    echo 💡 可以使用: redis-server
    pause
    exit /b 1
)
echo ✅ Redis 服務正常
echo.

REM 在新視窗啟動 Celery Worker
echo 👷 啟動 Celery Worker...
start "Celery Worker" cmd /k "python -m celery -A src.tasks.celery_app worker --loglevel=info --concurrency=4"

REM 等待 Worker 啟動
timeout /t 5 /nobreak >nul

REM 在新視窗啟動 Flower 監控
echo 🌸 啟動 Flower 監控...
start "Flower Monitor" cmd /k "python -m celery -A src.tasks.celery_app flower --port=5566"

REM 等待 Flower 啟動
timeout /t 3 /nobreak >nul

REM 啟動主應用
echo 🚀 啟動主應用...
echo.
echo ========================================
echo 🎉 生產環境啟動完成！
echo ========================================
echo 📊 服務端點:
echo   🔴 Redis:        localhost:6379
echo   🌸 Flower 監控:  http://localhost:5566
echo   🚀 主應用:       http://localhost:5555
echo   📱 網路瀏覽器:    http://localhost:5555/network/ui
echo ========================================
echo.

python start_integrated_services.py
